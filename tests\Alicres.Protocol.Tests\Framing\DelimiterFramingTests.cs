using Alicres.Protocol.Framing;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Alicres.Protocol.Tests.Framing;

/// <summary>
/// 分隔符帧处理器测试
/// </summary>
public class DelimiterFramingTests : IDisposable
{
    private readonly Mock<ILogger> _mockLogger;
    private readonly DelimiterFraming _framing;
    private const byte TestDelimiter = 0x0A; // 换行符

    public DelimiterFramingTests()
    {
        _mockLogger = new Mock<ILogger>();
        _framing = new DelimiterFraming(TestDelimiter, false, _mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeCorrectly()
    {
        // Assert
        _framing.FramingName.Should().Be("DelimiterFraming");
        _framing.Mode.Should().Be(Alicres.Protocol.Interfaces.FramingMode.Delimiter);
        _framing.Delimiter.Should().Be(TestDelimiter);
        _framing.IncludeDelimiter.Should().BeFalse();
        _framing.IsEnabled.Should().BeTrue();
    }

    [Fact]
    public void HasCompleteFrame_WithDelimiter_ShouldReturnTrue()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x02, TestDelimiter, 0x03 };

        // Act
        var result = _framing.HasCompleteFrame(data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void HasCompleteFrame_WithoutDelimiter_ShouldReturnFalse()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x02, 0x03 };

        // Act
        var result = _framing.HasCompleteFrame(data);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void HasCompleteFrame_WithEmptyData_ShouldReturnFalse()
    {
        // Arrange
        var data = Array.Empty<byte>();

        // Act
        var result = _framing.HasCompleteFrame(data);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void GetExpectedFrameLength_WithDelimiter_ShouldReturnCorrectLength()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x02, TestDelimiter, 0x03 };

        // Act
        var result = _framing.GetExpectedFrameLength(data);

        // Assert
        result.Should().Be(2); // 不包含分隔符时，长度为分隔符位置
    }

    [Fact]
    public void GetExpectedFrameLength_WithIncludeDelimiter_ShouldReturnCorrectLength()
    {
        // Arrange
        var framingWithDelimiter = new DelimiterFraming(TestDelimiter, true, _mockLogger.Object);
        var data = new byte[] { 0x01, 0x02, TestDelimiter, 0x03 };

        // Act
        var result = framingWithDelimiter.GetExpectedFrameLength(data);

        // Assert
        result.Should().Be(3); // 包含分隔符时，长度为分隔符位置+1
    }

    [Fact]
    public void FrameMessage_ShouldAddDelimiter()
    {
        // Arrange
        var message = new byte[] { 0x01, 0x02, 0x03 };

        // Act
        var result = _framing.FrameMessage(message);

        // Assert
        result.Should().HaveCount(4);
        result.Take(3).Should().BeEquivalentTo(message);
        result[3].Should().Be(TestDelimiter);
    }

    [Fact]
    public void UnframeMessage_WithoutIncludeDelimiter_ShouldRemoveDelimiter()
    {
        // Arrange
        var framedData = new byte[] { 0x01, 0x02, 0x03, TestDelimiter };

        // Act
        var result = _framing.UnframeMessage(framedData);

        // Assert
        result.Should().HaveCount(3);
        result.Should().BeEquivalentTo(new byte[] { 0x01, 0x02, 0x03 });
    }

    [Fact]
    public void UnframeMessage_WithIncludeDelimiter_ShouldKeepDelimiter()
    {
        // Arrange
        var framingWithDelimiter = new DelimiterFraming(TestDelimiter, true, _mockLogger.Object);
        var framedData = new byte[] { 0x01, 0x02, 0x03, TestDelimiter };

        // Act
        var result = framingWithDelimiter.UnframeMessage(framedData);

        // Assert
        result.Should().HaveCount(4);
        result.Should().BeEquivalentTo(framedData);
    }

    [Fact]
    public void ProcessIncomingData_WithSingleFrame_ShouldExtractFrame()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x02, 0x03, TestDelimiter };

        // Act
        var result = _framing.ProcessIncomingData(data);

        // Assert
        result.Should().HaveCount(1);
        result[0].Should().BeEquivalentTo(new byte[] { 0x01, 0x02, 0x03 });
    }

    [Fact]
    public void ProcessIncomingData_WithMultipleFrames_ShouldExtractAllFrames()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x02, TestDelimiter, 0x03, 0x04, TestDelimiter };

        // Act
        var result = _framing.ProcessIncomingData(data);

        // Assert
        result.Should().HaveCount(2);
        result[0].Should().BeEquivalentTo(new byte[] { 0x01, 0x02 });
        result[1].Should().BeEquivalentTo(new byte[] { 0x03, 0x04 });
    }

    [Fact]
    public void ProcessIncomingData_WithIncompleteFrame_ShouldReturnEmpty()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x02, 0x03 }; // 没有分隔符

        // Act
        var result = _framing.ProcessIncomingData(data);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public void ProcessIncomingData_WithPartialThenCompleteFrame_ShouldExtractCompleteFrame()
    {
        // Arrange
        var partialData = new byte[] { 0x01, 0x02 };
        var completeData = new byte[] { 0x03, TestDelimiter };

        // Act
        var result1 = _framing.ProcessIncomingData(partialData);
        var result2 = _framing.ProcessIncomingData(completeData);

        // Assert
        result1.Should().BeEmpty();
        result2.Should().HaveCount(1);
        result2[0].Should().BeEquivalentTo(new byte[] { 0x01, 0x02, 0x03 });
    }

    [Fact]
    public void ProcessIncomingData_WhenDisabled_ShouldReturnEmpty()
    {
        // Arrange
        _framing.IsEnabled = false;
        var data = new byte[] { 0x01, 0x02, TestDelimiter };

        // Act
        var result = _framing.ProcessIncomingData(data);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public void ProcessIncomingData_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        var action = () => _framing.ProcessIncomingData(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void Reset_ShouldClearBuffer()
    {
        // Arrange
        var partialData = new byte[] { 0x01, 0x02 };
        _framing.ProcessIncomingData(partialData);

        // Act
        _framing.Reset();
        var completeData = new byte[] { 0x03, TestDelimiter };
        var result = _framing.ProcessIncomingData(completeData);

        // Assert
        result.Should().HaveCount(1);
        result[0].Should().BeEquivalentTo(new byte[] { 0x03 }); // 只有新数据，之前的被清除了
    }

    [Fact]
    public void ProcessIncomingData_WithMaxFrameLengthExceeded_ShouldHandleGracefully()
    {
        // Arrange
        _framing.MaxFrameLength = 5;
        var longData = new byte[10]; // 超过最大长度且没有分隔符
        for (int i = 0; i < longData.Length; i++)
        {
            longData[i] = (byte)(i + 1);
        }

        // Act
        var result = _framing.ProcessIncomingData(longData);

        // Assert
        result.Should().HaveCount(1); // 应该强制处理以避免无限增长
        result[0].Should().HaveCount(5); // 应该被截断到最大长度
    }

    public void Dispose()
    {
        // 清理资源
    }
}
