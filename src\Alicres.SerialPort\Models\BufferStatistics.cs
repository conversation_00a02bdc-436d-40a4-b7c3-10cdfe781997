namespace Alicres.SerialPort.Models;

/// <summary>
/// 缓冲区统计信息
/// </summary>
public class BufferStatistics
{
    /// <summary>
    /// 当前队列长度
    /// </summary>
    public int QueueLength { get; set; }

    /// <summary>
    /// 队列使用率（百分比）
    /// </summary>
    public int QueueUsagePercentage { get; set; }

    /// <summary>
    /// 最大队列长度
    /// </summary>
    public int MaxQueueLength { get; set; }

    /// <summary>
    /// 总接收字节数
    /// </summary>
    public long TotalBytesReceived { get; set; }

    /// <summary>
    /// 总丢弃字节数
    /// </summary>
    public long TotalBytesDropped { get; set; }

    /// <summary>
    /// 数据丢失率（百分比）
    /// </summary>
    public double DataLossRate => TotalBytesReceived > 0 ? (double)TotalBytesDropped / TotalBytesReceived * 100 : 0;

    /// <summary>
    /// 最后清理时间
    /// </summary>
    public DateTime LastCleanupTime { get; set; }

    /// <summary>
    /// 溢出处理策略
    /// </summary>
    public BufferOverflowStrategy OverflowStrategy { get; set; }

    /// <summary>
    /// 统计信息生成时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>统计信息的字符串描述</returns>
    public override string ToString()
    {
        return $"缓冲区统计 [{Timestamp:HH:mm:ss.fff}]: " +
               $"队列 {QueueLength}/{MaxQueueLength} ({QueueUsagePercentage}%), " +
               $"接收 {TotalBytesReceived} 字节, " +
               $"丢弃 {TotalBytesDropped} 字节 ({DataLossRate:F2}%), " +
               $"策略: {OverflowStrategy}";
    }

    /// <summary>
    /// 获取详细报告
    /// </summary>
    /// <returns>详细的统计报告</returns>
    public string GetDetailedReport()
    {
        var report = new System.Text.StringBuilder();
        report.AppendLine("=== 缓冲区统计报告 ===");
        report.AppendLine($"生成时间: {Timestamp:yyyy-MM-dd HH:mm:ss.fff}");
        report.AppendLine($"队列状态: {QueueLength}/{MaxQueueLength} ({QueueUsagePercentage}%)");
        report.AppendLine($"数据统计:");
        report.AppendLine($"  - 总接收: {TotalBytesReceived:N0} 字节");
        report.AppendLine($"  - 总丢弃: {TotalBytesDropped:N0} 字节");
        report.AppendLine($"  - 丢失率: {DataLossRate:F2}%");
        report.AppendLine($"配置信息:");
        report.AppendLine($"  - 溢出策略: {OverflowStrategy}");
        report.AppendLine($"  - 最后清理: {LastCleanupTime:yyyy-MM-dd HH:mm:ss.fff}");
        
        // 添加健康状态评估
        var healthStatus = GetHealthStatus();
        report.AppendLine($"健康状态: {healthStatus}");
        
        return report.ToString();
    }

    /// <summary>
    /// 获取缓冲区健康状态
    /// </summary>
    /// <returns>健康状态描述</returns>
    public string GetHealthStatus()
    {
        if (QueueUsagePercentage >= 90)
            return "危险 - 队列使用率过高";
        
        if (QueueUsagePercentage >= 80)
            return "警告 - 队列使用率较高";
        
        if (DataLossRate >= 5)
            return "警告 - 数据丢失率较高";
        
        if (DataLossRate >= 1)
            return "注意 - 存在数据丢失";
        
        return "良好 - 运行正常";
    }

    /// <summary>
    /// 获取性能建议
    /// </summary>
    /// <returns>性能优化建议列表</returns>
    public List<string> GetPerformanceSuggestions()
    {
        var suggestions = new List<string>();

        if (QueueUsagePercentage >= 80)
        {
            suggestions.Add("考虑增加队列最大长度或优化数据处理速度");
        }

        if (DataLossRate >= 1)
        {
            suggestions.Add("数据丢失率较高，建议检查数据处理逻辑");
        }

        if (OverflowStrategy == BufferOverflowStrategy.DropNewest)
        {
            suggestions.Add("当前使用丢弃最新数据策略，可能导致数据不连续");
        }

        if (TotalBytesDropped > 0 && OverflowStrategy == BufferOverflowStrategy.ThrowException)
        {
            suggestions.Add("使用异常策略但仍有数据丢失，建议检查异常处理逻辑");
        }

        if (suggestions.Count == 0)
        {
            suggestions.Add("缓冲区运行状态良好，无需特别优化");
        }

        return suggestions;
    }
}

/// <summary>
/// 流控制统计信息
/// </summary>
public class FlowControlStatistics
{
    /// <summary>
    /// 当前流控制状态
    /// </summary>
    public FlowControlStatus CurrentStatus { get; set; }

    /// <summary>
    /// 流控制类型
    /// </summary>
    public FlowControlType FlowControlType { get; set; }

    /// <summary>
    /// 是否启用流控制
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 发送速率限制（字节/秒）
    /// </summary>
    public int SendRateLimit { get; set; }

    /// <summary>
    /// 当前发送速率（字节/秒）
    /// </summary>
    public double CurrentSendRate { get; set; }

    /// <summary>
    /// 总发送字节数
    /// </summary>
    public long TotalBytesSent { get; set; }

    /// <summary>
    /// 总接收字节数
    /// </summary>
    public long TotalBytesReceived { get; set; }

    /// <summary>
    /// 是否收到 XOFF
    /// </summary>
    public bool IsXoffReceived { get; set; }

    /// <summary>
    /// RTS 是否暂停
    /// </summary>
    public bool IsRtsPaused { get; set; }

    /// <summary>
    /// 拥塞控制阈值
    /// </summary>
    public int CongestionThreshold { get; set; }

    /// <summary>
    /// 统计信息生成时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 速率使用率（百分比）
    /// </summary>
    public double RateUsagePercentage => SendRateLimit > 0 ? (CurrentSendRate / SendRateLimit * 100) : 0;

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>统计信息的字符串描述</returns>
    public override string ToString()
    {
        return $"流控制统计 [{Timestamp:HH:mm:ss.fff}]: " +
               $"状态 {CurrentStatus}, " +
               $"类型 {FlowControlType}, " +
               $"速率 {CurrentSendRate:F2}/{SendRateLimit} ({RateUsagePercentage:F1}%), " +
               $"发送 {TotalBytesSent} 字节, " +
               $"接收 {TotalBytesReceived} 字节";
    }

    /// <summary>
    /// 获取详细报告
    /// </summary>
    /// <returns>详细的统计报告</returns>
    public string GetDetailedReport()
    {
        var report = new System.Text.StringBuilder();
        report.AppendLine("=== 流控制统计报告 ===");
        report.AppendLine($"生成时间: {Timestamp:yyyy-MM-dd HH:mm:ss.fff}");
        report.AppendLine($"流控制状态: {CurrentStatus}");
        report.AppendLine($"流控制类型: {FlowControlType}");
        report.AppendLine($"是否启用: {IsEnabled}");
        report.AppendLine($"速率统计:");
        report.AppendLine($"  - 当前速率: {CurrentSendRate:F2} 字节/秒");
        report.AppendLine($"  - 速率限制: {(SendRateLimit > 0 ? SendRateLimit.ToString() : "无限制")} 字节/秒");
        report.AppendLine($"  - 使用率: {RateUsagePercentage:F1}%");
        report.AppendLine($"数据统计:");
        report.AppendLine($"  - 总发送: {TotalBytesSent:N0} 字节");
        report.AppendLine($"  - 总接收: {TotalBytesReceived:N0} 字节");
        report.AppendLine($"流控制状态:");
        report.AppendLine($"  - XOFF 状态: {(IsXoffReceived ? "已接收" : "正常")}");
        report.AppendLine($"  - RTS 状态: {(IsRtsPaused ? "暂停" : "正常")}");
        report.AppendLine($"  - 拥塞阈值: {CongestionThreshold}%");

        return report.ToString();
    }

    /// <summary>
    /// 获取性能建议
    /// </summary>
    /// <returns>性能优化建议列表</returns>
    public List<string> GetPerformanceSuggestions()
    {
        var suggestions = new List<string>();

        if (!IsEnabled)
        {
            suggestions.Add("流控制未启用，考虑启用以提高数据传输可靠性");
        }

        if (RateUsagePercentage > 90)
        {
            suggestions.Add("发送速率接近限制，考虑增加速率限制或优化数据发送");
        }

        if (IsXoffReceived)
        {
            suggestions.Add("接收到 XOFF 信号，对方要求暂停发送，检查接收方处理能力");
        }

        if (IsRtsPaused)
        {
            suggestions.Add("RTS 流控制暂停，检查硬件连接和对方设备状态");
        }

        if (CurrentStatus == FlowControlStatus.Error)
        {
            suggestions.Add("流控制出现错误，检查配置和硬件连接");
        }

        if (suggestions.Count == 0)
        {
            suggestions.Add("流控制运行状态良好，无需特别优化");
        }

        return suggestions;
    }
}
