# Alicres.SerialPort - C# 串口通讯功能库

[![NuGet Version](https://img.shields.io/nuget/v/Alicres.SerialPort.svg)](https://www.nuget.org/packages/Alicres.SerialPort/)
[![NuGet Downloads](https://img.shields.io/nuget/dt/Alicres.SerialPort.svg)](https://www.nuget.org/packages/Alicres.SerialPort/)
[![.NET 8.0](https://img.shields.io/badge/.NET-8.0-blue.svg)](https://dotnet.microsoft.com/download/dotnet/8.0)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Gitee](https://img.shields.io/badge/Gitee-alicres-red.svg)](https://gitee.com/liam-gitee/alicres.git)

一个功能完整、易于使用的 .NET 串口通讯库，严格遵循 Alicres 系列库开发规范，提供异步操作、事件驱动、自动重连等高级功能。

## 🎯 项目概述

Alicres.SerialPort 是一个专业的 C# 串口通讯功能库，旨在为 .NET 应用程序提供稳定、高效的串口通讯解决方案。本项目严格遵循 Alicres 系列库的开发规范，确保代码质量、一致性和可维护性。

## ✨ 核心特性

### 🚀 异步操作支持
- 完全支持 async/await 模式
- 非阻塞的数据收发操作
- 支持取消令牌（CancellationToken）

### 📡 事件驱动架构
- 数据接收事件（DataReceived）
- 连接状态变化事件（StatusChanged）
- 错误处理事件（ErrorOccurred）

### 🔄 智能重连机制
- 可配置的自动重连功能
- 重连间隔和最大重连次数设置
- 连接状态实时监控

### 🎯 多端口管理
- 同时管理多个串口连接
- 批量操作（打开/关闭/广播）
- 统一的事件处理

### 🛡️ 完善的异常处理
- 自定义异常类型体系
- 详细的错误信息和恢复机制
- 集成日志记录

### 📊 状态监控与统计
- 实时连接状态跟踪
- 数据传输统计
- 性能监控指标

### 🗂️ 高级缓冲管理
- 智能缓冲区管理和队列处理
- 多种缓冲区溢出策略（丢弃最旧、丢弃最新、阻塞）
- 缓冲区使用率监控和警告机制
- 自动清理和内存优化

### 🚦 数据流控制
- 支持 XON/XOFF 软件流控制
- 支持 RTS/CTS 硬件流控制
- 可配置的发送速率限制
- 流控制状态实时监控

### 📈 性能监控与诊断
- 详细的性能统计信息
- 缓冲区使用情况分析
- 流控制效果监控
- 性能优化建议

### 🔧 依赖注入支持
- 原生支持 .NET 依赖注入
- 灵活的配置选项
- 易于集成到现有项目

## 📦 项目结构

```
Alicres.SerialPort/
├── src/Alicres.SerialPort/              # 主项目
│   ├── Interfaces/                      # 接口定义
│   │   ├── ISerialPortService.cs        # 串口服务接口
│   │   └── ISerialPortManager.cs        # 串口管理器接口
│   ├── Models/                          # 数据模型
│   │   ├── SerialPortConfiguration.cs   # 串口配置模型
│   │   ├── SerialPortStatus.cs          # 状态模型
│   │   └── SerialPortData.cs            # 数据传输模型
│   ├── Services/                        # 核心服务实现
│   │   ├── SerialPortService.cs         # 串口服务实现
│   │   └── SerialPortManager.cs         # 串口管理器实现
│   ├── Exceptions/                      # 自定义异常
│   │   └── SerialPortException.cs       # 异常类定义
│   ├── Extensions/                      # 扩展方法
│   │   └── ServiceCollectionExtensions.cs # 依赖注入扩展
│   ├── Constants/                       # 常量定义
│   │   └── SerialPortConstants.cs       # 串口常量
│   └── README.md                        # 项目文档
├── tests/Alicres.SerialPort.Tests/      # 单元测试
│   ├── Models/                          # 模型测试
│   │   ├── SerialPortConfigurationTests.cs
│   │   └── SerialPortDataTests.cs
│   └── TestHelpers/                     # 测试辅助
├── examples/Alicres.SerialPort.Examples/ # 示例项目
│   └── Program.cs                       # 示例代码
├── Directory.Build.props                # 全局构建属性
├── Directory.Packages.props             # 中央包管理
├── global.json                          # .NET SDK 版本
├── .editorconfig                        # 代码格式规范
└── Alicres.SerialPort.sln              # 解决方案文件
```

## 🚀 快速开始

### 安装

```bash
dotnet add package Alicres.SerialPort --version 1.1.0
```

### 基本使用

```csharp
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Microsoft.Extensions.Logging;

// 创建配置
var config = new SerialPortConfiguration
{
    PortName = "COM1",
    BaudRate = 9600,
    EnableAutoReconnect = true
};

// 创建服务
using var serialPort = new SerialPortService(config, logger);

// 订阅事件
serialPort.DataReceived += (sender, e) =>
{
    Console.WriteLine($"接收: {e.Data.ToText()}");
};

// 打开连接并发送数据
if (await serialPort.OpenAsync())
{
    await serialPort.SendTextAsync("Hello, Serial Port!");
}
```

### 依赖注入使用

```csharp
// 注册服务
services.AddAlicresSerialPort();

// 使用服务
var manager = serviceProvider.GetRequiredService<ISerialPortManager>();
var serialPort = manager.CreateSerialPort(config);
```

## 📋 技术规格

- **目标框架**: .NET 8.0
- **语言版本**: C# 12.0
- **包管理**: 中央包版本管理
- **测试框架**: xUnit + FluentAssertions + Moq
- **代码覆盖率**: ≥ 80%
- **文档**: 完整的 XML 注释

## 🧪 质量保证

### 单元测试
- **测试数量**: 260 个测试用例
- **覆盖率**: 持续改进中（目标 ≥80%）
- **测试框架**: xUnit, FluentAssertions, Moq

### 代码质量
- **代码分析**: 启用 .NET 分析器
- **警告处理**: 将警告视为错误
- **代码格式**: EditorConfig 统一格式

### 构建配置
- **持续集成**: 自动化构建和测试
- **包生成**: 自动生成 NuGet 包
- **符号包**: 包含调试符号

## 📚 API 文档

### 核心接口

#### ISerialPortService
主要的串口通讯服务接口，提供完整的串口操作功能。

#### ISerialPortManager  
串口管理器接口，用于管理多个串口连接。

### 配置模型

#### SerialPortConfiguration
串口配置类，包含所有串口参数设置：
- 端口名称、波特率、数据位等基本参数
- 超时设置、缓冲区大小等高级配置
- 自动重连相关设置

#### SerialPortStatus
串口状态信息类，提供连接状态和统计数据。

#### SerialPortData
数据传输模型，支持多种数据格式转换。

## 🔧 高级功能

### 自动重连
```csharp
var config = new SerialPortConfiguration
{
    EnableAutoReconnect = true,
    ReconnectInterval = 3000,      // 3秒重连间隔
    MaxReconnectAttempts = 5       // 最大重连5次
};
```

### 流控制功能
```csharp
var config = new SerialPortConfiguration
{
    PortName = "COM1",
    EnableFlowControl = true,
    FlowControlType = FlowControlType.XonXoff,  // 或 RtsCts、Both
    SendRateLimit = 1000           // 限制发送速率为 1000 字节/秒
};

using var serialPort = new SerialPortService(config, logger);

// 监听流控制状态变化
serialPort.FlowControlStatusChanged += (sender, e) =>
{
    Console.WriteLine($"流控制状态: {e.OldStatus} -> {e.NewStatus}");
};

// 获取流控制统计信息
var flowStats = serialPort.GetFlowControlStatistics();
if (flowStats != null)
{
    Console.WriteLine($"当前发送速率: {flowStats.CurrentSendRate:F2} 字节/秒");
    Console.WriteLine($"速率使用率: {flowStats.RateUsagePercentage:F1}%");
}

// 手动发送流控制字符
await serialPort.SendXoffAsync();  // 暂停对方发送
await serialPort.SendXonAsync();   // 恢复对方发送
```

### 数据格式转换
```csharp
// 文本转十六进制
var hexString = data.ToHexString(" ", true);

// 十六进制转数据
var data = SerialPortData.FromHexString("48 65 6C 6C 6F", "COM1");
```

### 多端口管理
```csharp
// 批量操作
await manager.OpenAllAsync();
await manager.BroadcastTextAsync("广播消息");
await manager.CloseAllAsync();
```

## 🛠️ 开发环境

- **IDE**: Visual Studio 2022 / VS Code
- **SDK**: .NET 8.0 SDK
- **包管理器**: NuGet
- **版本控制**: Git

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！请遵循以下规范：

1. 遵循 Alicres 系列库开发规范
2. 确保代码覆盖率 ≥ 80%
3. 添加完整的 XML 文档注释
4. 通过所有单元测试

## 📞 支持与反馈

- **Gitee Issues**: [提交问题](https://gitee.com/liam-gitee/alicres/issues)
- **文档**: [查看文档](https://gitee.com/liam-gitee/alicres.git)
- **示例**: [示例代码](https://gitee.com/liam-gitee/alicres/tree/master/examples)

---

**Alicres.SerialPort** - 让串口通讯变得简单而强大！
