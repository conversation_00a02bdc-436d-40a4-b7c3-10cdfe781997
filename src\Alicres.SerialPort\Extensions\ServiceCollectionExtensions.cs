using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Alicres.SerialPort.Interfaces;
using Alicres.SerialPort.Services;

namespace Alicres.SerialPort.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 Alicres 串口服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddAlicresSerialPort(this IServiceCollection services)
    {
        ArgumentNullException.ThrowIfNull(services);

        // 注册串口服务
        services.TryAddTransient<ISerialPortService, SerialPortService>();
        
        // 注册串口管理器（单例）
        services.TryAddSingleton<ISerialPortManager, SerialPortManager>();

        return services;
    }

    /// <summary>
    /// 添加 Alicres 串口服务并配置选项
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddAlicresSerialPort(
        this IServiceCollection services,
        Action<SerialPortServiceOptions> configureOptions)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configureOptions);

        services.AddAlicresSerialPort();
        services.Configure(configureOptions);

        return services;
    }
}

/// <summary>
/// 串口服务选项
/// </summary>
public class SerialPortServiceOptions
{
    /// <summary>
    /// 默认配置
    /// </summary>
    public Models.SerialPortConfiguration? DefaultConfiguration { get; set; }

    /// <summary>
    /// 是否启用全局错误处理
    /// </summary>
    public bool EnableGlobalErrorHandling { get; set; } = true;

    /// <summary>
    /// 是否启用详细日志记录
    /// </summary>
    public bool EnableVerboseLogging { get; set; } = false;

    /// <summary>
    /// 全局重连配置
    /// </summary>
    public GlobalReconnectOptions? GlobalReconnectOptions { get; set; }
}

/// <summary>
/// 全局重连选项
/// </summary>
public class GlobalReconnectOptions
{
    /// <summary>
    /// 是否启用全局自动重连
    /// </summary>
    public bool EnableAutoReconnect { get; set; } = false;

    /// <summary>
    /// 重连间隔时间（毫秒）
    /// </summary>
    public int ReconnectInterval { get; set; } = 3000;

    /// <summary>
    /// 最大重连次数
    /// </summary>
    public int MaxReconnectAttempts { get; set; } = 5;
}
