using Alicres.Protocol.Models;

namespace Alicres.Protocol.Protocols.Modbus.Messages;

/// <summary>
/// Modbus 消息基类
/// </summary>
public abstract class ModbusMessage : ProtocolMessage
{
    /// <summary>
    /// 协议名称
    /// </summary>
    public override string ProtocolName => "ModbusRTU";

    /// <summary>
    /// 从站地址
    /// </summary>
    public byte SlaveAddress { get; set; }

    /// <summary>
    /// 功能码
    /// </summary>
    public byte FunctionCode { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    protected ModbusMessage()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="rawData">原始数据</param>
    protected ModbusMessage(byte[] rawData) : base(rawData)
    {
        if (rawData.Length >= 2)
        {
            SlaveAddress = rawData[0];
            FunctionCode = rawData[1];
        }
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="functionCode">功能码</param>
    protected ModbusMessage(byte slaveAddress, byte functionCode)
    {
        SlaveAddress = slaveAddress;
        FunctionCode = functionCode;
    }

    /// <summary>
    /// 验证 Modbus 消息的基本格式
    /// </summary>
    /// <returns>如果消息有效返回 true，否则返回 false</returns>
    protected override bool ValidateInternal()
    {
        // 如果有原始数据，验证最小长度为4字节（地址+功能码+数据+CRC）
        if (RawData.Length > 0 && RawData.Length < 4)
            return false;

        // 验证从站地址（0-247有效）
        if (SlaveAddress > 247)
            return false;

        // 验证功能码
        if (!IsValidFunctionCode(FunctionCode))
            return false;

        return true;
    }

    /// <summary>
    /// 检查功能码是否有效
    /// </summary>
    /// <param name="functionCode">功能码</param>
    /// <returns>如果有效返回 true，否则返回 false</returns>
    protected virtual bool IsValidFunctionCode(byte functionCode)
    {
        // 标准 Modbus 功能码
        return functionCode switch
        {
            0x01 => true, // 读取线圈状态
            0x02 => true, // 读取离散输入状态
            0x03 => true, // 读取保持寄存器
            0x04 => true, // 读取输入寄存器
            0x05 => true, // 写单个线圈
            0x06 => true, // 写单个寄存器
            0x0F => true, // 写多个线圈
            0x10 => true, // 写多个寄存器
            _ => false
        };
    }

    /// <summary>
    /// 获取消息的字符串表示
    /// </summary>
    /// <returns>消息的字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] {ProtocolName}.{MessageType} (ID: {MessageId}, Slave: {SlaveAddress}, FC: 0x{FunctionCode:X2}, Length: {RawData.Length} bytes)";
    }
}

/// <summary>
/// Modbus 请求消息基类
/// </summary>
public abstract class ModbusRequestMessage : ModbusMessage
{
    /// <summary>
    /// 构造函数
    /// </summary>
    protected ModbusRequestMessage()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="rawData">原始数据</param>
    protected ModbusRequestMessage(byte[] rawData) : base(rawData)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="functionCode">功能码</param>
    protected ModbusRequestMessage(byte slaveAddress, byte functionCode) : base(slaveAddress, functionCode)
    {
    }
}

/// <summary>
/// Modbus 响应消息基类
/// </summary>
public abstract class ModbusResponseMessage : ModbusMessage
{
    /// <summary>
    /// 构造函数
    /// </summary>
    protected ModbusResponseMessage()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="rawData">原始数据</param>
    protected ModbusResponseMessage(byte[] rawData) : base(rawData)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="functionCode">功能码</param>
    protected ModbusResponseMessage(byte slaveAddress, byte functionCode) : base(slaveAddress, functionCode)
    {
    }
}

/// <summary>
/// Modbus 异常响应消息
/// </summary>
public class ModbusExceptionResponse : ModbusResponseMessage
{
    /// <summary>
    /// 消息类型
    /// </summary>
    public override string MessageType => "ExceptionResponse";

    /// <summary>
    /// 异常码
    /// </summary>
    public byte ExceptionCode { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public ModbusExceptionResponse()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="rawData">原始数据</param>
    public ModbusExceptionResponse(byte[] rawData) : base(rawData)
    {
        if (rawData.Length >= 3)
        {
            ExceptionCode = rawData[2];
        }
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="functionCode">功能码</param>
    /// <param name="exceptionCode">异常码</param>
    public ModbusExceptionResponse(byte slaveAddress, byte functionCode, byte exceptionCode) 
        : base(slaveAddress, (byte)(functionCode | 0x80))
    {
        ExceptionCode = exceptionCode;
    }

    /// <summary>
    /// 将消息序列化为字节数组
    /// </summary>
    /// <returns>序列化后的字节数组</returns>
    public override byte[] ToBytes()
    {
        return new byte[] { SlaveAddress, FunctionCode, ExceptionCode };
    }

    /// <summary>
    /// 验证异常响应消息
    /// </summary>
    /// <returns>如果消息有效返回 true，否则返回 false</returns>
    protected override bool ValidateInternal()
    {
        if (!base.ValidateInternal())
            return false;

        // 异常响应的功能码最高位应该为1
        if ((FunctionCode & 0x80) == 0)
            return false;

        // 验证异常码
        return ExceptionCode switch
        {
            0x01 => true, // 非法功能码
            0x02 => true, // 非法数据地址
            0x03 => true, // 非法数据值
            0x04 => true, // 从站设备故障
            0x05 => true, // 确认
            0x06 => true, // 从站设备忙
            0x08 => true, // 存储奇偶性差错
            0x0A => true, // 不可用网关路径
            0x0B => true, // 网关目标设备响应失败
            _ => false
        };
    }

    /// <summary>
    /// 获取异常码描述
    /// </summary>
    /// <returns>异常码描述</returns>
    public string GetExceptionDescription()
    {
        return ExceptionCode switch
        {
            0x01 => "非法功能码",
            0x02 => "非法数据地址",
            0x03 => "非法数据值",
            0x04 => "从站设备故障",
            0x05 => "确认",
            0x06 => "从站设备忙",
            0x08 => "存储奇偶性差错",
            0x0A => "不可用网关路径",
            0x0B => "网关目标设备响应失败",
            _ => $"未知异常码: 0x{ExceptionCode:X2}"
        };
    }
}
