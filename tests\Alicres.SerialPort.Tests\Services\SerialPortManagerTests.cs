using Alicres.SerialPort.Interfaces;
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using System.IO.Ports;
using Xunit;

namespace Alicres.SerialPort.Tests.Services;

/// <summary>
/// 串口管理器测试类
/// </summary>
public class SerialPortManagerTests : IDisposable
{
    private readonly Mock<ILogger<SerialPortManager>> _mockLogger;
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<ILogger<SerialPortService>> _mockSerialPortLogger;
    private readonly SerialPortManager _manager;

    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortManagerTests()
    {
        _mockLogger = new Mock<ILogger<SerialPortManager>>();
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockSerialPortLogger = new Mock<ILogger<SerialPortService>>();

        // 设置服务提供程序返回日志记录器
        _mockServiceProvider
            .Setup(sp => sp.GetService(typeof(ILogger<SerialPortService>)))
            .Returns(_mockSerialPortLogger.Object);

        _manager = new SerialPortManager(_mockLogger.Object, _mockServiceProvider.Object);
    }

    /// <summary>
    /// 测试构造函数正确初始化
    /// </summary>
    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeCorrectly()
    {
        // Assert
        _manager.SerialPorts.Should().NotBeNull();
        _manager.SerialPorts.Should().BeEmpty();
    }

    /// <summary>
    /// 测试空日志记录器抛出异常
    /// </summary>
    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowException()
    {
        // Act & Assert
        var action = () => new SerialPortManager(null!, _mockServiceProvider.Object);
        action.Should().Throw<ArgumentNullException>();
    }

    /// <summary>
    /// 测试空服务提供程序抛出异常
    /// </summary>
    [Fact]
    public void Constructor_WithNullServiceProvider_ShouldThrowException()
    {
        // Act & Assert
        var action = () => new SerialPortManager(_mockLogger.Object, null!);
        action.Should().Throw<ArgumentNullException>();
    }

    /// <summary>
    /// 测试创建串口服务
    /// </summary>
    [Fact]
    public void CreateSerialPort_WithValidConfiguration_ShouldCreateAndAddPort()
    {
        // Arrange
        var configuration = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 9600,
            DataBits = 8,
            StopBits = StopBits.One,
            Parity = Parity.None
        };

        // Act
        var serialPort = _manager.CreateSerialPort(configuration);

        // Assert
        serialPort.Should().NotBeNull();
        serialPort.Configuration.Should().BeEquivalentTo(configuration);
        _manager.SerialPorts.Should().ContainKey("COM1");
        _manager.SerialPorts["COM1"].Should().Be(serialPort);
    }

    /// <summary>
    /// 测试创建重复端口抛出异常
    /// </summary>
    [Fact]
    public void CreateSerialPort_WithDuplicatePortName_ShouldThrowException()
    {
        // Arrange
        var configuration = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 9600
        };

        _manager.CreateSerialPort(configuration);

        // Act & Assert
        var action = () => _manager.CreateSerialPort(configuration);
        action.Should().Throw<ArgumentException>()
            .WithMessage("*端口 COM1 已存在*");
    }

    /// <summary>
    /// 测试创建串口空配置抛出异常
    /// </summary>
    [Fact]
    public void CreateSerialPort_WithNullConfiguration_ShouldThrowException()
    {
        // Act & Assert
        var action = () => _manager.CreateSerialPort(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    /// <summary>
    /// 测试添加现有串口服务
    /// </summary>
    [Fact]
    public void AddSerialPort_WithValidService_ShouldAddToCollection()
    {
        // Arrange
        var configuration = new SerialPortConfiguration { PortName = "COM2" };
        var mockSerialPort = new Mock<ISerialPortService>();
        mockSerialPort.Setup(sp => sp.Configuration).Returns(configuration);

        // Act
        _manager.AddSerialPort(mockSerialPort.Object);

        // Assert
        _manager.SerialPorts.Should().ContainKey("COM2");
        _manager.SerialPorts["COM2"].Should().Be(mockSerialPort.Object);
    }

    /// <summary>
    /// 测试添加空串口服务抛出异常
    /// </summary>
    [Fact]
    public void AddSerialPort_WithNullService_ShouldThrowException()
    {
        // Act & Assert
        var action = () => _manager.AddSerialPort(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    /// <summary>
    /// 测试添加重复串口服务抛出异常
    /// </summary>
    [Fact]
    public void AddSerialPort_WithDuplicatePortName_ShouldThrowException()
    {
        // Arrange
        var configuration = new SerialPortConfiguration { PortName = "COM3" };
        var mockSerialPort1 = new Mock<ISerialPortService>();
        var mockSerialPort2 = new Mock<ISerialPortService>();
        mockSerialPort1.Setup(sp => sp.Configuration).Returns(configuration);
        mockSerialPort2.Setup(sp => sp.Configuration).Returns(configuration);

        _manager.AddSerialPort(mockSerialPort1.Object);

        // Act & Assert
        var action = () => _manager.AddSerialPort(mockSerialPort2.Object);
        action.Should().Throw<ArgumentException>()
            .WithMessage("*端口 COM3 已存在*");
    }

    /// <summary>
    /// 测试移除串口服务
    /// </summary>
    [Fact]
    public void RemoveSerialPort_WithExistingPort_ShouldRemoveAndReturnTrue()
    {
        // Arrange
        var configuration = new SerialPortConfiguration { PortName = "COM4" };
        _manager.CreateSerialPort(configuration);

        // Act
        var result = _manager.RemoveSerialPort("COM4");

        // Assert
        result.Should().BeTrue();
        _manager.SerialPorts.Should().NotContainKey("COM4");
    }

    /// <summary>
    /// 测试移除不存在的串口服务
    /// </summary>
    [Fact]
    public void RemoveSerialPort_WithNonExistentPort_ShouldReturnFalse()
    {
        // Act
        var result = _manager.RemoveSerialPort("COM99");

        // Assert
        result.Should().BeFalse();
    }

    /// <summary>
    /// 测试移除串口空端口名抛出异常
    /// </summary>
    [Fact]
    public void RemoveSerialPort_WithNullOrEmptyPortName_ShouldThrowException()
    {
        // Act & Assert
        var action1 = () => _manager.RemoveSerialPort(null!);
        var action2 = () => _manager.RemoveSerialPort("");
        var action3 = () => _manager.RemoveSerialPort("   ");

        action1.Should().Throw<ArgumentException>();
        action2.Should().Throw<ArgumentException>();
        action3.Should().Throw<ArgumentException>();
    }

    /// <summary>
    /// 测试获取串口服务
    /// </summary>
    [Fact]
    public void GetSerialPort_WithExistingPort_ShouldReturnService()
    {
        // Arrange
        var configuration = new SerialPortConfiguration { PortName = "COM5" };
        var createdPort = _manager.CreateSerialPort(configuration);

        // Act
        var retrievedPort = _manager.GetSerialPort("COM5");

        // Assert
        retrievedPort.Should().Be(createdPort);
    }

    /// <summary>
    /// 测试获取不存在的串口服务
    /// </summary>
    [Fact]
    public void GetSerialPort_WithNonExistentPort_ShouldReturnNull()
    {
        // Act
        var result = _manager.GetSerialPort("COM99");

        // Assert
        result.Should().BeNull();
    }

    /// <summary>
    /// 测试检查端口是否存在
    /// </summary>
    [Fact]
    public void ContainsPort_WithExistingPort_ShouldReturnTrue()
    {
        // Arrange
        var configuration = new SerialPortConfiguration { PortName = "COM6" };
        _manager.CreateSerialPort(configuration);

        // Act & Assert
        _manager.ContainsPort("COM6").Should().BeTrue();
        _manager.ContainsPort("COM99").Should().BeFalse();
    }

    /// <summary>
    /// 测试获取所有状态
    /// </summary>
    [Fact]
    public void GetAllStatus_WithMultiplePorts_ShouldReturnAllStatuses()
    {
        // Arrange
        var config1 = new SerialPortConfiguration { PortName = "COM7" };
        var config2 = new SerialPortConfiguration { PortName = "COM8" };
        _manager.CreateSerialPort(config1);
        _manager.CreateSerialPort(config2);

        // Act
        var statuses = _manager.GetAllStatus();

        // Assert
        statuses.Should().HaveCount(2);
        statuses.Should().ContainKey("COM7");
        statuses.Should().ContainKey("COM8");
    }

    /// <summary>
    /// 测试获取可用端口
    /// </summary>
    [Fact]
    public void GetAvailablePorts_ShouldReturnPortArray()
    {
        // Act
        var ports = _manager.GetAvailablePorts();

        // Assert
        ports.Should().NotBeNull();
        ports.Should().BeOfType<string[]>();
    }

    /// <summary>
    /// 测试广播数据空数据抛出异常
    /// </summary>
    [Fact]
    public async Task BroadcastAsync_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        var action = async () => await _manager.BroadcastAsync(null!);
        await action.Should().ThrowAsync<ArgumentNullException>();
    }

    /// <summary>
    /// 测试广播文本空文本抛出异常
    /// </summary>
    [Fact]
    public async Task BroadcastTextAsync_WithNullText_ShouldThrowException()
    {
        // Act & Assert
        var action = async () => await _manager.BroadcastTextAsync(null!);
        await action.Should().ThrowAsync<ArgumentNullException>();
    }

    /// <summary>
    /// 测试释放资源
    /// </summary>
    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _manager.Dispose();
        action.Should().NotThrow();
    }

    /// <summary>
    /// 释放测试资源
    /// </summary>
    public void Dispose()
    {
        _manager?.Dispose();
    }
}
