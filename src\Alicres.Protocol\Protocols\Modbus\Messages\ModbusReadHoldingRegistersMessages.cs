namespace Alicres.Protocol.Protocols.Modbus.Messages;

/// <summary>
/// Modbus 读取保持寄存器请求消息（功能码 0x03）
/// </summary>
public class ModbusReadHoldingRegistersRequest : ModbusRequestMessage
{
    /// <summary>
    /// 消息类型
    /// </summary>
    public override string MessageType => "ReadHoldingRegistersRequest";

    /// <summary>
    /// 起始地址
    /// </summary>
    public ushort StartAddress { get; set; }

    /// <summary>
    /// 寄存器数量
    /// </summary>
    public ushort RegisterCount { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public ModbusReadHoldingRegistersRequest()
    {
        FunctionCode = 0x03;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="rawData">原始数据</param>
    public ModbusReadHoldingRegistersRequest(byte[] rawData) : base(rawData)
    {
        if (rawData.Length >= 6)
        {
            StartAddress = (ushort)((rawData[2] << 8) | rawData[3]);
            RegisterCount = (ushort)((rawData[4] << 8) | rawData[5]);
        }
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="startAddress">起始地址</param>
    /// <param name="registerCount">寄存器数量</param>
    public ModbusReadHoldingRegistersRequest(byte slaveAddress, ushort startAddress, ushort registerCount)
        : base(slaveAddress, 0x03)
    {
        StartAddress = startAddress;
        RegisterCount = registerCount;
    }

    /// <summary>
    /// 将消息序列化为字节数组
    /// </summary>
    /// <returns>序列化后的字节数组</returns>
    public override byte[] ToBytes()
    {
        var result = new byte[]
        {
            SlaveAddress,
            FunctionCode,
            (byte)(StartAddress >> 8),
            (byte)(StartAddress & 0xFF),
            (byte)(RegisterCount >> 8),
            (byte)(RegisterCount & 0xFF)
        };

        // 更新 RawData 以便验证
        RawData = result;
        return result;
    }

    /// <summary>
    /// 验证请求消息
    /// </summary>
    /// <returns>如果消息有效返回 true，否则返回 false</returns>
    protected override bool ValidateInternal()
    {
        if (!base.ValidateInternal())
            return false;

        // 验证功能码
        if (FunctionCode != 0x03)
            return false;

        // 验证寄存器数量（1-125个寄存器）
        if (RegisterCount < 1 || RegisterCount > 125)
            return false;

        // 验证地址范围
        if ((uint)StartAddress + RegisterCount > 65536)
            return false;

        return true;
    }

    /// <summary>
    /// 获取消息的字符串表示
    /// </summary>
    /// <returns>消息的字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] {ProtocolName}.ReadHoldingRegistersRequest (ID: {MessageId}, Slave: {SlaveAddress}, Start: {StartAddress}, Count: {RegisterCount})";
    }
}

/// <summary>
/// Modbus 读取保持寄存器响应消息（功能码 0x03）
/// </summary>
public class ModbusReadHoldingRegistersResponse : ModbusResponseMessage
{
    /// <summary>
    /// 消息类型
    /// </summary>
    public override string MessageType => "ReadHoldingRegistersResponse";

    /// <summary>
    /// 字节数
    /// </summary>
    public byte ByteCount { get; set; }

    /// <summary>
    /// 寄存器值数组
    /// </summary>
    public ushort[] RegisterValues { get; set; } = Array.Empty<ushort>();

    /// <summary>
    /// 构造函数
    /// </summary>
    public ModbusReadHoldingRegistersResponse()
    {
        FunctionCode = 0x03;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="rawData">原始数据</param>
    public ModbusReadHoldingRegistersResponse(byte[] rawData) : base(rawData)
    {
        if (rawData.Length >= 3)
        {
            ByteCount = rawData[2];
            
            if (rawData.Length >= 3 + ByteCount)
            {
                var registerCount = ByteCount / 2;
                RegisterValues = new ushort[registerCount];
                
                for (int i = 0; i < registerCount; i++)
                {
                    var dataIndex = 3 + (i * 2);
                    RegisterValues[i] = (ushort)((rawData[dataIndex] << 8) | rawData[dataIndex + 1]);
                }
            }
        }
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="slaveAddress">从站地址</param>
    /// <param name="registerValues">寄存器值数组</param>
    public ModbusReadHoldingRegistersResponse(byte slaveAddress, ushort[] registerValues)
        : base(slaveAddress, 0x03)
    {
        RegisterValues = registerValues ?? throw new ArgumentNullException(nameof(registerValues));
        ByteCount = (byte)(registerValues.Length * 2);
    }

    /// <summary>
    /// 将消息序列化为字节数组
    /// </summary>
    /// <returns>序列化后的字节数组</returns>
    public override byte[] ToBytes()
    {
        var result = new byte[3 + ByteCount];
        result[0] = SlaveAddress;
        result[1] = FunctionCode;
        result[2] = ByteCount;

        for (int i = 0; i < RegisterValues.Length; i++)
        {
            var dataIndex = 3 + (i * 2);
            result[dataIndex] = (byte)(RegisterValues[i] >> 8);
            result[dataIndex + 1] = (byte)(RegisterValues[i] & 0xFF);
        }

        return result;
    }

    /// <summary>
    /// 验证响应消息
    /// </summary>
    /// <returns>如果消息有效返回 true，否则返回 false</returns>
    protected override bool ValidateInternal()
    {
        if (!base.ValidateInternal())
            return false;

        // 验证功能码
        if (FunctionCode != 0x03)
            return false;

        // 验证字节数
        if (ByteCount != RegisterValues.Length * 2)
            return false;

        // 验证字节数范围（最多125个寄存器，即250字节）
        if (ByteCount > 250)
            return false;

        return true;
    }

    /// <summary>
    /// 获取指定索引的寄存器值
    /// </summary>
    /// <param name="index">寄存器索引</param>
    /// <returns>寄存器值</returns>
    public ushort GetRegisterValue(int index)
    {
        if (index < 0 || index >= RegisterValues.Length)
            throw new ArgumentOutOfRangeException(nameof(index));

        return RegisterValues[index];
    }

    /// <summary>
    /// 设置指定索引的寄存器值
    /// </summary>
    /// <param name="index">寄存器索引</param>
    /// <param name="value">寄存器值</param>
    public void SetRegisterValue(int index, ushort value)
    {
        if (index < 0 || index >= RegisterValues.Length)
            throw new ArgumentOutOfRangeException(nameof(index));

        RegisterValues[index] = value;
    }

    /// <summary>
    /// 获取寄存器数量
    /// </summary>
    /// <returns>寄存器数量</returns>
    public int GetRegisterCount()
    {
        return RegisterValues.Length;
    }

    /// <summary>
    /// 获取消息的字符串表示
    /// </summary>
    /// <returns>消息的字符串描述</returns>
    public override string ToString()
    {
        var values = string.Join(", ", RegisterValues.Take(5).Select(v => v.ToString()));
        var suffix = RegisterValues.Length > 5 ? "..." : "";
        return $"[{Timestamp:HH:mm:ss.fff}] {ProtocolName}.ReadHoldingRegistersResponse (ID: {MessageId}, Slave: {SlaveAddress}, Count: {RegisterValues.Length}, Values: [{values}{suffix}])";
    }
}
