# 更新日志

本文档记录了 Alicres 系列功能库的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [1.1.0 & 1.0.0] - 2025-06-12 - 新账户发布和功能完善

### 🔄 账户迁移和发布

**包所有权转移**
- ✅ 成功将包所有权转移到新的微软账户
- ✅ 更新了所有仓库地址到 `https://gitee.com/liam-gitee/alicres.git`
- ✅ 配置了新的 API 密钥用于包发布

**版本发布**
- ✅ **Alicres.SerialPort 1.1.0** - 从 1.0.1 升级，包含完整 P1 级功能
- ✅ **Alicres.Protocol 1.0.0** - 从 1.0.0-alpha.1 升级到正式版

### Alicres.SerialPort 1.1.0

#### ✨ 新增功能
- ✅ 完整的 P1 级功能集成（高级缓冲、流控制、性能监控）
- ✅ 260 个测试用例全部通过，测试覆盖率达到 95%
- ✅ 更新了仓库地址和包配置信息

### Alicres.Protocol 1.0.0

#### ✨ 新增功能
- ✅ 从 Alpha 版本升级到正式版
- ✅ 完整的协议解析框架
- ✅ 支持多种传输层（串口、TCP、UDP）
- ✅ 内置 Modbus RTU/ASCII 协议支持
- ✅ 数据校验与完整性检查（CRC16）
- ✅ 53 个测试用例，测试覆盖率达到 80%

#### 🔧 技术改进
- ✅ 修复了包配置中的图标重复问题
- ✅ 优化了项目依赖关系
- ✅ 完善了 XML 文档注释

### 📚 文档更新

**版本信息更新**
- ✅ 更新了主 README.md 中的版本号和安装命令
- ✅ 更新了各模块 README.md 中的版本信息
- ✅ 更新了开发进度表，反映当前状态

**安装命令更新**
- ✅ Alicres.SerialPort: `dotnet add package Alicres.SerialPort --version 1.1.0`
- ✅ Alicres.Protocol: `dotnet add package Alicres.Protocol --version 1.0.0`

### 🧪 质量验证

**包安装测试**
- ✅ 验证了新版本包可以正常安装
- ✅ 验证了包的基本功能可以正常使用
- ✅ 确认了包的依赖关系正确

**发布验证**
- ✅ 包已成功发布到 NuGet.org
- ✅ 包信息和链接正确显示
- ✅ 新账户具有完全控制权

### 📊 发布统计

| 包名 | 新版本 | 发布状态 | 测试数量 | 功能完成度 |
|------|--------|----------|----------|------------|
| **Alicres.SerialPort** | 1.1.0 | ✅ 已发布 | 260 个测试 | 95% |
| **Alicres.Protocol** | 1.0.0 | ✅ 已发布 | 53 个测试 | 80% |

## [0.2.0] - 2025-06-12 - 阶段二 P1 级功能完成

### Alicres.SerialPort

#### ✨ 新增功能

**高级缓冲和队列管理**
- ✅ `AdvancedBufferManager` 类，提供智能缓冲区管理
- ✅ 支持多种缓冲区溢出策略：丢弃最旧数据、丢弃最新数据、阻塞等待、抛出异常、动态扩展
- ✅ 缓冲区使用率监控和警告机制
- ✅ 自动清理和内存优化功能
- ✅ `BufferStatistics` 类，提供详细的缓冲区统计信息

**数据流控制功能**
- ✅ `FlowControlManager` 类，支持多种流控制协议
- ✅ XON/XOFF 软件流控制支持
- ✅ RTS/CTS 硬件流控制支持
- ✅ 可配置的发送速率限制
- ✅ 流控制状态实时监控
- ✅ `FlowControlStatistics` 类，提供流控制效果监控
- ✅ 流控制管理器已完全集成到 `SerialPortService`

**性能监控与诊断**
- ✅ 实时性能指标监控
- ✅ 性能优化建议和报告生成
- ✅ 缓冲区使用情况分析
- ✅ 流控制效果评估
- ✅ 发送速率监控和统计

**扩展的数据模型**
- ✅ `BufferOverflowStrategy` 枚举，定义缓冲区溢出处理策略
- ✅ `FlowControlType` 枚举，定义流控制类型
- ✅ `FlowControlStatus` 枚举，定义流控制状态
- ✅ 相关事件参数类和统计模型

**接口扩展**
- ✅ `ISerialPortService` 接口新增流控制相关方法
- ✅ `SerialPortConfiguration` 新增流控制配置项
- ✅ 新增流控制状态变化事件

#### 🔧 改进功能

**核心服务增强**
- ✅ `SerialPortService` 完全集成流控制管理器
- ✅ 数据发送前检查流控制状态
- ✅ 数据接收时处理流控制字符
- ✅ 增强了异常处理和错误恢复机制
- ✅ 增强了日志记录的详细程度和可读性

**配置模型扩展**
- ✅ 新增 `EnableFlowControl` 配置项
- ✅ 新增 `FlowControlType` 配置项
- ✅ 新增 `SendRateLimit` 配置项

#### 🧪 测试改进

**测试覆盖率大幅提升**
- ✅ 测试用例数量从 129 个增加到 260 个
- ✅ 所有测试用例 100% 通过
- ✅ 新增流控制管理器集成测试
- ✅ 改进了现有测试的稳定性和可靠性

**测试质量提升**
- ✅ 增加了覆盖率收集和报告生成
- ✅ 改进了测试的边界条件覆盖
- ✅ 增强了异常情况的测试验证
- ✅ 优化了测试的执行效率
- ✅ 更新了 MockSerialPortService 以支持新接口

#### 📚 文档更新

**项目文档完善**
- ✅ 更新了项目主 README.md，反映阶段二功能特性
- ✅ 更新了 Alicres.SerialPort 模块 README.md
- ✅ 新增了流控制功能的详细使用示例
- ✅ 改进了代码注释的完整性和准确性

**开发文档改进**
- ✅ 更新了功能特性描述
- ✅ 增加了高级功能的使用指南
- ✅ 完善了性能监控的说明文档
- ✅ 更新了测试统计信息
- ✅ 新增了质量审查报告

#### 🔧 修复问题

**集成问题修复**
- ✅ 修复了 FlowControlManager 未集成到 SerialPortService 的问题
- ✅ 修复了接口方法缺失导致的编译错误
- ✅ 修复了配置项缺失的问题
- ✅ 修复了测试辅助类的接口实现问题

**代码质量改进**
- ✅ 消除了所有编译警告
- ✅ 统一了事件参数类的定义
- ✅ 整合了重复的统计功能
- ✅ 优化了错误处理机制

#### 📊 质量指标

**代码质量**
- ✅ 编译警告: 0 个严重警告
- ✅ 代码分析: 通过所有规则检查
- ✅ 单元测试: 260 个测试全部通过
- ✅ 接口完整性: 100% 实现

**功能完整性**
- ✅ P1 级功能: 90% 完成
- ✅ 核心功能: 100% 可用
- ✅ 集成度: 95% 模块间集成完成
- ✅ 配置支持: 100% 支持所需配置

## [1.0.1] - 2024-06-11

### ✨ 新增功能

**包图标支持**
- ✅ 添加了 NuGet 包图标配置
- ✅ 配置了全局图标管理系统  
- ✅ 统一了所有 Alicres 系列包的图标标准

**构建改进**
- ✅ 优化了全局构建配置
- ✅ 改进了包生成流程
- ✅ 增强了图标包含机制

### 🔧 技术改进

**项目配置**
- ✅ 在 `Directory.Build.props` 中添加了统一图标配置
- ✅ 简化了项目文件中的重复配置
- ✅ 建立了 `src/icon.png` 作为统一图标源

**包管理**
- ✅ 改进了 NuGet 包元数据
- ✅ 优化了包内容组织
- ✅ 增强了包的视觉识别度

### 📦 发布信息

- **版本**: 1.0.1
- **发布时间**: 2024-06-11
- **包大小**: 优化后的包大小
- **新增内容**: 包图标支持

## [1.0.0] - 2024-06-11

### 🎉 首次发布

#### ✨ 新增功能

**核心功能**
- ✅ 串口连接管理（打开、关闭、状态检查）
- ✅ 数据收发功能（同步/异步方式）
- ✅ 串口参数配置（波特率、数据位、停止位、校验位等）
- ✅ 事件驱动的数据接收处理
- ✅ 错误处理和异常管理
- ✅ 连接状态监控和自动重连机制

**接口设计**
- ✅ `ISerialPortService` - 核心串口服务接口
- ✅ `ISerialPortManager` - 串口管理器接口
- ✅ 完整的事件参数定义

**数据模型**
- ✅ `SerialPortConfiguration` - 串口配置模型
- ✅ `SerialPortStatus` - 连接状态模型  
- ✅ `SerialPortData` - 数据传输模型
- ✅ 支持多种数据格式转换（文本、十六进制）

**核心服务**
- ✅ `SerialPortService` - 主要服务实现
- ✅ `SerialPortManager` - 连接管理器
- ✅ 完整的异步操作支持

**异常处理**
- ✅ `SerialPortException` - 基础异常类
- ✅ `SerialPortConnectionException` - 连接异常
- ✅ `SerialPortConfigurationException` - 配置异常
- ✅ `SerialPortDataException` - 数据传输异常

**扩展功能**
- ✅ 依赖注入扩展方法
- ✅ 配置选项支持
- ✅ 多端口管理功能

#### 🏗️ 项目基础设施

**构建配置**
- ✅ `Directory.Build.props` - 统一构建属性
- ✅ `Directory.Packages.props` - 中央包管理
- ✅ `global.json` - .NET SDK 版本控制
- ✅ `.editorconfig` - 代码格式规范

**项目结构**
- ✅ 标准的 Alicres 项目目录结构
- ✅ 接口优先的设计模式
- ✅ 完整的 XML 文档注释

**质量保证**
- ✅ 单元测试覆盖率 ≥ 80%（53个测试用例）
- ✅ 代码分析和警告处理
- ✅ NuGet 包发布配置

#### 📚 文档和示例

**文档**
- ✅ 完整的 README.md 文档
- ✅ API 文档和使用指南
- ✅ 项目架构说明

**示例项目**
- ✅ 基本串口通讯示例
- ✅ 串口管理器使用示例
- ✅ 自动重连功能演示
- ✅ 数据格式转换示例
- ✅ 依赖注入集成示例

#### 🧪 测试覆盖

**模型测试**
- ✅ `SerialPortConfigurationTests` - 配置模型测试
- ✅ `SerialPortDataTests` - 数据模型测试
- ✅ 边界条件和异常情况测试

**测试框架**
- ✅ xUnit 测试框架
- ✅ FluentAssertions 断言库
- ✅ Moq 模拟对象框架

#### 📦 NuGet 包

**包信息**
- ✅ 包 ID: `Alicres.SerialPort`
- ✅ 版本: `1.0.0`
- ✅ 目标框架: `.NET 8.0`
- ✅ 许可证: `MIT`

**包内容**
- ✅ 主程序集和依赖项
- ✅ XML 文档文件
- ✅ 符号包（.snupkg）
- ✅ README 文件

---

## 版本说明

### 版本号规则
本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范：

- **主版本号**：不兼容的 API 修改
- **次版本号**：向下兼容的功能性新增  
- **修订号**：向下兼容的问题修正

### 发布周期
- **主版本**：根据重大功能更新或架构变更发布
- **次版本**：每月发布，包含新功能和改进
- **修订版本**：根据需要发布，主要修复 bug

### 支持政策
- 当前主版本：完全支持
- 前一个主版本：安全更新和关键 bug 修复
- 更早版本：不再维护

---

*更多信息请访问 [Gitee 仓库](https://gitee.com/liam-gitee/alicres.git)*
