# Alicres.SerialPort 图标资源

## 📁 文件说明

### icon.svg
- **用途**: 源图标文件（矢量格式）
- **尺寸**: 128x128 像素
- **格式**: SVG
- **设计**: 串口连接器图标，蓝色主题

### icon.png（需要生成）
- **用途**: NuGet 包图标
- **尺寸**: 128x128 像素
- **格式**: PNG
- **要求**: 从 SVG 转换而来

## 🎨 设计说明

### 图标元素
- **背景**: 蓝色圆形背景 (#2563eb)
- **主体**: 白色串口连接器
- **针脚**: 蓝色圆点表示串口针脚
- **数据线**: 绿色曲线表示数据传输
- **文字**: "Alicres" 和 "SerialPort" 标识

### 颜色方案
- **主色**: #2563eb (蓝色)
- **辅色**: #10b981 (绿色)
- **背景**: #ffffff (白色)
- **文字**: #ffffff (白色)

## 🔧 生成 PNG 图标

您可以使用以下方法将 SVG 转换为 PNG：

### 方法1: 在线转换
1. 访问 https://convertio.co/svg-png/
2. 上传 icon.svg 文件
3. 设置输出尺寸为 128x128
4. 下载生成的 icon.png

### 方法2: 使用 Inkscape
```bash
inkscape --export-png=icon.png --export-width=128 --export-height=128 icon.svg
```

### 方法3: 使用 ImageMagick
```bash
magick convert -background transparent -size 128x128 icon.svg icon.png
```

### 方法4: 使用在线工具
- https://cloudconvert.com/svg-to-png
- https://www.freeconvert.com/svg-to-png

## 📋 使用说明

1. 将生成的 `icon.png` 文件放在此目录下
2. 确保项目文件中已配置图标引用
3. 重新构建并发布 NuGet 包
4. 图标将在 NuGet.org 和 Visual Studio 中显示

## ✅ 检查清单

- [ ] 生成 icon.png 文件
- [ ] 验证图标尺寸 (128x128)
- [ ] 确认文件大小 < 1MB
- [ ] 测试图标在不同背景下的显示效果
- [ ] 更新包版本号
- [ ] 重新发布 NuGet 包
