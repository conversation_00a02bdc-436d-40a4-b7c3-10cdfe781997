<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- 包信息 -->
    <PackageId>Alicres.Protocol</PackageId>
    <Version>1.0.0</Version>
    <Description>A comprehensive communication protocol library for .NET, supporting multiple transport layers including Serial Port, TCP, and UDP with extensible protocol parsing framework.</Description>
    <PackageTags>protocol;communication;modbus;serialport;tcp;udp;parsing;industrial</PackageTags>
    <PackageProjectUrl>https://gitee.com/liam-gitee/alicres.git</PackageProjectUrl>
    <RepositoryUrl>https://gitee.com/liam-gitee/alicres.git</RepositoryUrl>
    
    <!-- 包依赖关系 -->
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <PackageIcon>icon.png</PackageIcon>
  </PropertyGroup>

  <ItemGroup>
    <!-- 包含 README 和图标文件 -->
    <None Include="README.md" Pack="true" PackagePath="\" />
    <None Include="..\icon.png" Pack="true" PackagePath="\" />
  </ItemGroup>

  <ItemGroup>
    <!-- 日志记录 -->
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />

    <!-- 依赖注入 -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />

    <!-- 配置管理 -->
    <PackageReference Include="Microsoft.Extensions.Options" />
  </ItemGroup>

  <ItemGroup>
    <!-- 项目引用 -->
    <ProjectReference Include="..\Alicres.SerialPort\Alicres.SerialPort.csproj" />
  </ItemGroup>

</Project>
