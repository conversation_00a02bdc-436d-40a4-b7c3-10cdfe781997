using Alicres.SerialPort.Exceptions;
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Tests.Integration.TestHelpers;
using Alicres.SerialPort.Tests.TestHelpers;
using FluentAssertions;
using System.Text;
using Xunit;

namespace Alicres.SerialPort.Tests.Integration;

/// <summary>
/// 边界条件测试
/// </summary>
public class BoundaryConditionTests : IntegrationTestBase
{
    /// <summary>
    /// 测试极小数据包处理
    /// </summary>
    [Theory]
    [InlineData(0)]    // 空数据
    [InlineData(1)]    // 单字节
    [InlineData(2)]    // 双字节
    public async Task SerialPort_TinyDataPackets_ShouldHandleCorrectly(int dataSize)
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_TINY_DATA");
        var service = CreateSerialPortService(configuration);

        try
        {
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            if (dataSize == 0)
            {
                // Act & Assert - 空数据应该抛出异常
                var emptyData = Array.Empty<byte>();
                var sendAction = async () => await service.SendAsync(emptyData);
                await sendAction.Should().ThrowAsync<ArgumentNullException>("发送空数据应该抛出异常");
            }
            else
            {
                // Act & Assert - 小数据包应该正常处理
                var tinyData = TestDataGenerator.GenerateTestData(dataSize);
                var sentBytes = await service.SendAsync(tinyData);

                sentBytes.Should().Be(dataSize, "应该发送所有字节");
                service.Status.BytesSent.Should().Be(dataSize, "统计应该正确");
            }
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试极大数据包处理
    /// </summary>
    [Theory]
    [InlineData(64 * 1024)]      // 64KB
    [InlineData(128 * 1024)]     // 128KB
    [InlineData(256 * 1024)]     // 256KB
    public async Task SerialPort_LargeDataPackets_ShouldHandleCorrectly(int dataSize)
    {
        // Arrange
        var configuration = TestDataGenerator.CreateAdvancedBufferingConfiguration("COM_LARGE_DATA");
        configuration.SendBufferSize = Math.Max(configuration.SendBufferSize, dataSize + 1024);
        configuration.ReceiveBufferSize = Math.Max(configuration.ReceiveBufferSize, dataSize + 1024);

        var service = CreateSerialPortService(configuration);

        try
        {
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // Act
            var largeData = TestDataGenerator.GenerateTestData(dataSize);
            var startTime = DateTime.UtcNow;
            var sentBytes = await service.SendAsync(largeData);
            var elapsed = DateTime.UtcNow - startTime;

            // Assert
            sentBytes.Should().Be(dataSize, "应该发送所有字节");
            service.Status.BytesSent.Should().Be(dataSize, "统计应该正确");
            
            // 性能验证：大数据包传输应该在合理时间内完成
            elapsed.Should().BeLessThan(TimeSpan.FromSeconds(30), "大数据包传输应该在30秒内完成");

            // 生成性能报告
            GeneratePerformanceReport($"大数据包传输测试 ({dataSize / 1024}KB)", dataSize, elapsed);
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试特殊字符处理
    /// </summary>
    [Theory]
    [InlineData("\0")]           // 空字符
    [InlineData("\r\n")]         // 换行符
    [InlineData("\t")]           // 制表符
    [InlineData("中文测试")]      // 中文字符
    [InlineData("🚀🔥💻")]        // Emoji字符
    [InlineData("\"'\\")]        // 转义字符
    public async Task SerialPort_SpecialCharacters_ShouldHandleCorrectly(string testText)
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_SPECIAL_CHARS");
        var service = CreateSerialPortService(configuration);
        var mockService = service as MockSerialPortService;
        var mockPort = mockService!.GetMockSerialPort();

        var dataCollector = CreateEventCollector<SerialPortDataReceivedEventArgs>();
        service.DataReceived += dataCollector.EventHandler;

        try
        {
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // Act - 发送特殊字符
            var sentBytes = await service.SendTextAsync(testText, Encoding.UTF8);
            var expectedBytes = Encoding.UTF8.GetByteCount(testText);

            // Assert
            sentBytes.Should().Be(expectedBytes, "应该发送正确的字节数");

            // 模拟回环测试
            mockPort.SimulateDataReceived(testText);
            
            var dataReceived = await dataCollector.WaitForEventsAsync(1, TimeSpan.FromSeconds(3));
            dataReceived.Should().BeTrue("应该接收到数据");

            var receivedEvent = dataCollector.GetFirstEvent();
            receivedEvent.Should().NotBeNull();
            
            var receivedText = Encoding.UTF8.GetString(receivedEvent!.Args.Data.RawData);
            receivedText.Should().Be(testText, "接收的文本应该与发送的文本一致");
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试极限波特率配置
    /// </summary>
    [Theory]
    [InlineData(300)]        // 极低波特率
    [InlineData(1200)]       // 低波特率
    [InlineData(9600)]       // 标准波特率
    [InlineData(115200)]     // 高波特率
    [InlineData(921600)]     // 极高波特率
    public async Task SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(int baudRate)
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_BAUD_RATE_TEST");
        configuration.BaudRate = baudRate;

        var service = CreateSerialPortService(configuration);

        try
        {
            // Act & Assert
            service.Configuration.BaudRate.Should().Be(baudRate, "配置的波特率应该正确");

            await service.OpenAsync();
            service.IsConnected.Should().BeTrue("应该能够以任何波特率打开连接");

            // 测试数据传输
            var testData = TestDataGenerator.GenerateTestData(100);
            var sentBytes = await service.SendAsync(testData);
            sentBytes.Should().Be(testData.Length, "应该能够正常发送数据");
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试极限超时配置
    /// </summary>
    [Theory]
    [InlineData(1)]          // 极短超时
    [InlineData(100)]        // 短超时
    [InlineData(30000)]      // 长超时
    [InlineData(int.MaxValue)] // 极长超时
    public async Task SerialPort_ExtremeTimeouts_ShouldRespectSettings(int timeoutMs)
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_TIMEOUT_EXTREME");
        configuration.ReadTimeout = timeoutMs;
        configuration.WriteTimeout = timeoutMs;

        var service = CreateSerialPortService(configuration);

        try
        {
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // Act & Assert
            service.Configuration.ReadTimeout.Should().Be(timeoutMs);
            service.Configuration.WriteTimeout.Should().Be(timeoutMs);

            // 测试实际超时行为（仅对合理的超时值）
            if (timeoutMs <= 10000) // 只测试10秒以内的超时
            {
                var buffer = new byte[1024];
                var startTime = DateTime.UtcNow;
                
                var bytesRead = await service.ReadAsync(buffer, 0, buffer.Length);
                var elapsed = DateTime.UtcNow - startTime;

                bytesRead.Should().Be(0, "没有数据时应该返回0");
                
                if (timeoutMs < int.MaxValue)
                {
                    elapsed.Should().BeLessThan(TimeSpan.FromMilliseconds(timeoutMs + 2000), 
                        "操作应该在超时时间内完成");
                }
            }
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试缓冲区边界条件
    /// </summary>
    [Theory]
    [InlineData(1)]          // 最小缓冲区
    [InlineData(512)]        // 小缓冲区
    [InlineData(4096)]       // 标准缓冲区
    [InlineData(65536)]      // 大缓冲区
    public async Task SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(int bufferSize)
    {
        // Arrange
        var configuration = TestDataGenerator.CreateAdvancedBufferingConfiguration("COM_BUFFER_BOUNDARY");
        configuration.ReceiveBufferSize = bufferSize;
        configuration.SendBufferSize = bufferSize;

        var service = CreateSerialPortService(configuration);

        try
        {
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // Act & Assert - 测试发送等于缓冲区大小的数据
            var bufferSizeData = TestDataGenerator.GenerateTestData(bufferSize);
            var sentBytes = await service.SendAsync(bufferSizeData);
            sentBytes.Should().Be(bufferSize, "应该能够发送等于缓冲区大小的数据");

            // 测试发送超过缓冲区大小的数据
            if (bufferSize < 32768) // 避免创建过大的测试数据
            {
                var oversizeData = TestDataGenerator.GenerateTestData(bufferSize + 1);
                var oversizeSentBytes = await service.SendAsync(oversizeData);
                oversizeSentBytes.Should().Be(bufferSize + 1, "应该能够发送超过缓冲区大小的数据");
            }
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试快速连接/断开循环
    /// </summary>
    [Fact]
    public async Task SerialPort_RapidConnectDisconnect_ShouldHandleStably()
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_RAPID_CYCLE");
        var service = CreateSerialPortService(configuration);

        try
        {
            // Act & Assert - 快速连接/断开循环
            for (int i = 0; i < 20; i++)
            {
                var openResult = await service.OpenAsync();
                openResult.Should().BeTrue($"第{i + 1}次打开应该成功");
                service.IsConnected.Should().BeTrue();

                // 发送少量数据
                var testData = TestDataGenerator.GenerateTestData(10);
                await service.SendAsync(testData);

                var closeResult = await service.CloseAsync();
                closeResult.Should().BeTrue($"第{i + 1}次关闭应该成功");
                service.IsConnected.Should().BeFalse();

                // 短暂延迟
                await Task.Delay(10);
            }

            // 验证最终状态
            service.IsConnected.Should().BeFalse("循环结束后应该处于断开状态");
            service.Status.ErrorCount.Should().Be(0, "快速循环不应该产生错误");
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试空字符串和null参数处理
    /// </summary>
    [Fact]
    public async Task SerialPort_NullAndEmptyParameters_ShouldHandleGracefully()
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_NULL_EMPTY");
        var service = CreateSerialPortService(configuration);

        try
        {
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // Act & Assert - null数据
            var nullDataAction = async () => await service.SendAsync(null!);
            await nullDataAction.Should().ThrowAsync<ArgumentNullException>("发送null数据应该抛出异常");

            // 空字符串
            var emptyStringAction = async () => await service.SendTextAsync("");
            await emptyStringAction.Should().ThrowAsync<ArgumentNullException>("发送空字符串应该抛出异常");

            // null字符串
            var nullStringAction = async () => await service.SendTextAsync(null!);
            await nullStringAction.Should().ThrowAsync<ArgumentNullException>("发送null字符串应该抛出异常");

            // null缓冲区读取
            var nullBufferAction = async () => await service.ReadAsync(null!, 0, 0);
            await nullBufferAction.Should().ThrowAsync<ArgumentNullException>("使用null缓冲区读取应该抛出异常");

            // 验证服务状态仍然正常
            service.IsConnected.Should().BeTrue("参数验证失败不应该影响连接状态");
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试编码边界条件
    /// </summary>
    [Theory]
    [InlineData("UTF-8")]
    [InlineData("ASCII")]
    [InlineData("UTF-16")]
    [InlineData("UTF-32")]
    public async Task SerialPort_DifferentEncodings_ShouldHandleCorrectly(string encodingName)
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_ENCODING_TEST");
        var service = CreateSerialPortService(configuration);
        var encoding = Encoding.GetEncoding(encodingName);

        try
        {
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // Act & Assert
            var testText = "Hello, 世界! 🌍";
            var sentBytes = await service.SendTextAsync(testText, encoding);
            var expectedBytes = encoding.GetByteCount(testText);

            sentBytes.Should().Be(expectedBytes, $"使用{encodingName}编码发送的字节数应该正确");
            service.Status.BytesSent.Should().Be(expectedBytes, "统计应该正确");
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }
}
