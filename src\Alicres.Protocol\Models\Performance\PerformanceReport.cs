using Alicres.Protocol.Interfaces;

namespace Alicres.Protocol.Models.Performance;

/// <summary>
/// 性能报告
/// </summary>
public class PerformanceReport
{
    /// <summary>
    /// 报告类型
    /// </summary>
    public PerformanceReportType ReportType { get; set; }

    /// <summary>
    /// 报告标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 报告生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 报告时间范围开始
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 报告时间范围结束
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 当前性能指标
    /// </summary>
    public PerformanceMetrics? CurrentMetrics { get; set; }

    /// <summary>
    /// 平均性能指标
    /// </summary>
    public PerformanceMetrics? AverageMetrics { get; set; }

    /// <summary>
    /// 峰值性能指标
    /// </summary>
    public PerformanceMetrics? PeakMetrics { get; set; }

    /// <summary>
    /// 性能趋势数据
    /// </summary>
    public List<PerformanceDataPoint> TrendData { get; set; } = new();

    /// <summary>
    /// 性能警告列表
    /// </summary>
    public List<PerformanceWarning> Warnings { get; set; } = new();

    /// <summary>
    /// 性能建议列表
    /// </summary>
    public List<string> Suggestions { get; set; } = new();

    /// <summary>
    /// 报告摘要
    /// </summary>
    public string Summary { get; set; } = string.Empty;

    /// <summary>
    /// 报告详细内容
    /// </summary>
    public string DetailedContent { get; set; } = string.Empty;

    /// <summary>
    /// 报告时间跨度
    /// </summary>
    public TimeSpan TimeSpan => EndTime - StartTime;

    /// <summary>
    /// 获取报告的文本表示
    /// </summary>
    /// <returns>报告文本</returns>
    public string GetReportText()
    {
        var report = new System.Text.StringBuilder();
        
        report.AppendLine($"=== {Title} ===");
        report.AppendLine($"报告类型: {ReportType}");
        report.AppendLine($"生成时间: {GeneratedAt:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine($"时间范围: {StartTime:yyyy-MM-dd HH:mm:ss} - {EndTime:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine($"时间跨度: {TimeSpan:dd\\.hh\\:mm\\:ss}");
        report.AppendLine();

        if (!string.IsNullOrEmpty(Summary))
        {
            report.AppendLine("=== 报告摘要 ===");
            report.AppendLine(Summary);
            report.AppendLine();
        }

        if (CurrentMetrics != null)
        {
            report.AppendLine("=== 当前性能指标 ===");
            report.AppendLine(CurrentMetrics.GetDetailedReport());
            report.AppendLine();
        }

        if (AverageMetrics != null)
        {
            report.AppendLine("=== 平均性能指标 ===");
            report.AppendLine(AverageMetrics.GetDetailedReport());
            report.AppendLine();
        }

        if (PeakMetrics != null)
        {
            report.AppendLine("=== 峰值性能指标 ===");
            report.AppendLine(PeakMetrics.GetDetailedReport());
            report.AppendLine();
        }

        if (Warnings.Count > 0)
        {
            report.AppendLine("=== 性能警告 ===");
            foreach (var warning in Warnings)
            {
                report.AppendLine($"- [{warning.Timestamp:HH:mm:ss}] {warning.Type}: {warning.Message}");
            }
            report.AppendLine();
        }

        if (Suggestions.Count > 0)
        {
            report.AppendLine("=== 性能建议 ===");
            foreach (var suggestion in Suggestions)
            {
                report.AppendLine($"- {suggestion}");
            }
            report.AppendLine();
        }

        if (TrendData.Count > 0)
        {
            report.AppendLine("=== 性能趋势数据 ===");
            report.AppendLine($"数据点数量: {TrendData.Count}");
            report.AppendLine("最近10个数据点:");
            var recentData = TrendData.TakeLast(10);
            foreach (var point in recentData)
            {
                report.AppendLine($"  [{point.Timestamp:HH:mm:ss}] " +
                    $"吞吐量: {point.ThroughputBytesPerSecond:F2} B/s, " +
                    $"延迟: {point.AverageLatencyMs:F2}ms, " +
                    $"错误率: {point.ErrorRatePercentage:F2}%");
            }
            report.AppendLine();
        }

        if (!string.IsNullOrEmpty(DetailedContent))
        {
            report.AppendLine("=== 详细内容 ===");
            report.AppendLine(DetailedContent);
        }

        return report.ToString();
    }

    /// <summary>
    /// 获取报告的 JSON 表示
    /// </summary>
    /// <returns>JSON 字符串</returns>
    public string GetReportJson()
    {
        return System.Text.Json.JsonSerializer.Serialize(this, new System.Text.Json.JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
        });
    }

    /// <summary>
    /// 添加性能警告
    /// </summary>
    /// <param name="type">警告类型</param>
    /// <param name="message">警告消息</param>
    /// <param name="timestamp">时间戳</param>
    public void AddWarning(string type, string message, DateTime? timestamp = null)
    {
        Warnings.Add(new PerformanceWarning
        {
            Type = type,
            Message = message,
            Timestamp = timestamp ?? DateTime.Now
        });
    }

    /// <summary>
    /// 添加性能建议
    /// </summary>
    /// <param name="suggestion">建议内容</param>
    public void AddSuggestion(string suggestion)
    {
        if (!string.IsNullOrWhiteSpace(suggestion))
        {
            Suggestions.Add(suggestion);
        }
    }

    /// <summary>
    /// 生成报告摘要
    /// </summary>
    public void GenerateSummary()
    {
        var summary = new System.Text.StringBuilder();
        
        if (CurrentMetrics != null)
        {
            summary.AppendLine($"当前系统性能等级: {CurrentMetrics.GetPerformanceGrade()}");
            summary.AppendLine($"数据吞吐量: {CurrentMetrics.ThroughputBytesPerSecond:F2} 字节/秒");
            summary.AppendLine($"平均延迟: {CurrentMetrics.AverageLatencyMs:F2} 毫秒");
            summary.AppendLine($"错误率: {CurrentMetrics.ErrorRatePercentage:F2}%");
        }

        if (Warnings.Count > 0)
        {
            summary.AppendLine($"性能警告数量: {Warnings.Count}");
        }

        if (TrendData.Count > 0)
        {
            summary.AppendLine($"监控数据点: {TrendData.Count} 个");
        }

        Summary = summary.ToString();
    }

    /// <summary>
    /// 创建实时报告
    /// </summary>
    /// <param name="metrics">当前性能指标</param>
    /// <returns>实时报告</returns>
    public static PerformanceReport CreateRealTimeReport(PerformanceMetrics metrics)
    {
        ArgumentNullException.ThrowIfNull(metrics);

        var report = new PerformanceReport
        {
            ReportType = PerformanceReportType.RealTime,
            Title = "实时性能报告",
            StartTime = metrics.StartTime,
            EndTime = metrics.LastUpdateTime,
            CurrentMetrics = metrics
        };

        report.Suggestions.AddRange(metrics.GetPerformanceSuggestions());
        report.GenerateSummary();

        return report;
    }

    /// <summary>
    /// 创建历史报告
    /// </summary>
    /// <param name="reportType">报告类型</param>
    /// <param name="history">历史数据</param>
    /// <returns>历史报告</returns>
    public static PerformanceReport CreateHistoryReport(PerformanceReportType reportType, PerformanceHistory history)
    {
        ArgumentNullException.ThrowIfNull(history);

        var report = new PerformanceReport
        {
            ReportType = reportType,
            Title = GetReportTitle(reportType),
            StartTime = history.StartTime,
            EndTime = history.EndTime,
            AverageMetrics = history.GetAverageMetrics(),
            PeakMetrics = history.GetPeakMetrics(),
            TrendData = history.DataPoints
        };

        if (report.AverageMetrics != null)
        {
            report.Suggestions.AddRange(report.AverageMetrics.GetPerformanceSuggestions());
        }

        report.GenerateSummary();

        return report;
    }

    /// <summary>
    /// 获取报告标题
    /// </summary>
    /// <param name="reportType">报告类型</param>
    /// <returns>报告标题</returns>
    private static string GetReportTitle(PerformanceReportType reportType)
    {
        return reportType switch
        {
            PerformanceReportType.RealTime => "实时性能报告",
            PerformanceReportType.Hourly => "小时性能报告",
            PerformanceReportType.Daily => "日性能报告",
            PerformanceReportType.Weekly => "周性能报告",
            PerformanceReportType.Monthly => "月性能报告",
            PerformanceReportType.Custom => "自定义性能报告",
            _ => "性能报告"
        };
    }
}

/// <summary>
/// 性能警告
/// </summary>
public class PerformanceWarning
{
    /// <summary>
    /// 警告类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 警告消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 警告时间
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 警告级别
    /// </summary>
    public WarningLevel Level { get; set; } = WarningLevel.Warning;
}

/// <summary>
/// 警告级别枚举
/// </summary>
public enum WarningLevel
{
    /// <summary>
    /// 信息
    /// </summary>
    Info,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 错误
    /// </summary>
    Error,

    /// <summary>
    /// 严重
    /// </summary>
    Critical
}
