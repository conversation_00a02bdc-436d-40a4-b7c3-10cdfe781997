# Alicres.SerialPort 快速入门指南

[![NuGet Version](https://img.shields.io/nuget/v/Alicres.SerialPort.svg)](https://www.nuget.org/packages/Alicres.SerialPort/)

本指南将帮助您快速上手 Alicres.SerialPort 串口通讯库，从安装到实现基本的串口通讯功能。

## 📋 目录

- [环境要求](#环境要求)
- [安装](#安装)
- [基本概念](#基本概念)
- [快速开始](#快速开始)
- [基础配置](#基础配置)
- [事件处理](#事件处理)
- [错误处理](#错误处理)
- [下一步](#下一步)

---

## 🔧 环境要求

### 系统要求
- **.NET 8.0** 或更高版本
- **Windows 10/11** 或 **Linux** 或 **macOS**
- 可用的串口设备（物理或虚拟）

### 开发环境
- **Visual Studio 2022** (推荐) 或 **VS Code**
- **NuGet 包管理器**
- **Git** (可选，用于获取示例代码)

### 硬件要求
- 串口设备或 USB 转串口适配器
- 测试用的串口设备（如传感器、单片机等）

---

## 📦 安装

### 使用 NuGet 包管理器

#### 方法 1: Package Manager Console
```powershell
Install-Package Alicres.SerialPort -Version 1.1.0
```

#### 方法 2: .NET CLI
```bash
dotnet add package Alicres.SerialPort --version 1.1.0
```

#### 方法 3: PackageReference (推荐)
在您的 `.csproj` 文件中添加：
```xml
<PackageReference Include="Alicres.SerialPort" Version="1.1.0" />
```

### 验证安装
创建一个简单的控制台应用程序来验证安装：

```csharp
using Alicres.SerialPort.Models;

Console.WriteLine("Alicres.SerialPort 安装成功！");
var config = new SerialPortConfiguration();
Console.WriteLine($"默认配置: {config.PortName} @ {config.BaudRate}");
```

---

## 💡 基本概念

### 核心组件

#### 1. SerialPortConfiguration
串口配置类，包含所有串口参数：
```csharp
var config = new SerialPortConfiguration
{
    PortName = "COM1",        // 串口名称
    BaudRate = 9600,          // 波特率
    DataBits = 8,             // 数据位
    StopBits = StopBits.One,  // 停止位
    Parity = Parity.None      // 校验位
};
```

#### 2. SerialPortService
核心服务类，提供串口操作功能：
```csharp
using var serialPort = new SerialPortService(config);
```

#### 3. SerialPortData
数据传输模型，支持多种数据格式：
```csharp
var data = new SerialPortData("Hello World");
var hexData = SerialPortData.FromHexString("48656C6C6F");
```

### 主要功能特性
- ✅ **异步操作** - 完全支持 async/await 模式
- ✅ **事件驱动** - 数据接收、状态变化、错误处理事件
- ✅ **自动重连** - 可配置的智能重连机制
- ✅ **数据格式** - 支持文本、十六进制、字节数组
- ✅ **流控制** - 硬件和软件流控制支持
- ✅ **性能监控** - 实时性能指标和统计

---

## 🚀 快速开始

### 示例 1: 基本串口通讯

```csharp
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Microsoft.Extensions.Logging;

// 创建日志记录器（可选）
using var loggerFactory = LoggerFactory.Create(builder => 
    builder.AddConsole().SetMinimumLevel(LogLevel.Information));
var logger = loggerFactory.CreateLogger<SerialPortService>();

// 创建串口配置
var config = new SerialPortConfiguration
{
    PortName = "COM1",              // 根据实际情况修改
    BaudRate = 9600,
    DataBits = 8,
    StopBits = StopBits.One,
    Parity = Parity.None,
    ReadTimeout = 1000,
    WriteTimeout = 1000
};

// 创建串口服务
using var serialPort = new SerialPortService(config, logger);

try
{
    // 打开串口
    await serialPort.OpenAsync();
    Console.WriteLine($"串口 {config.PortName} 已打开");

    // 发送数据
    await serialPort.SendAsync("Hello, Serial Port!");
    Console.WriteLine("数据已发送");

    // 等待一段时间以接收响应
    await Task.Delay(2000);
}
catch (Exception ex)
{
    Console.WriteLine($"错误: {ex.Message}");
}
finally
{
    // 关闭串口
    await serialPort.CloseAsync();
    Console.WriteLine("串口已关闭");
}
```

### 示例 2: 事件驱动的数据接收

```csharp
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;

var config = new SerialPortConfiguration
{
    PortName = "COM1",
    BaudRate = 9600
};

using var serialPort = new SerialPortService(config);

// 订阅数据接收事件
serialPort.DataReceived += (sender, e) =>
{
    Console.WriteLine($"接收到数据: {e.Data.ToText()}");
    Console.WriteLine($"十六进制: {e.Data.ToHexString()}");
    Console.WriteLine($"字节数: {e.Data.Length}");
};

// 订阅状态变化事件
serialPort.StatusChanged += (sender, e) =>
{
    Console.WriteLine($"状态变化: {e.PreviousStatus} -> {e.CurrentStatus}");
};

// 订阅错误事件
serialPort.ErrorOccurred += (sender, e) =>
{
    Console.WriteLine($"发生错误: {e.Exception.Message}");
};

try
{
    await serialPort.OpenAsync();
    
    // 发送测试数据
    await serialPort.SendAsync("AT\r\n");
    
    // 保持程序运行以接收数据
    Console.WriteLine("按任意键退出...");
    Console.ReadKey();
}
finally
{
    await serialPort.CloseAsync();
}
```

---

## ⚙️ 基础配置

### 常用配置选项

```csharp
var config = new SerialPortConfiguration
{
    // 基本串口参数
    PortName = "COM1",                    // 串口名称
    BaudRate = 9600,                      // 波特率
    DataBits = 8,                         // 数据位 (5-8)
    StopBits = StopBits.One,              // 停止位
    Parity = Parity.None,                 // 校验位
    
    // 超时设置
    ReadTimeout = 1000,                   // 读取超时 (毫秒)
    WriteTimeout = 1000,                  // 写入超时 (毫秒)
    
    // 缓冲区设置
    ReadBufferSize = 4096,                // 读取缓冲区大小
    WriteBufferSize = 2048,               // 写入缓冲区大小
    
    // 重连设置
    EnableAutoReconnect = true,           // 启用自动重连
    ReconnectInterval = 5000,             // 重连间隔 (毫秒)
    MaxReconnectAttempts = 3,             // 最大重连次数
    
    // 流控制设置 (1.1.0 新增)
    EnableFlowControl = false,            // 启用流控制
    FlowControlType = FlowControlType.None // 流控制类型
};
```

### 配置验证

```csharp
// 验证配置有效性
if (config.IsValid())
{
    Console.WriteLine("配置有效");
}
else
{
    Console.WriteLine("配置无效，请检查参数");
}

// 创建默认配置
var defaultConfig = SerialPortConfiguration.CreateDefault("COM1");
```

---

## 📡 事件处理

### 数据接收事件

```csharp
serialPort.DataReceived += (sender, e) =>
{
    var data = e.Data;
    var timestamp = e.Timestamp;
    
    // 处理文本数据
    if (data.Length > 0)
    {
        string text = data.ToText();
        Console.WriteLine($"[{timestamp:HH:mm:ss}] 接收: {text}");
    }
};
```

### 状态变化事件

```csharp
serialPort.StatusChanged += (sender, e) =>
{
    Console.WriteLine($"状态变化: {e.PreviousStatus} -> {e.CurrentStatus}");
    
    switch (e.CurrentStatus)
    {
        case SerialPortStatus.Connected:
            Console.WriteLine("串口已连接");
            break;
        case SerialPortStatus.Disconnected:
            Console.WriteLine("串口已断开");
            break;
        case SerialPortStatus.Reconnecting:
            Console.WriteLine("正在重连...");
            break;
    }
};
```

### 错误处理事件

```csharp
serialPort.ErrorOccurred += (sender, e) =>
{
    Console.WriteLine($"错误类型: {e.ErrorType}");
    Console.WriteLine($"错误消息: {e.Exception.Message}");
    
    // 根据错误类型采取不同的处理策略
    switch (e.ErrorType)
    {
        case SerialPortErrorType.ConnectionLost:
            Console.WriteLine("连接丢失，将尝试重连");
            break;
        case SerialPortErrorType.WriteTimeout:
            Console.WriteLine("写入超时");
            break;
        case SerialPortErrorType.ReadTimeout:
            Console.WriteLine("读取超时");
            break;
    }
};
```

---

## ❌ 错误处理

### 常见异常类型

```csharp
try
{
    await serialPort.OpenAsync();
}
catch (SerialPortConnectionException ex)
{
    Console.WriteLine($"连接错误: {ex.Message}");
    // 检查串口是否存在、是否被占用
}
catch (SerialPortConfigurationException ex)
{
    Console.WriteLine($"配置错误: {ex.Message}");
    // 检查配置参数是否正确
}
catch (SerialPortDataException ex)
{
    Console.WriteLine($"数据错误: {ex.Message}");
    // 检查数据格式是否正确
}
catch (Exception ex)
{
    Console.WriteLine($"未知错误: {ex.Message}");
}
```

### 最佳实践

```csharp
// 使用 using 语句确保资源释放
using var serialPort = new SerialPortService(config);

// 检查串口是否可用
var availablePorts = SerialPortService.GetAvailablePorts();
if (!availablePorts.Contains(config.PortName))
{
    Console.WriteLine($"串口 {config.PortName} 不可用");
    return;
}

// 设置合理的超时时间
config.ReadTimeout = 5000;   // 5 秒
config.WriteTimeout = 3000;  // 3 秒
```

---

## 🎯 下一步

恭喜！您已经掌握了 Alicres.SerialPort 的基础使用方法。接下来可以：

### 📚 深入学习
- [高级功能详解](advanced-features.md) - 学习缓冲管理、流控制、性能监控
- [示例代码集合](examples/) - 查看更多实际应用示例
- [故障排除指南](troubleshooting.md) - 解决常见问题

### 🔗 集成其他库
- [Alicres.Protocol 集成](../alicres-protocol/integration.md) - 与协议解析库协同使用

### 🛠️ 实际项目
- 创建一个简单的串口调试工具
- 实现设备数据采集程序
- 开发工业设备通讯应用

---

## 📞 获取帮助

如果您在使用过程中遇到问题：

- 📖 查看 [故障排除指南](troubleshooting.md)
- 🐛 提交 [Issue](https://gitee.com/liam-gitee/alicres/issues)
- 📚 查看 [API 文档](../../api/alicres-serialport.md)
- 💬 参与社区讨论

---

**下一篇**: [高级功能详解](advanced-features.md) →
