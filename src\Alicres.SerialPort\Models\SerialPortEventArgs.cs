using Alicres.SerialPort.Models;

namespace Alicres.SerialPort.Models;

/// <summary>
/// 数据接收事件参数
/// </summary>
public class SerialPortDataReceivedEventArgs : EventArgs
{
    /// <summary>
    /// 接收到的数据
    /// </summary>
    public SerialPortData Data { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="data">接收到的数据</param>
    public SerialPortDataReceivedEventArgs(SerialPortData data)
    {
        Data = data ?? throw new ArgumentNullException(nameof(data));
    }

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>字符串描述</returns>
    public override string ToString()
    {
        return $"数据接收事件: 端口 {Data.PortName}, 长度 {Data.Length} 字节, 方向 {Data.Direction}";
    }
}

/// <summary>
/// 状态变化事件参数
/// </summary>
public class SerialPortStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 之前的状态
    /// </summary>
    public SerialPortConnectionState PreviousState { get; }

    /// <summary>
    /// 当前状态
    /// </summary>
    public SerialPortConnectionState CurrentState { get; }

    /// <summary>
    /// 端口名称
    /// </summary>
    public string PortName { get; }

    /// <summary>
    /// 状态变化时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <param name="previousState">之前的状态</param>
    /// <param name="currentState">当前状态</param>
    public SerialPortStatusChangedEventArgs(string portName, SerialPortConnectionState previousState, SerialPortConnectionState currentState)
    {
        PortName = portName ?? throw new ArgumentNullException(nameof(portName));
        PreviousState = previousState;
        CurrentState = currentState;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] 端口 {PortName} 状态变化: {PreviousState} -> {CurrentState}";
    }
}

/// <summary>
/// 错误事件参数
/// </summary>
public class SerialPortErrorEventArgs : EventArgs
{
    /// <summary>
    /// 错误异常
    /// </summary>
    public Exception Exception { get; }

    /// <summary>
    /// 端口名称
    /// </summary>
    public string PortName { get; }

    /// <summary>
    /// 错误时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 错误级别
    /// </summary>
    public string ErrorLevel { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <param name="exception">错误异常</param>
    /// <param name="errorLevel">错误级别</param>
    public SerialPortErrorEventArgs(string portName, Exception exception, string errorLevel = "Error")
    {
        PortName = portName ?? throw new ArgumentNullException(nameof(portName));
        Exception = exception ?? throw new ArgumentNullException(nameof(exception));
        ErrorLevel = errorLevel ?? "Error";
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] 端口 {PortName} 发生 {ErrorLevel} 错误: {Exception.Message}";
    }
}
