using System.Collections.Concurrent;
using Alicres.SerialPort.Models;
using Microsoft.Extensions.Logging;

namespace Alicres.SerialPort.Services;

/// <summary>
/// 高级缓冲管理器，提供数据队列、溢出保护和自动清理功能
/// </summary>
public class AdvancedBufferManager : IDisposable
{
    private readonly SerialPortConfiguration _configuration;
    private readonly ILogger? _logger;
    private readonly ConcurrentQueue<SerialPortData> _dataQueue;
    private readonly Timer _cleanupTimer;
    private readonly object _lockObject = new();
    private bool _disposed;
    private long _totalBytesReceived;
    private long _totalBytesDropped;
    private DateTime _lastCleanupTime;

    /// <summary>
    /// 当前队列长度
    /// </summary>
    public int QueueLength => _dataQueue.Count;

    /// <summary>
    /// 队列使用率（百分比）
    /// </summary>
    public int QueueUsagePercentage => (int)((double)QueueLength / _configuration.DataQueueMaxLength * 100);

    /// <summary>
    /// 总接收字节数
    /// </summary>
    public long TotalBytesReceived => _totalBytesReceived;

    /// <summary>
    /// 总丢弃字节数
    /// </summary>
    public long TotalBytesDropped => _totalBytesDropped;

    /// <summary>
    /// 缓冲区警告事件
    /// </summary>
    public event EventHandler<BufferWarningEventArgs>? BufferWarning;

    /// <summary>
    /// 缓冲区溢出事件
    /// </summary>
    public event EventHandler<BufferOverflowEventArgs>? BufferOverflow;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">串口配置</param>
    /// <param name="logger">日志记录器</param>
    public AdvancedBufferManager(SerialPortConfiguration configuration, ILogger? logger = null)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger;
        _dataQueue = new ConcurrentQueue<SerialPortData>();
        _lastCleanupTime = DateTime.Now;

        // 创建清理定时器
        _cleanupTimer = new Timer(PerformCleanup, null, 
            TimeSpan.FromMilliseconds(_configuration.BufferCleanupInterval),
            TimeSpan.FromMilliseconds(_configuration.BufferCleanupInterval));

        _logger?.LogDebug("高级缓冲管理器已创建，队列最大长度: {MaxLength}, 清理间隔: {Interval}ms", 
            _configuration.DataQueueMaxLength, _configuration.BufferCleanupInterval);
    }

    /// <summary>
    /// 添加数据到队列
    /// </summary>
    /// <param name="data">串口数据</param>
    /// <returns>是否成功添加</returns>
    public bool EnqueueData(SerialPortData data)
    {
        if (_disposed)
            return false;

        ArgumentNullException.ThrowIfNull(data);

        lock (_lockObject)
        {
            Interlocked.Add(ref _totalBytesReceived, data.Length);

            // 检查队列是否已满
            if (_dataQueue.Count >= _configuration.DataQueueMaxLength)
            {
                return HandleBufferOverflow(data);
            }

            // 检查是否需要发出警告
            var usagePercentage = QueueUsagePercentage;
            if (usagePercentage >= _configuration.BufferWarningThreshold)
            {
                OnBufferWarning(new BufferWarningEventArgs(usagePercentage, _dataQueue.Count, _configuration.DataQueueMaxLength));
            }

            _dataQueue.Enqueue(data);
            _logger?.LogTrace("数据已添加到队列，当前长度: {Length}, 使用率: {Usage}%", 
                _dataQueue.Count, usagePercentage);

            return true;
        }
    }

    /// <summary>
    /// 从队列中取出数据
    /// </summary>
    /// <param name="data">输出的数据</param>
    /// <returns>是否成功取出数据</returns>
    public bool TryDequeueData(out SerialPortData? data)
    {
        if (_disposed)
        {
            data = null;
            return false;
        }

        var result = _dataQueue.TryDequeue(out data);
        if (result)
        {
            _logger?.LogTrace("数据已从队列中取出，剩余长度: {Length}", _dataQueue.Count);
        }

        return result;
    }

    /// <summary>
    /// 批量取出数据
    /// </summary>
    /// <param name="maxCount">最大取出数量</param>
    /// <returns>取出的数据列表</returns>
    public List<SerialPortData> DequeueBatch(int maxCount = 10)
    {
        if (_disposed)
            return new List<SerialPortData>();

        var result = new List<SerialPortData>();
        var count = 0;

        while (count < maxCount && _dataQueue.TryDequeue(out var data))
        {
            if (data != null)
            {
                result.Add(data);
                count++;
            }
        }

        if (result.Count > 0)
        {
            _logger?.LogTrace("批量取出 {Count} 个数据项，剩余队列长度: {Length}", 
                result.Count, _dataQueue.Count);
        }

        return result;
    }

    /// <summary>
    /// 清空队列
    /// </summary>
    public void ClearQueue()
    {
        if (_disposed)
            return;

        lock (_lockObject)
        {
            var count = _dataQueue.Count;
            while (_dataQueue.TryDequeue(out _)) { }

            _logger?.LogDebug("队列已清空，清除了 {Count} 个数据项", count);
        }
    }

    /// <summary>
    /// 获取缓冲区统计信息
    /// </summary>
    /// <returns>缓冲区统计信息</returns>
    public BufferStatistics GetStatistics()
    {
        return new BufferStatistics
        {
            QueueLength = QueueLength,
            QueueUsagePercentage = QueueUsagePercentage,
            MaxQueueLength = _configuration.DataQueueMaxLength,
            TotalBytesReceived = TotalBytesReceived,
            TotalBytesDropped = TotalBytesDropped,
            LastCleanupTime = _lastCleanupTime,
            OverflowStrategy = _configuration.BufferOverflowStrategy
        };
    }

    /// <summary>
    /// 处理缓冲区溢出
    /// </summary>
    /// <param name="newData">新数据</param>
    /// <returns>是否成功处理</returns>
    private bool HandleBufferOverflow(SerialPortData newData)
    {
        var overflowArgs = new BufferOverflowEventArgs(_dataQueue.Count, _configuration.DataQueueMaxLength, newData);
        OnBufferOverflow(overflowArgs);

        switch (_configuration.BufferOverflowStrategy)
        {
            case BufferOverflowStrategy.DropOldest:
                if (_dataQueue.TryDequeue(out var oldData))
                {
                    Interlocked.Add(ref _totalBytesDropped, oldData?.Length ?? 0);
                    _dataQueue.Enqueue(newData);
                    _logger?.LogWarning("缓冲区溢出，丢弃最旧数据，长度: {Length} 字节", oldData?.Length ?? 0);
                    return true;
                }
                break;

            case BufferOverflowStrategy.DropNewest:
                Interlocked.Add(ref _totalBytesDropped, newData.Length);
                _logger?.LogWarning("缓冲区溢出，丢弃最新数据，长度: {Length} 字节", newData.Length);
                return false;

            case BufferOverflowStrategy.ThrowException:
                throw new InvalidOperationException($"缓冲区溢出，当前队列长度: {_dataQueue.Count}");

            case BufferOverflowStrategy.Block:
                // 在实际应用中，这里应该实现阻塞逻辑
                _logger?.LogWarning("缓冲区溢出，阻塞策略暂不支持，丢弃数据");
                Interlocked.Add(ref _totalBytesDropped, newData.Length);
                return false;

            case BufferOverflowStrategy.Expand:
                // 在实际应用中，这里可以动态扩展队列大小
                _logger?.LogWarning("缓冲区溢出，扩展策略暂不支持，丢弃数据");
                Interlocked.Add(ref _totalBytesDropped, newData.Length);
                return false;
        }

        return false;
    }

    /// <summary>
    /// 执行清理操作
    /// </summary>
    /// <param name="state">状态对象</param>
    private void PerformCleanup(object? state)
    {
        if (_disposed)
            return;

        try
        {
            lock (_lockObject)
            {
                _lastCleanupTime = DateTime.Now;
                
                // 这里可以添加更多清理逻辑，比如清理过期数据等
                var queueLength = _dataQueue.Count;
                var usagePercentage = QueueUsagePercentage;

                _logger?.LogTrace("执行缓冲区清理，队列长度: {Length}, 使用率: {Usage}%", 
                    queueLength, usagePercentage);

                // 如果队列使用率过高，可以考虑清理一些数据
                if (usagePercentage > 90)
                {
                    _logger?.LogWarning("队列使用率过高: {Usage}%，考虑优化数据处理速度", usagePercentage);
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "执行缓冲区清理时发生错误");
        }
    }

    /// <summary>
    /// 触发缓冲区警告事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnBufferWarning(BufferWarningEventArgs e)
    {
        BufferWarning?.Invoke(this, e);
    }

    /// <summary>
    /// 触发缓冲区溢出事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnBufferOverflow(BufferOverflowEventArgs e)
    {
        BufferOverflow?.Invoke(this, e);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _cleanupTimer?.Dispose();
            ClearQueue();
            _disposed = true;
            _logger?.LogDebug("高级缓冲管理器已释放");
        }
    }
}
