using Alicres.Protocol.Interfaces;

namespace Alicres.Protocol.Models;

/// <summary>
/// 协议消息基类，提供所有协议消息的通用实现
/// </summary>
public abstract class ProtocolMessage : IProtocolMessage
{
    /// <summary>
    /// 消息唯一标识符
    /// </summary>
    public string MessageId { get; protected set; }

    /// <summary>
    /// 协议名称
    /// </summary>
    public abstract string ProtocolName { get; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public abstract string MessageType { get; }

    /// <summary>
    /// 消息时间戳
    /// </summary>
    public DateTime Timestamp { get; protected set; }

    /// <summary>
    /// 原始数据
    /// </summary>
    public byte[] RawData { get; protected set; }

    /// <summary>
    /// 消息是否有效
    /// </summary>
    public virtual bool IsValid => Validate();

    /// <summary>
    /// 构造函数
    /// </summary>
    protected ProtocolMessage()
    {
        MessageId = Guid.NewGuid().ToString("N")[..8]; // 使用8位短ID
        Timestamp = DateTime.Now;
        RawData = Array.Empty<byte>();
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="rawData">原始数据</param>
    protected ProtocolMessage(byte[] rawData) : this()
    {
        RawData = rawData ?? throw new ArgumentNullException(nameof(rawData));
    }

    /// <summary>
    /// 验证消息完整性
    /// </summary>
    /// <returns>如果消息有效返回 true，否则返回 false</returns>
    public virtual bool Validate()
    {
        // 子类可以重写此方法实现特定的验证逻辑
        return ValidateInternal();
    }

    /// <summary>
    /// 内部验证逻辑，由子类实现
    /// </summary>
    /// <returns>如果消息有效返回 true，否则返回 false</returns>
    protected virtual bool ValidateInternal()
    {
        return true;
    }

    /// <summary>
    /// 将消息序列化为字节数组
    /// </summary>
    /// <returns>序列化后的字节数组</returns>
    public virtual byte[] ToBytes()
    {
        // 默认返回原始数据的副本
        var result = new byte[RawData.Length];
        Array.Copy(RawData, result, RawData.Length);
        return result;
    }

    /// <summary>
    /// 获取消息的字符串表示
    /// </summary>
    /// <returns>消息的字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] {ProtocolName}.{MessageType} (ID: {MessageId}, Length: {RawData.Length} bytes)";
    }

    /// <summary>
    /// 获取消息的十六进制表示
    /// </summary>
    /// <param name="separator">分隔符</param>
    /// <param name="uppercase">是否使用大写</param>
    /// <returns>十六进制字符串</returns>
    public string ToHexString(string separator = " ", bool uppercase = true)
    {
        if (RawData.Length == 0)
            return string.Empty;

        var format = uppercase ? "X2" : "x2";
        return string.Join(separator, RawData.Select(b => b.ToString(format)));
    }

    /// <summary>
    /// 比较两个消息是否相等
    /// </summary>
    /// <param name="obj">要比较的对象</param>
    /// <returns>如果相等返回 true，否则返回 false</returns>
    public override bool Equals(object? obj)
    {
        if (obj is not ProtocolMessage other)
            return false;

        return MessageId == other.MessageId &&
               ProtocolName == other.ProtocolName &&
               MessageType == other.MessageType &&
               RawData.SequenceEqual(other.RawData);
    }

    /// <summary>
    /// 获取哈希码
    /// </summary>
    /// <returns>哈希码</returns>
    public override int GetHashCode()
    {
        return HashCode.Combine(MessageId, ProtocolName, MessageType, RawData.Length);
    }
}
