<Project>
  <PropertyGroup>
    <!-- 目标框架 -->
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>

    <!-- 包信息 -->
    <Authors>Alicres</Authors>
    <Company>Alicres</Company>
    <Copyright>Copyright © Alicres 2024</Copyright>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <RepositoryType>git</RepositoryType>
    <RepositoryUrl>https://gitee.com/liam-gitee/alicres.git</RepositoryUrl>

    <!-- 文档生成 -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>

    <!-- 代码分析 -->
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>

    <!-- 包生成 -->
    <GeneratePackageOnBuild Condition="'$(Configuration)' == 'Release'">true</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>

    <!-- 统一图标配置 -->
    <PackageIcon>icon.png</PackageIcon>
  </PropertyGroup>

  <!-- 全局图标文件包含 -->
  <ItemGroup>
    <None Include="$(MSBuildThisFileDirectory)src\icon.png" Pack="true" PackagePath="\" Condition="Exists('$(MSBuildThisFileDirectory)src\icon.png')" />
  </ItemGroup>
</Project>
