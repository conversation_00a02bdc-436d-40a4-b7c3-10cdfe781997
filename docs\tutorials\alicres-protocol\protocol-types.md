# Alicres.Protocol 协议类型详解

本文档详细介绍 Alicres.Protocol 支持的各种协议类型，包括内置的 Modbus 协议和自定义协议的实现方法。

## 📋 目录

- [Modbus RTU 协议](#modbus-rtu-协议)
- [Modbus ASCII 协议](#modbus-ascii-协议)
- [自定义协议框架](#自定义协议框架)
- [协议栈组合](#协议栈组合)
- [高级协议特性](#高级协议特性)

---

## 🏭 Modbus RTU 协议

### Modbus RTU 概述

Modbus RTU 是工业自动化中最常用的通讯协议之一，使用二进制格式传输数据，具有高效、可靠的特点。

### 基本使用示例

```csharp
using Alicres.Protocol.Modbus;
using Alicres.Protocol.Framing;
using Alicres.Protocol.Validators;

// 创建 Modbus RTU 协议处理器
var modbusRtu = new ModbusRtuProtocol();

// 配置帧处理和校验
var framing = new DelimiterFraming(); // Modbus RTU 通常不使用分隔符
var validator = new Crc16Validator();  // Modbus RTU 使用 CRC16 校验

// 创建读取保持寄存器请求 (功能码 03)
var readRequest = modbusRtu.CreateReadHoldingRegistersRequest(
    slaveId: 1,           // 从站地址
    startAddress: 0x0000, // 起始地址
    quantity: 10          // 读取数量
);

Console.WriteLine($"读取请求: {BitConverter.ToString(readRequest)}");

// 解析响应数据
var responseData = new byte[] 
{ 
    0x01, 0x03, 0x14,    // 从站地址, 功能码, 字节数
    0x00, 0x0A, 0x00, 0x0B, 0x00, 0x0C, 0x00, 0x0D, 0x00, 0x0E,
    0x00, 0x0F, 0x00, 0x10, 0x00, 0x11, 0x00, 0x12, 0x00, 0x13,
    0x85, 0xC6           // CRC16 校验码
};

if (validator.Validate(responseData))
{
    var parsedResponse = modbusRtu.ParseReadHoldingRegistersResponse(responseData);
    
    Console.WriteLine($"从站地址: {parsedResponse.SlaveId}");
    Console.WriteLine($"功能码: {parsedResponse.FunctionCode}");
    Console.WriteLine($"寄存器值: [{string.Join(", ", parsedResponse.RegisterValues)}]");
}
```

### Modbus RTU 功能码支持

```csharp
public class ModbusRtuExamples
{
    private readonly ModbusRtuProtocol _modbus = new();

    // 功能码 01: 读取线圈状态
    public byte[] ReadCoils(byte slaveId, ushort startAddress, ushort quantity)
    {
        return _modbus.CreateReadCoilsRequest(slaveId, startAddress, quantity);
    }

    // 功能码 02: 读取离散输入状态
    public byte[] ReadDiscreteInputs(byte slaveId, ushort startAddress, ushort quantity)
    {
        return _modbus.CreateReadDiscreteInputsRequest(slaveId, startAddress, quantity);
    }

    // 功能码 03: 读取保持寄存器
    public byte[] ReadHoldingRegisters(byte slaveId, ushort startAddress, ushort quantity)
    {
        return _modbus.CreateReadHoldingRegistersRequest(slaveId, startAddress, quantity);
    }

    // 功能码 04: 读取输入寄存器
    public byte[] ReadInputRegisters(byte slaveId, ushort startAddress, ushort quantity)
    {
        return _modbus.CreateReadInputRegistersRequest(slaveId, startAddress, quantity);
    }

    // 功能码 05: 写单个线圈
    public byte[] WriteSingleCoil(byte slaveId, ushort address, bool value)
    {
        return _modbus.CreateWriteSingleCoilRequest(slaveId, address, value);
    }

    // 功能码 06: 写单个寄存器
    public byte[] WriteSingleRegister(byte slaveId, ushort address, ushort value)
    {
        return _modbus.CreateWriteSingleRegisterRequest(slaveId, address, value);
    }

    // 功能码 15: 写多个线圈
    public byte[] WriteMultipleCoils(byte slaveId, ushort startAddress, bool[] values)
    {
        return _modbus.CreateWriteMultipleCoilsRequest(slaveId, startAddress, values);
    }

    // 功能码 16: 写多个寄存器
    public byte[] WriteMultipleRegisters(byte slaveId, ushort startAddress, ushort[] values)
    {
        return _modbus.CreateWriteMultipleRegistersRequest(slaveId, startAddress, values);
    }
}
```

### Modbus RTU 主站实现

```csharp
public class ModbusRtuMaster
{
    private readonly ITransportAdapter _transport;
    private readonly ModbusRtuProtocol _protocol;
    private readonly Crc16Validator _validator;
    private readonly SemaphoreSlim _requestLock = new(1, 1);

    public ModbusRtuMaster(ITransportAdapter transport)
    {
        _transport = transport;
        _protocol = new ModbusRtuProtocol();
        _validator = new Crc16Validator();
        
        _transport.DataReceived += OnDataReceived;
    }

    public async Task<ushort[]> ReadHoldingRegistersAsync(
        byte slaveId, ushort startAddress, ushort quantity, 
        CancellationToken cancellationToken = default)
    {
        await _requestLock.WaitAsync(cancellationToken);
        try
        {
            // 创建请求
            var request = _protocol.CreateReadHoldingRegistersRequest(
                slaveId, startAddress, quantity);

            // 发送请求
            await _transport.SendAsync(request);

            // 等待响应
            var response = await WaitForResponse(slaveId, 0x03, cancellationToken);

            // 解析响应
            if (_validator.Validate(response))
            {
                var parsedResponse = _protocol.ParseReadHoldingRegistersResponse(response);
                return parsedResponse.RegisterValues;
            }
            else
            {
                throw new InvalidDataException("响应数据校验失败");
            }
        }
        finally
        {
            _requestLock.Release();
        }
    }

    public async Task WriteSingleRegisterAsync(
        byte slaveId, ushort address, ushort value,
        CancellationToken cancellationToken = default)
    {
        await _requestLock.WaitAsync(cancellationToken);
        try
        {
            var request = _protocol.CreateWriteSingleRegisterRequest(slaveId, address, value);
            await _transport.SendAsync(request);

            var response = await WaitForResponse(slaveId, 0x06, cancellationToken);
            
            if (!_validator.Validate(response))
            {
                throw new InvalidDataException("写入操作响应校验失败");
            }

            // 验证写入是否成功
            var parsedResponse = _protocol.ParseWriteSingleRegisterResponse(response);
            if (parsedResponse.Address != address || parsedResponse.Value != value)
            {
                throw new InvalidOperationException("写入操作失败");
            }
        }
        finally
        {
            _requestLock.Release();
        }
    }

    private readonly Dictionary<string, TaskCompletionSource<byte[]>> _pendingResponses = new();

    private void OnDataReceived(object sender, byte[] data)
    {
        if (data.Length < 4) return; // 最小 Modbus 帧长度

        var slaveId = data[0];
        var functionCode = data[1];
        var key = $"{slaveId}:{functionCode}";

        if (_pendingResponses.TryGetValue(key, out var tcs))
        {
            _pendingResponses.Remove(key);
            tcs.SetResult(data);
        }
    }

    private async Task<byte[]> WaitForResponse(
        byte slaveId, byte functionCode, 
        CancellationToken cancellationToken)
    {
        var key = $"{slaveId}:{functionCode}";
        var tcs = new TaskCompletionSource<byte[]>();
        
        _pendingResponses[key] = tcs;

        try
        {
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, timeoutCts.Token);

            combinedCts.Token.Register(() => 
            {
                _pendingResponses.Remove(key);
                tcs.TrySetCanceled();
            });

            return await tcs.Task;
        }
        catch (OperationCanceledException)
        {
            _pendingResponses.Remove(key);
            throw new TimeoutException("等待 Modbus 响应超时");
        }
    }
}
```

---

## 📝 Modbus ASCII 协议

### Modbus ASCII 概述

Modbus ASCII 使用 ASCII 字符传输数据，具有良好的可读性和调试便利性，适用于需要人工监控的场合。

### 基本使用示例

```csharp
using Alicres.Protocol.Modbus;

// 创建 Modbus ASCII 协议处理器
var modbusAscii = new ModbusAsciiProtocol();

// 创建读取保持寄存器请求
var readRequest = modbusAscii.CreateReadHoldingRegistersRequest(
    slaveId: 1,
    startAddress: 0x0000,
    quantity: 4
);

// Modbus ASCII 帧格式: :010300000004FA\r\n
Console.WriteLine($"ASCII 请求: {Encoding.ASCII.GetString(readRequest)}");

// 解析 ASCII 响应
var asciiResponse = ":01030800010002000300040079\r\n";
var responseBytes = Encoding.ASCII.GetBytes(asciiResponse);

var parsedResponse = modbusAscii.ParseReadHoldingRegistersResponse(responseBytes);
Console.WriteLine($"寄存器值: [{string.Join(", ", parsedResponse.RegisterValues)}]");
```

### ASCII 帧处理

```csharp
public class ModbusAsciiFrameHandler
{
    private readonly ModbusAsciiProtocol _protocol = new();
    private readonly StringBuilder _frameBuffer = new();

    public List<byte[]> ProcessIncomingData(byte[] data)
    {
        var frames = new List<byte[]>();
        var text = Encoding.ASCII.GetString(data);

        foreach (char c in text)
        {
            if (c == ':') // 帧开始
            {
                _frameBuffer.Clear();
                _frameBuffer.Append(c);
            }
            else if (c == '\n') // 帧结束
            {
                _frameBuffer.Append('\r').Append('\n');
                
                var frameText = _frameBuffer.ToString();
                if (IsValidAsciiFrame(frameText))
                {
                    frames.Add(Encoding.ASCII.GetBytes(frameText));
                }
                
                _frameBuffer.Clear();
            }
            else if (_frameBuffer.Length > 0)
            {
                _frameBuffer.Append(c);
            }
        }

        return frames;
    }

    private bool IsValidAsciiFrame(string frame)
    {
        // 检查帧格式: :AAAA...AACC\r\n
        if (frame.Length < 9) return false; // 最小长度
        if (!frame.StartsWith(":")) return false;
        if (!frame.EndsWith("\r\n")) return false;
        
        // 检查是否都是有效的十六进制字符
        var hexPart = frame.Substring(1, frame.Length - 3);
        return hexPart.All(c => "0123456789ABCDEF".Contains(char.ToUpper(c)));
    }
}
```

---

## 🔧 自定义协议框架

### 实现自定义协议

```csharp
// 定义自定义协议接口
public interface ICustomProtocol
{
    byte[] CreateRequest(byte command, byte[] data);
    CustomResponse ParseResponse(byte[] data);
    bool ValidateFrame(byte[] frame);
}

// 实现自定义协议
public class CustomIndustrialProtocol : ICustomProtocol
{
    private const byte STX = 0x02; // 帧开始
    private const byte ETX = 0x03; // 帧结束
    private const byte ACK = 0x06; // 确认
    private const byte NAK = 0x15; // 否认

    public byte[] CreateRequest(byte command, byte[] data)
    {
        var frame = new List<byte>
        {
            STX,           // 帧开始
            command,       // 命令字节
            (byte)data.Length // 数据长度
        };
        
        frame.AddRange(data);
        
        // 计算校验和
        byte checksum = CalculateChecksum(frame.Skip(1).ToArray());
        frame.Add(checksum);
        frame.Add(ETX);

        return frame.ToArray();
    }

    public CustomResponse ParseResponse(byte[] data)
    {
        if (data.Length < 5) // 最小帧长度
            throw new ArgumentException("数据长度不足");

        if (data[0] != STX)
            throw new ArgumentException("无效的帧开始标志");

        if (data[data.Length - 1] != ETX)
            throw new ArgumentException("无效的帧结束标志");

        var response = new CustomResponse
        {
            Status = data[1],
            DataLength = data[2],
            Data = new byte[data[2]]
        };

        Array.Copy(data, 3, response.Data, 0, response.DataLength);
        response.Checksum = data[data.Length - 2];

        // 验证校验和
        var calculatedChecksum = CalculateChecksum(data.Skip(1).Take(data.Length - 3).ToArray());
        response.IsValid = (calculatedChecksum == response.Checksum);

        return response;
    }

    public bool ValidateFrame(byte[] frame)
    {
        if (frame.Length < 5) return false;
        if (frame[0] != STX || frame[frame.Length - 1] != ETX) return false;

        var dataLength = frame[2];
        if (frame.Length != 5 + dataLength) return false;

        var calculatedChecksum = CalculateChecksum(frame.Skip(1).Take(frame.Length - 3).ToArray());
        var frameChecksum = frame[frame.Length - 2];

        return calculatedChecksum == frameChecksum;
    }

    private byte CalculateChecksum(byte[] data)
    {
        byte checksum = 0;
        foreach (byte b in data)
        {
            checksum ^= b;
        }
        return checksum;
    }
}

public class CustomResponse
{
    public byte Status { get; set; }
    public byte DataLength { get; set; }
    public byte[] Data { get; set; }
    public byte Checksum { get; set; }
    public bool IsValid { get; set; }
}
```

### 自定义协议使用示例

```csharp
public class CustomProtocolExample
{
    private readonly CustomIndustrialProtocol _protocol = new();
    private readonly ITransportAdapter _transport;

    public CustomProtocolExample(ITransportAdapter transport)
    {
        _transport = transport;
        _transport.DataReceived += OnDataReceived;
    }

    public async Task SendCommandAsync(byte command, byte[] data)
    {
        var request = _protocol.CreateRequest(command, data);
        
        Console.WriteLine($"发送命令: {BitConverter.ToString(request)}");
        await _transport.SendAsync(request);
    }

    private void OnDataReceived(object sender, byte[] data)
    {
        try
        {
            if (_protocol.ValidateFrame(data))
            {
                var response = _protocol.ParseResponse(data);
                
                Console.WriteLine($"接收到响应:");
                Console.WriteLine($"  状态: 0x{response.Status:X2}");
                Console.WriteLine($"  数据: {BitConverter.ToString(response.Data)}");
                Console.WriteLine($"  有效性: {response.IsValid}");

                ProcessResponse(response);
            }
            else
            {
                Console.WriteLine("接收到无效帧");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"解析响应失败: {ex.Message}");
        }
    }

    private void ProcessResponse(CustomResponse response)
    {
        switch (response.Status)
        {
            case 0x06: // ACK
                Console.WriteLine("命令执行成功");
                break;
            case 0x15: // NAK
                Console.WriteLine("命令执行失败");
                break;
            default:
                Console.WriteLine($"未知状态码: 0x{response.Status:X2}");
                break;
        }
    }

    public async Task RunExample()
    {
        await _transport.OpenAsync();

        // 发送一些测试命令
        await SendCommandAsync(0x01, new byte[] { 0x12, 0x34 });
        await Task.Delay(1000);

        await SendCommandAsync(0x02, new byte[] { 0x56, 0x78, 0x9A });
        await Task.Delay(1000);

        await _transport.CloseAsync();
    }
}
```

---

## 🔗 协议栈组合

### 多层协议处理

```csharp
public class ProtocolStack
{
    private readonly IMessageFraming _framing;
    private readonly IDataValidator _validator;
    private readonly ICustomProtocol _protocol;
    private readonly ITransportAdapter _transport;

    public ProtocolStack(
        ITransportAdapter transport,
        IMessageFraming framing,
        IDataValidator validator,
        ICustomProtocol protocol)
    {
        _transport = transport;
        _framing = framing;
        _validator = validator;
        _protocol = protocol;

        _transport.DataReceived += ProcessIncomingData;
    }

    public async Task SendDataAsync(byte command, byte[] data)
    {
        // 1. 应用层: 创建协议数据
        var protocolData = _protocol.CreateRequest(command, data);

        // 2. 数据链路层: 添加校验
        var validatedData = _validator.AddChecksum(protocolData);

        // 3. 物理层: 添加帧格式
        var framedData = _framing.FrameMessage(validatedData);

        // 4. 传输层: 发送数据
        await _transport.SendAsync(framedData);
    }

    private void ProcessIncomingData(object sender, byte[] rawData)
    {
        try
        {
            // 1. 物理层: 提取帧
            var frames = _framing.ProcessIncomingData(rawData);

            foreach (var frame in frames)
            {
                // 2. 数据链路层: 验证校验
                if (_validator.Validate(frame))
                {
                    var protocolData = _validator.RemoveChecksum(frame);

                    // 3. 应用层: 解析协议
                    if (_protocol.ValidateFrame(protocolData))
                    {
                        var response = _protocol.ParseResponse(protocolData);
                        OnDataProcessed(response);
                    }
                }
                else
                {
                    Console.WriteLine("数据校验失败");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"数据处理错误: {ex.Message}");
        }
    }

    public event EventHandler<CustomResponse> DataProcessed;

    protected virtual void OnDataProcessed(CustomResponse response)
    {
        DataProcessed?.Invoke(this, response);
    }
}
```

### 协议栈使用示例

```csharp
public class ProtocolStackExample
{
    public static async Task RunExample()
    {
        // 创建传输层
        var serialConfig = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 9600
        };
        var serialPort = new SerialPortService(serialConfig);
        var transport = new SerialPortTransportAdapter(serialPort);

        // 创建协议栈
        var framing = new DelimiterFraming(0x0D, 0x0A);
        var validator = new Crc16Validator();
        var protocol = new CustomIndustrialProtocol();

        var protocolStack = new ProtocolStack(transport, framing, validator, protocol);

        // 订阅数据处理事件
        protocolStack.DataProcessed += (sender, response) =>
        {
            Console.WriteLine($"协议栈处理完成: 状态={response.Status:X2}");
        };

        // 打开连接并发送数据
        await transport.OpenAsync();

        await protocolStack.SendDataAsync(0x01, new byte[] { 0x12, 0x34 });
        await Task.Delay(2000);

        await transport.CloseAsync();
    }
}
```

---

## 🚀 高级协议特性

### 协议版本管理

```csharp
public class VersionedProtocol
{
    private readonly Dictionary<byte, ICustomProtocol> _protocolVersions = new();

    public void RegisterProtocolVersion(byte version, ICustomProtocol protocol)
    {
        _protocolVersions[version] = protocol;
    }

    public byte[] CreateVersionedRequest(byte version, byte command, byte[] data)
    {
        if (!_protocolVersions.TryGetValue(version, out var protocol))
        {
            throw new NotSupportedException($"不支持的协议版本: {version}");
        }

        var protocolData = protocol.CreateRequest(command, data);
        
        // 在协议数据前添加版本号
        var versionedData = new byte[protocolData.Length + 1];
        versionedData[0] = version;
        Array.Copy(protocolData, 0, versionedData, 1, protocolData.Length);

        return versionedData;
    }

    public object ParseVersionedResponse(byte[] data)
    {
        if (data.Length < 1)
            throw new ArgumentException("数据长度不足");

        var version = data[0];
        var protocolData = new byte[data.Length - 1];
        Array.Copy(data, 1, protocolData, 0, protocolData.Length);

        if (!_protocolVersions.TryGetValue(version, out var protocol))
        {
            throw new NotSupportedException($"不支持的协议版本: {version}");
        }

        return protocol.ParseResponse(protocolData);
    }
}
```

### 协议性能监控

```csharp
public class ProtocolPerformanceMonitor
{
    private readonly Dictionary<string, ProtocolMetrics> _metrics = new();
    private readonly Timer _reportTimer;

    public ProtocolPerformanceMonitor()
    {
        _reportTimer = new Timer(GenerateReport, null, 
            TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    public void RecordRequest(string protocolName, int dataSize)
    {
        GetMetrics(protocolName).RecordRequest(dataSize);
    }

    public void RecordResponse(string protocolName, int dataSize, TimeSpan responseTime)
    {
        GetMetrics(protocolName).RecordResponse(dataSize, responseTime);
    }

    public void RecordError(string protocolName, string errorType)
    {
        GetMetrics(protocolName).RecordError(errorType);
    }

    private ProtocolMetrics GetMetrics(string protocolName)
    {
        if (!_metrics.TryGetValue(protocolName, out var metrics))
        {
            metrics = new ProtocolMetrics(protocolName);
            _metrics[protocolName] = metrics;
        }
        return metrics;
    }

    private void GenerateReport(object state)
    {
        Console.WriteLine("=== 协议性能报告 ===");
        foreach (var kvp in _metrics)
        {
            var metrics = kvp.Value;
            Console.WriteLine($"协议: {metrics.ProtocolName}");
            Console.WriteLine($"  请求数: {metrics.RequestCount}");
            Console.WriteLine($"  响应数: {metrics.ResponseCount}");
            Console.WriteLine($"  平均响应时间: {metrics.AverageResponseTime.TotalMilliseconds:F1} ms");
            Console.WriteLine($"  错误数: {metrics.ErrorCount}");
            Console.WriteLine($"  成功率: {metrics.SuccessRate:F1}%");
            Console.WriteLine();
        }
    }
}

public class ProtocolMetrics
{
    public string ProtocolName { get; }
    public int RequestCount { get; private set; }
    public int ResponseCount { get; private set; }
    public int ErrorCount { get; private set; }
    public TimeSpan TotalResponseTime { get; private set; }
    
    public ProtocolMetrics(string protocolName)
    {
        ProtocolName = protocolName;
    }

    public void RecordRequest(int dataSize)
    {
        RequestCount++;
    }

    public void RecordResponse(int dataSize, TimeSpan responseTime)
    {
        ResponseCount++;
        TotalResponseTime = TotalResponseTime.Add(responseTime);
    }

    public void RecordError(string errorType)
    {
        ErrorCount++;
    }

    public TimeSpan AverageResponseTime => 
        ResponseCount > 0 ? TimeSpan.FromTicks(TotalResponseTime.Ticks / ResponseCount) : TimeSpan.Zero;

    public double SuccessRate => 
        RequestCount > 0 ? (double)ResponseCount / RequestCount * 100 : 0;
}
```

---

**相关文档**:
- [快速入门指南](getting-started.md)
- [集成指南](integration.md)
- [示例代码集合](examples/)
