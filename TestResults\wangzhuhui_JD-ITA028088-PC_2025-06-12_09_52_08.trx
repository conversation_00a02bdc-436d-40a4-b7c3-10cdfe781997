﻿<?xml version="1.0" encoding="utf-8"?>
<TestRun id="b2df5509-1561-45ce-94ba-1a7d85524f3d" name="wa<PERSON><PERSON><PERSON><PERSON>@JD-ITA028088-PC 2025-06-12 09:52:08" runUser="AUXGROUP\wangzhuhui" xmlns="http://microsoft.com/schemas/VisualStudio/TeamTest/2010">
  <Times creation="2025-06-12T09:52:08.4621114+08:00" queuing="2025-06-12T09:52:08.4621116+08:00" start="2025-06-12T09:52:06.9765831+08:00" finish="2025-06-12T09:52:08.5364533+08:00" />
  <TestSettings name="default" id="a5a8c2b4-233b-4448-ac91-3b5bb086e845">
    <Deployment runDeploymentRoot="wangzhuhui_JD-ITA028088-PC_2025-06-12_09_52_08" />
  </TestSettings>
  <Results>
    <UnitTestResult executionId="31d90c2d-9448-49ea-9b03-7dbf3646ac21" testId="ceb4348a-7f39-5209-f0c5-47a92f0ccf52" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithMultipleItems_ShouldReturnCorrectCount" computerName="JD-ITA028088-PC" duration="00:00:00.0006637" startTime="2025-06-12T09:52:08.4578460+08:00" endTime="2025-06-12T09:52:08.4578461+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="31d90c2d-9448-49ea-9b03-7dbf3646ac21" />
    <UnitTestResult executionId="dd9a8120-28f0-4b6e-8850-76bb903e0eb6" testId="4fb7a94f-b5ad-a202-33ad-c14f2afe94a5" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48-65-6C-6C-6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" computerName="JD-ITA028088-PC" duration="00:00:00.0000840" startTime="2025-06-12T09:52:08.4522544+08:00" endTime="2025-06-12T09:52:08.4522545+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="dd9a8120-28f0-4b6e-8850-76bb903e0eb6" />
    <UnitTestResult executionId="fa24df87-2d38-497a-b47c-226524585332" testId="40f0c48a-0bc6-7691-1bcb-bf225bd03b25" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 3000, maxAttempts: 5, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000043" startTime="2025-06-12T09:52:08.4488316+08:00" endTime="2025-06-12T09:52:08.4488317+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fa24df87-2d38-497a-b47c-226524585332" />
    <UnitTestResult executionId="8a6d579f-3a1f-4ba2-a175-3e489658c80a" testId="de7a5bdb-03df-5bef-5375-0d9de7fb33cd" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48 65 6C 6C 6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" computerName="JD-ITA028088-PC" duration="00:00:00.0002980" startTime="2025-06-12T09:52:08.4520484+08:00" endTime="2025-06-12T09:52:08.4520484+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8a6d579f-3a1f-4ba2-a175-3e489658c80a" />
    <UnitTestResult executionId="ec803ad9-1e0d-4cd3-8a32-e9f4a574c40b" testId="35f2d5bf-cf12-732f-0809-1a84cafae57a" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;&quot;, expectedBytes: [])" computerName="JD-ITA028088-PC" duration="00:00:00.0002306" startTime="2025-06-12T09:52:08.4518479+08:00" endTime="2025-06-12T09:52:08.4518480+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ec803ad9-1e0d-4cd3-8a32-e9f4a574c40b" />
    <UnitTestResult executionId="0567ab5e-a2d4-4fdc-b869-aeecaeeca907" testId="1fc3a25a-3320-5e0f-9e3e-b9761c8f4549" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithNullOrWhiteSpace_ShouldReturnEmptyData" computerName="JD-ITA028088-PC" duration="00:00:00.0013718" startTime="2025-06-12T09:52:08.4527506+08:00" endTime="2025-06-12T09:52:08.4527507+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0567ab5e-a2d4-4fdc-b869-aeecaeeca907" />
    <UnitTestResult executionId="0f928ada-588a-432a-98f5-605c14262bf5" testId="68fa167d-968d-2375-d00a-3a724af909d8" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;:&quot;, uppercase: False, expectedHex: &quot;48:65:6c&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0001283" startTime="2025-06-12T09:52:08.4548120+08:00" endTime="2025-06-12T09:52:08.4548120+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0f928ada-588a-432a-98f5-605c14262bf5" />
    <UnitTestResult executionId="8f24a948-51f0-4a81-9b86-f53019d2e781" testId="0acc92da-f9c6-47d9-c5a2-760ab204265e" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 6, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000041" startTime="2025-06-12T09:52:08.4203881+08:00" endTime="2025-06-12T09:52:08.4203890+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8f24a948-51f0-4a81-9b86-f53019d2e781" />
    <UnitTestResult executionId="57ee19a0-6e77-4ce4-ad82-d56baf312910" testId="3b43f857-4c69-124a-f1e5-4526c8f72ed1" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 0, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000049" startTime="2025-06-12T09:52:08.4427239+08:00" endTime="2025-06-12T09:52:08.4427240+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="57ee19a0-6e77-4ce4-ad82-d56baf312910" />
    <UnitTestResult executionId="fdc2b886-4b70-4dd1-9c8a-57f9b19a33e4" testId="d0e94aff-761a-b752-8ea1-b05e263e6baa" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithMoreRequestedThanAvailable_ShouldReturnAllAvailable" computerName="JD-ITA028088-PC" duration="00:00:00.0001914" startTime="2025-06-12T09:52:08.4597029+08:00" endTime="2025-06-12T09:52:08.4597030+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fdc2b886-4b70-4dd1-9c8a-57f9b19a33e4" />
    <UnitTestResult executionId="6f0b83b5-8acd-42bf-850d-50b89b051be7" testId="3f1e1745-b426-f5fa-fc4d-7c36c8ed8987" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 4096, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000109" startTime="2025-06-12T09:52:08.4424805+08:00" endTime="2025-06-12T09:52:08.4424806+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6f0b83b5-8acd-42bf-850d-50b89b051be7" />
    <UnitTestResult executionId="d3040952-6794-4d91-b526-0d93fa0cadb7" testId="7c790003-2d63-4b93-840e-28312682c36d" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;&quot;, expectedText: &quot;&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0002625" startTime="2025-06-12T09:52:08.4530238+08:00" endTime="2025-06-12T09:52:08.4530238+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d3040952-6794-4d91-b526-0d93fa0cadb7" />
    <UnitTestResult executionId="fe7604a3-34d7-4a73-a3df-b98a06d5b007" testId="ae04b4e0-cf08-4697-35c4-f48e7c5131c5" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;你好&quot;, expectedText: &quot;你好&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0000071" startTime="2025-06-12T09:52:08.4541426+08:00" endTime="2025-06-12T09:52:08.4541427+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fe7604a3-34d7-4a73-a3df-b98a06d5b007" />
    <UnitTestResult executionId="9c388f4e-685e-43bf-929c-2dc8fc557f9f" testId="44298843-f215-2386-e136-f2a25ffedf0e" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: 1000, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000059" startTime="2025-06-12T09:52:08.4434782+08:00" endTime="2025-06-12T09:52:08.4434783+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9c388f4e-685e-43bf-929c-2dc8fc557f9f" />
    <UnitTestResult executionId="45d62ee6-12eb-48d2-bb29-9b7d4cbeca1f" testId="37b241c9-a28c-3630-63d5-22833c81cd74" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.CreateDefault_WithEmptyPortName_ShouldReturnInvalidConfiguration" computerName="JD-ITA028088-PC" duration="00:00:00.0001416" startTime="2025-06-12T09:52:08.4514924+08:00" endTime="2025-06-12T09:52:08.4514925+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="45d62ee6-12eb-48d2-bb29-9b7d4cbeca1f" />
    <UnitTestResult executionId="43ef0c38-2ee6-4a63-9dce-c0e659b7c6bb" testId="d42f65dc-b483-693d-547e-03998b4ce142" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;COM2&quot;, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000056" startTime="2025-06-12T09:52:08.4495929+08:00" endTime="2025-06-12T09:52:08.4495930+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="43ef0c38-2ee6-4a63-9dce-c0e659b7c6bb" />
    <UnitTestResult executionId="13c1c962-9f93-4ec9-9b91-3670367f1a2a" testId="31b4aa6a-561a-ec27-c6d2-f1426e253b35" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithValidData_ShouldAddToQueue" computerName="JD-ITA028088-PC" duration="00:00:00.0002251" startTime="2025-06-12T09:52:08.4576341+08:00" endTime="2025-06-12T09:52:08.4576342+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="13c1c962-9f93-4ec9-9b91-3670367f1a2a" />
    <UnitTestResult executionId="19123914-2246-4643-bce1-9450996499f4" testId="03bac210-5121-4552-6b4a-8ad9c8a4430a" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.Constructor_WithNullConfiguration_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0004753" startTime="2025-06-12T09:52:08.4590483+08:00" endTime="2025-06-12T09:52:08.4590484+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="19123914-2246-4643-bce1-9450996499f4" />
    <UnitTestResult executionId="a8337ecd-7b20-477c-b847-063946eabbb4" testId="ee4c97f9-aba3-7fd3-4eff-c0ae32af980a" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToString_ShouldReturnFormattedString" computerName="JD-ITA028088-PC" duration="00:00:00.0012101" startTime="2025-06-12T09:52:08.4543557+08:00" endTime="2025-06-12T09:52:08.4543558+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a8337ecd-7b20-477c-b847-063946eabbb4" />
    <UnitTestResult executionId="68c94aff-c3dc-4d50-a0e4-5dab4b729164" testId="54219804-c561-b035-0838-e2a41e644606" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [255, 0, 171], expectedHex: &quot;FF 00 AB&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0026139" startTime="2025-06-12T09:52:08.4462545+08:00" endTime="2025-06-12T09:52:08.4462546+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="68c94aff-c3dc-4d50-a0e4-5dab4b729164" />
    <UnitTestResult executionId="ef11da1b-f4e0-49b1-bac5-6fe2dd00be07" testId="14c24c6f-1bd1-8a0b-0fdb-8dccb26c718e" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.GetStatistics_ShouldReturnCorrectInformation" computerName="JD-ITA028088-PC" duration="00:00:00.0008900" startTime="2025-06-12T09:52:08.4601423+08:00" endTime="2025-06-12T09:52:08.4601423+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ef11da1b-f4e0-49b1-bac5-6fe2dd00be07" />
    <UnitTestResult executionId="7317bc32-b506-49fd-999a-f8a4dbb20e1f" testId="38e1a48a-8126-3cc7-e8ac-60f91e67cb3d" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 9600, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0001856" startTime="2025-06-12T09:52:08.4455614+08:00" endTime="2025-06-12T09:52:08.4455615+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7317bc32-b506-49fd-999a-f8a4dbb20e1f" />
    <UnitTestResult executionId="2895acd1-46b5-42db-8b18-d3ee10af4976" testId="5772937f-dba3-ddd7-fc6d-303fd2ade221" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: null, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000025" startTime="2025-06-12T09:52:08.4498361+08:00" endTime="2025-06-12T09:52:08.4498362+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2895acd1-46b5-42db-8b18-d3ee10af4976" />
    <UnitTestResult executionId="c8aa1632-f8b6-4909-961f-d556e874b01c" testId="700480ab-f9a3-c14c-3862-c09cc3b0f4a7" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WhenNearCapacity_ShouldTriggerWarningEvent" computerName="JD-ITA028088-PC" duration="00:00:00.0005338" startTime="2025-06-12T09:52:08.4594883+08:00" endTime="2025-06-12T09:52:08.4594883+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c8aa1632-f8b6-4909-961f-d556e874b01c" />
    <UnitTestResult executionId="be35d40b-a7e4-4642-a4d8-248f4ff94427" testId="fc0f03d5-65c5-740b-47d9-a78e15ea1d9c" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48656C6C6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" computerName="JD-ITA028088-PC" duration="00:00:00.0000564" startTime="2025-06-12T09:52:08.4524580+08:00" endTime="2025-06-12T09:52:08.4524581+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="be35d40b-a7e4-4642-a4d8-248f4ff94427" />
    <UnitTestResult executionId="b0a09051-22f7-4966-800b-39784144eb86" testId="1110dce6-cdc2-edbd-83e1-76acc885583d" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.Constructor_WithValidConfiguration_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001628" startTime="2025-06-12T09:52:08.4580552+08:00" endTime="2025-06-12T09:52:08.4580553+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b0a09051-22f7-4966-800b-39784144eb86" />
    <UnitTestResult executionId="f9aae8ab-d75f-4dc5-bc7e-d315d68ecaa7" testId="bfdab111-1736-9094-751d-b681895868a8" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;   &quot;, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001018" startTime="2025-06-12T09:52:08.4490964+08:00" endTime="2025-06-12T09:52:08.4490965+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f9aae8ab-d75f-4dc5-bc7e-d315d68ecaa7" />
    <UnitTestResult executionId="68e6d515-b673-421b-87dc-2e2e9fdce721" testId="5168b9bd-ef9a-5e12-e4e4-606e2d42d0c0" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_Default_ShouldInitializeWithEmptyData" computerName="JD-ITA028088-PC" duration="00:00:00.0002593" startTime="2025-06-12T09:52:08.4568499+08:00" endTime="2025-06-12T09:52:08.4568500+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="68e6d515-b673-421b-87dc-2e2e9fdce721" />
    <UnitTestResult executionId="cfd5d9b8-a3f4-4e87-ae22-b573530b678a" testId="456b7034-8cd0-175d-c38d-784450059613" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 115200, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000050" startTime="2025-06-12T09:52:08.4460253+08:00" endTime="2025-06-12T09:52:08.4460254+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cfd5d9b8-a3f4-4e87-ae22-b573530b678a" />
    <UnitTestResult executionId="638040db-85bf-4c3e-86f0-46447fbc6770" testId="ac6b9c02-26c7-4e3d-e30f-fe45601d8bad" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(strategy: ThrowException)" computerName="JD-ITA028088-PC" duration="00:00:00.0423984" startTime="2025-06-12T09:52:08.4572033+08:00" endTime="2025-06-12T09:52:08.4572034+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="638040db-85bf-4c3e-86f0-46447fbc6770" />
    <UnitTestResult executionId="6c4d3735-fc1a-4227-be98-4bf3ddc6519c" testId="f543d2e9-4ced-9d03-4307-68823ad67ec5" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithByteArray_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0007530" startTime="2025-06-12T09:52:08.4557070+08:00" endTime="2025-06-12T09:52:08.4557072+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6c4d3735-fc1a-4227-be98-4bf3ddc6519c" />
    <UnitTestResult executionId="48826994-4f45-414b-8dc7-4d927451b925" testId="011894a4-ce24-7e54-f1d5-7f0ac286b44b" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.QueueUsagePercentage_ShouldCalculateCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001631" startTime="2025-06-12T09:52:08.4592744+08:00" endTime="2025-06-12T09:52:08.4592745+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="48826994-4f45-414b-8dc7-4d927451b925" />
    <UnitTestResult executionId="b4af8f5c-d0ac-4fb0-a54c-54e8760deacd" testId="d095da48-d2a1-b869-306b-68ec74fb6a86" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 8, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000083" startTime="2025-06-12T09:52:08.4200245+08:00" endTime="2025-06-12T09:52:08.4200246+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b4af8f5c-d0ac-4fb0-a54c-54e8760deacd" />
    <UnitTestResult executionId="994c655d-97eb-4edb-aef0-574ebbe154a6" testId="c029ac10-2236-0f3e-f243-4fb9689c404b" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 4, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000056" startTime="2025-06-12T09:52:08.4207502+08:00" endTime="2025-06-12T09:52:08.4207508+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="994c655d-97eb-4edb-aef0-574ebbe154a6" />
    <UnitTestResult executionId="74a06d99-744f-4bae-968b-d4a8cff03982" testId="2f010a59-e386-1c48-7a96-6eaf300b4a00" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.GetDataCopy_ShouldReturnIndependentCopy" computerName="JD-ITA028088-PC" duration="00:00:00.0007975" startTime="2025-06-12T09:52:08.4545869+08:00" endTime="2025-06-12T09:52:08.4545870+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="74a06d99-744f-4bae-968b-d4a8cff03982" />
    <UnitTestResult executionId="ca1d2d60-0c86-4565-ab1e-962731a63e63" testId="a81de412-4838-6623-61fa-67ddc3188238" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;Hello&quot;, expectedText: &quot;Hello&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0001238" startTime="2025-06-12T09:52:08.4539250+08:00" endTime="2025-06-12T09:52:08.4539251+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ca1d2d60-0c86-4565-ab1e-962731a63e63" />
    <UnitTestResult executionId="d2c2101f-a279-4115-a512-28beef946eeb" testId="abc2d729-2b39-bae1-e7d9-85d1bd974a73" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;COM1&quot;, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000018" startTime="2025-06-12T09:52:08.4500439+08:00" endTime="2025-06-12T09:52:08.4500439+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d2c2101f-a279-4115-a512-28beef946eeb" />
    <UnitTestResult executionId="0536ebcf-3369-44e3-bf3e-fd12c9e0f2b6" testId="2660b25c-0ab3-7226-1818-3de3d93f7e8b" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: -1, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0002435" startTime="2025-06-12T09:52:08.4419633+08:00" endTime="2025-06-12T09:52:08.4419634+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0536ebcf-3369-44e3-bf3e-fd12c9e0f2b6" />
    <UnitTestResult executionId="eeae05a0-6287-4873-bf74-1f8098281c27" testId="86618079-abf9-30c9-2492-2a4b2efcc170" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithNullText_ShouldInitializeWithEmptyData" computerName="JD-ITA028088-PC" duration="00:00:00.0001153" startTime="2025-06-12T09:52:08.4570592+08:00" endTime="2025-06-12T09:52:08.4570593+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="eeae05a0-6287-4873-bf74-1f8098281c27" />
    <UnitTestResult executionId="af0f2c6d-daf6-4a06-b2d0-3f946ad550cf" testId="fcb4374f-81fa-4153-7c7a-49168b68b53b" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;&quot;, uppercase: True, expectedHex: &quot;48656C&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0000096" startTime="2025-06-12T09:52:08.4552331+08:00" endTime="2025-06-12T09:52:08.4552332+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="af0f2c6d-daf6-4a06-b2d0-3f946ad550cf" />
    <UnitTestResult executionId="94a18988-3703-4895-8634-4a57b92d3284" testId="db250e33-9c97-4756-436a-16d6e8cf414f" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: 0, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0001786" startTime="2025-06-12T09:52:08.4432357+08:00" endTime="2025-06-12T09:52:08.4432358+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="94a18988-3703-4895-8634-4a57b92d3284" />
    <UnitTestResult executionId="ed26f225-b745-4a18-8f1f-4160063c0fac" testId="ccb6e43b-ed87-41fb-aacf-8532bac21ec1" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(strategy: DropNewest)" computerName="JD-ITA028088-PC" duration="00:00:00.0009064" startTime="2025-06-12T09:52:08.4574218+08:00" endTime="2025-06-12T09:52:08.4574219+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ed26f225-b745-4a18-8f1f-4160063c0fac" />
    <UnitTestResult executionId="1e4227eb-b839-4469-a575-c1f2ed6c0a69" testId="df64e4db-ecba-2113-fb43-2182e90a55d5" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.TryDequeueData_WithEmptyQueue_ShouldReturnFalse" computerName="JD-ITA028088-PC" duration="00:00:00.0002038" startTime="2025-06-12T09:52:08.4603593+08:00" endTime="2025-06-12T09:52:08.4603594+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1e4227eb-b839-4469-a575-c1f2ed6c0a69" />
    <UnitTestResult executionId="54198639-f49d-4300-aee5-3c4e97b2b616" testId="c34da281-7d78-d48e-fd36-b17e7d8c79e7" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [], expectedHex: &quot;&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0001249" startTime="2025-06-12T09:52:08.4467121+08:00" endTime="2025-06-12T09:52:08.4467122+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="54198639-f49d-4300-aee5-3c4e97b2b616" />
    <UnitTestResult executionId="3da31921-a9a9-4c0e-adb9-3d004870be75" testId="29cc443b-5843-75d0-329d-fdd89478ba0f" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithNullData_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0004520" startTime="2025-06-12T09:52:08.4605696+08:00" endTime="2025-06-12T09:52:08.4605697+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3da31921-a9a9-4c0e-adb9-3d004870be75" />
    <UnitTestResult executionId="f58c39e4-2321-4339-aa0b-a25fde5f961f" testId="8c638f55-8f9a-3b1d-dff8-8169a30d369f" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 5, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0055720" startTime="2025-06-12T09:52:08.4125208+08:00" endTime="2025-06-12T09:52:08.4125822+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f58c39e4-2321-4339-aa0b-a25fde5f961f" />
    <UnitTestResult executionId="5e2f053d-e9fa-426e-b4c6-d0d361ca584c" testId="faacff99-6157-5e13-4b0f-4d4468903363" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48:65:6C:6C:6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" computerName="JD-ITA028088-PC" duration="00:00:00.0195167" startTime="2025-06-12T09:52:08.4516365+08:00" endTime="2025-06-12T09:52:08.4516366+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5e2f053d-e9fa-426e-b4c6-d0d361ca584c" />
    <UnitTestResult executionId="191cdfda-bf54-439c-9dd8-c359e8229456" testId="f0dd822e-110a-9423-89f0-2a6bc0510f84" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 1024, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0001518" startTime="2025-06-12T09:52:08.4414689+08:00" endTime="2025-06-12T09:52:08.4414690+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="191cdfda-bf54-439c-9dd8-c359e8229456" />
    <UnitTestResult executionId="59fa376d-ff01-4b4a-b48f-18635236be7d" testId="ed73d661-31f4-564a-3bca-e7bd1da12bc7" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [72, 101, 108, 108, 111], expectedHex: &quot;48 65 6C 6C 6F&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0000968" startTime="2025-06-12T09:52:08.4469657+08:00" endTime="2025-06-12T09:52:08.4469658+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="59fa376d-ff01-4b4a-b48f-18635236be7d" />
    <UnitTestResult executionId="3abf323f-ce2a-4cbb-a74e-c7b22a98c1c9" testId="d8f02cf7-433b-61ec-c449-566982f978ff" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithNullByteArray_ShouldInitializeWithEmptyData" computerName="JD-ITA028088-PC" duration="00:00:00.0001607" startTime="2025-06-12T09:52:08.4554476+08:00" endTime="2025-06-12T09:52:08.4554477+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3abf323f-ce2a-4cbb-a74e-c7b22a98c1c9" />
    <UnitTestResult executionId="b8b21815-e1ba-43bb-aea9-d79dd680d262" testId="969900f1-f9c4-1336-3ebc-d975003306b5" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.CreateDefault_ShouldReturnValidConfiguration" computerName="JD-ITA028088-PC" duration="00:00:00.0016702" startTime="2025-06-12T09:52:08.4464684+08:00" endTime="2025-06-12T09:52:08.4464685+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b8b21815-e1ba-43bb-aea9-d79dd680d262" />
    <UnitTestResult executionId="e0c80af1-a941-41d4-99c6-e44ce78ad18b" testId="cf6e81ed-2d36-4cb5-7d19-3207ec4a1e6b" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 0, maxAttempts: 0, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0001767" startTime="2025-06-12T09:52:08.4479046+08:00" endTime="2025-06-12T09:52:08.4479047+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e0c80af1-a941-41d4-99c6-e44ce78ad18b" />
    <UnitTestResult executionId="8ac1744d-05b2-403c-bcb0-121e8315d16c" testId="a33546b2-5014-1109-76f2-708037dcb465" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithText_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0005282" startTime="2025-06-12T09:52:08.4566287+08:00" endTime="2025-06-12T09:52:08.4566288+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8ac1744d-05b2-403c-bcb0-121e8315d16c" />
    <UnitTestResult executionId="40f87d66-7c89-431a-b28e-d29b79bb7908" testId="1e4bdd15-076b-9af7-45c3-530c1fecf022" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.Constructor_ShouldInitializeWithDefaultValues" computerName="JD-ITA028088-PC" duration="00:00:00.0032309" startTime="2025-06-12T09:52:08.4512608+08:00" endTime="2025-06-12T09:52:08.4512609+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="40f87d66-7c89-431a-b28e-d29b79bb7908" />
    <UnitTestResult executionId="a751fec9-2798-4e63-9555-d59081079aad" testId="d915a464-2e21-829b-f426-6772ddf55055" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ClearQueue_WithDataInQueue_ShouldEmptyQueue" computerName="JD-ITA028088-PC" duration="00:00:00.0001376" startTime="2025-06-12T09:52:08.4599182+08:00" endTime="2025-06-12T09:52:08.4599182+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a751fec9-2798-4e63-9555-d59081079aad" />
    <UnitTestResult executionId="0636d393-e71e-48fc-9445-32469f1863b6" testId="53a79683-6e18-bdcc-2942-0ae631602802" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 7, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000041" startTime="2025-06-12T09:52:08.4210600+08:00" endTime="2025-06-12T09:52:08.4210601+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0636d393-e71e-48fc-9445-32469f1863b6" />
    <UnitTestResult executionId="efe46473-4f62-4b18-910f-59108b2126dc" testId="5fcd1008-efe6-9593-2351-b4703f8be1f5" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WhenQueueFull_ShouldTriggerOverflowEvent" computerName="JD-ITA028088-PC" duration="00:00:00.0003647" startTime="2025-06-12T09:52:08.4607881+08:00" endTime="2025-06-12T09:52:08.4607881+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="efe46473-4f62-4b18-910f-59108b2126dc" />
    <UnitTestResult executionId="d281f9cc-2e54-4022-bf44-0247ffe485cb" testId="8927fc38-5352-5a92-ac17-c0b53db402c5" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;&quot;, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001436" startTime="2025-06-12T09:52:08.4493254+08:00" endTime="2025-06-12T09:52:08.4493255+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d281f9cc-2e54-4022-bf44-0247ffe485cb" />
    <UnitTestResult executionId="22eb96d4-be53-4fee-bfa2-db82a075aa12" testId="33597ab1-9d34-65c5-b0a1-f1a694190e93" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: -1, maxAttempts: 0, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000064" startTime="2025-06-12T09:52:08.4484582+08:00" endTime="2025-06-12T09:52:08.4484582+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="22eb96d4-be53-4fee-bfa2-db82a075aa12" />
    <UnitTestResult executionId="22fa4d18-ae3a-4e1c-85ae-eda5c29b5177" testId="10ecc65a-1d98-44c2-5793-97f0d54a583d" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: -1, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001852" startTime="2025-06-12T09:52:08.4453165+08:00" endTime="2025-06-12T09:52:08.4453166+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="22fa4d18-ae3a-4e1c-85ae-eda5c29b5177" />
    <UnitTestResult executionId="89b54f00-20e1-4432-9e8a-006e785277a8" testId="09ce9de4-34a5-edec-6b47-7870a0ac8152" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: -1, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0002219" startTime="2025-06-12T09:52:08.4429878+08:00" endTime="2025-06-12T09:52:08.4429879+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="89b54f00-20e1-4432-9e8a-006e785277a8" />
    <UnitTestResult executionId="beaedd90-dc05-4034-ad88-e92875d7e372" testId="a70006d6-2422-b7b2-466f-65fda27fd805" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.TryDequeueData_WithDataInQueue_ShouldReturnData" computerName="JD-ITA028088-PC" duration="00:00:00.0003299" startTime="2025-06-12T09:52:08.4582588+08:00" endTime="2025-06-12T09:52:08.4582588+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="beaedd90-dc05-4034-ad88-e92875d7e372" />
    <UnitTestResult executionId="1a71e24f-10e8-4797-b2b7-a12cf96e5af7" testId="c783c277-08d3-0e65-e123-287a21c8b95a" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 0, maxAttempts: -1, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001781" startTime="2025-06-12T09:52:08.4482010+08:00" endTime="2025-06-12T09:52:08.4482011+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1a71e24f-10e8-4797-b2b7-a12cf96e5af7" />
    <UnitTestResult executionId="82f6f55f-6538-428f-ac94-d1c7635dbe80" testId="bfffdd04-e7b6-b0a1-e537-a650f2f9df27" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 9, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001533" startTime="2025-06-12T09:52:08.4190022+08:00" endTime="2025-06-12T09:52:08.4190024+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="82f6f55f-6538-428f-ac94-d1c7635dbe80" />
    <UnitTestResult executionId="5af24054-3544-4b74-b005-abf58e048480" testId="0e388063-0d1d-fee8-e535-f6ea426ca6a0" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithInvalidHex_ShouldThrowException(invalidHex: &quot;GG&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0003658" startTime="2025-06-12T09:52:08.4421179+08:00" endTime="2025-06-12T09:52:08.4421180+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5af24054-3544-4b74-b005-abf58e048480" />
    <UnitTestResult executionId="f7fd25ff-0d4b-4eb6-8127-654d27c98b51" testId="c53401be-8da1-5d73-868a-6420974db0e4" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;-&quot;, uppercase: True, expectedHex: &quot;48-65-6C&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0001674" startTime="2025-06-12T09:52:08.4550231+08:00" endTime="2025-06-12T09:52:08.4550231+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f7fd25ff-0d4b-4eb6-8127-654d27c98b51" />
    <UnitTestResult executionId="a5127d1d-0776-4cb0-895f-d8eb8a7955b4" testId="d796aa36-66a9-9e1e-199e-e7f8579e7312" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 0, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000071" startTime="2025-06-12T09:52:08.4458051+08:00" endTime="2025-06-12T09:52:08.4458052+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a5127d1d-0776-4cb0-895f-d8eb8a7955b4" />
    <UnitTestResult executionId="5fb50384-40cc-4b15-81e7-da61d2134dd8" testId="5dbf9991-d128-405c-e119-2c5c8fb571d8" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithInvalidHex_ShouldThrowException(invalidHex: &quot;48 65 6C 6C 6&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0083464" startTime="2025-06-12T09:52:08.4213833+08:00" endTime="2025-06-12T09:52:08.4213834+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5fb50384-40cc-4b15-81e7-da61d2134dd8" />
  </Results>
  <TestDefinitions>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 9, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bfffdd04-e7b6-b0a1-e537-a650f2f9df27">
      <Execution id="82f6f55f-6538-428f-ac94-d1c7635dbe80" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithNullByteArray_ShouldInitializeWithEmptyData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d8f02cf7-433b-61ec-c449-566982f978ff">
      <Execution id="3abf323f-ce2a-4cbb-a74e-c7b22a98c1c9" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_WithNullByteArray_ShouldInitializeWithEmptyData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;-&quot;, uppercase: True, expectedHex: &quot;48-65-6C&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c53401be-8da1-5d73-868a-6420974db0e4">
      <Execution id="f7fd25ff-0d4b-4eb6-8127-654d27c98b51" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WhenNearCapacity_ShouldTriggerWarningEvent" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="700480ab-f9a3-c14c-3862-c09cc3b0f4a7">
      <Execution id="c8aa1632-f8b6-4909-961f-d556e874b01c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WhenNearCapacity_ShouldTriggerWarningEvent" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48 65 6C 6C 6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="de7a5bdb-03df-5bef-5375-0d9de7fb33cd">
      <Execution id="8a6d579f-3a1f-4ba2-a175-3e489658c80a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.CreateDefault_WithEmptyPortName_ShouldReturnInvalidConfiguration" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="37b241c9-a28c-3630-63d5-22833c81cd74">
      <Execution id="45d62ee6-12eb-48d2-bb29-9b7d4cbeca1f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="CreateDefault_WithEmptyPortName_ShouldReturnInvalidConfiguration" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.QueueUsagePercentage_ShouldCalculateCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="011894a4-ce24-7e54-f1d5-7f0ac286b44b">
      <Execution id="48826994-4f45-414b-8dc7-4d927451b925" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="QueueUsagePercentage_ShouldCalculateCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 3000, maxAttempts: 5, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="40f0c48a-0bc6-7691-1bcb-bf225bd03b25">
      <Execution id="fa24df87-2d38-497a-b47c-226524585332" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithReconnectSettings_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [255, 0, 171], expectedHex: &quot;FF 00 AB&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="54219804-c561-b035-0838-e2a41e644606">
      <Execution id="68c94aff-c3dc-4d50-a0e4-5dab4b729164" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToString_ShouldReturnFormattedString" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ee4c97f9-aba3-7fd3-4eff-c0ae32af980a">
      <Execution id="a8337ecd-7b20-477c-b847-063946eabbb4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToString_ShouldReturnFormattedString" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.TryDequeueData_WithEmptyQueue_ShouldReturnFalse" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="df64e4db-ecba-2113-fb43-2182e90a55d5">
      <Execution id="1e4227eb-b839-4469-a575-c1f2ed6c0a69" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="TryDequeueData_WithEmptyQueue_ShouldReturnFalse" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithNullOrWhiteSpace_ShouldReturnEmptyData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1fc3a25a-3320-5e0f-9e3e-b9761c8f4549">
      <Execution id="0567ab5e-a2d4-4fdc-b869-aeecaeeca907" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithNullOrWhiteSpace_ShouldReturnEmptyData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48656C6C6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fc0f03d5-65c5-740b-47d9-a78e15ea1d9c">
      <Execution id="be35d40b-a7e4-4642-a4d8-248f4ff94427" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: -1, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2660b25c-0ab3-7226-1818-3de3d93f7e8b">
      <Execution id="0536ebcf-3369-44e3-bf3e-fd12c9e0f2b6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBufferSizes_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;&quot;, expectedBytes: [])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="35f2d5bf-cf12-732f-0809-1a84cafae57a">
      <Execution id="ec803ad9-1e0d-4cd3-8a32-e9f4a574c40b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 7, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="53a79683-6e18-bdcc-2942-0ae631602802">
      <Execution id="0636d393-e71e-48fc-9445-32469f1863b6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.CreateDefault_ShouldReturnValidConfiguration" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="969900f1-f9c4-1336-3ebc-d975003306b5">
      <Execution id="b8b21815-e1ba-43bb-aea9-d79dd680d262" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="CreateDefault_ShouldReturnValidConfiguration" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;COM1&quot;, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="abc2d729-2b39-bae1-e7d9-85d1bd974a73">
      <Execution id="d2c2101f-a279-4115-a512-28beef946eeb" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithInvalidHex_ShouldThrowException(invalidHex: &quot;48 65 6C 6C 6&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5dbf9991-d128-405c-e119-2c5c8fb571d8">
      <Execution id="5fb50384-40cc-4b15-81e7-da61d2134dd8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithInvalidHex_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: -1, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="10ecc65a-1d98-44c2-5793-97f0d54a583d">
      <Execution id="22fa4d18-ae3a-4e1c-85ae-eda5c29b5177" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBaudRate_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_Default_ShouldInitializeWithEmptyData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5168b9bd-ef9a-5e12-e4e4-606e2d42d0c0">
      <Execution id="68e6d515-b673-421b-87dc-2e2e9fdce721" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_Default_ShouldInitializeWithEmptyData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ClearQueue_WithDataInQueue_ShouldEmptyQueue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d915a464-2e21-829b-f426-6772ddf55055">
      <Execution id="a751fec9-2798-4e63-9555-d59081079aad" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="ClearQueue_WithDataInQueue_ShouldEmptyQueue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 4096, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3f1e1745-b426-f5fa-fc4d-7c36c8ed8987">
      <Execution id="6f0b83b5-8acd-42bf-850d-50b89b051be7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBufferSizes_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;&quot;, uppercase: True, expectedHex: &quot;48656C&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fcb4374f-81fa-4153-7c7a-49168b68b53b">
      <Execution id="af0f2c6d-daf6-4a06-b2d0-3f946ad550cf" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 0, maxAttempts: -1, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c783c277-08d3-0e65-e123-287a21c8b95a">
      <Execution id="1a71e24f-10e8-4797-b2b7-a12cf96e5af7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithReconnectSettings_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: 0, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="db250e33-9c97-4756-436a-16d6e8cf414f">
      <Execution id="94a18988-3703-4895-8634-4a57b92d3284" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithTimeouts_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 1024, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f0dd822e-110a-9423-89f0-2a6bc0510f84">
      <Execution id="191cdfda-bf54-439c-9dd8-c359e8229456" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBufferSizes_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: -1, maxAttempts: 0, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="33597ab1-9d34-65c5-b0a1-f1a694190e93">
      <Execution id="22eb96d4-be53-4fee-bfa2-db82a075aa12" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithReconnectSettings_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithNullText_ShouldInitializeWithEmptyData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="86618079-abf9-30c9-2492-2a4b2efcc170">
      <Execution id="eeae05a0-6287-4873-bf74-1f8098281c27" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_WithNullText_ShouldInitializeWithEmptyData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(strategy: ThrowException)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ac6b9c02-26c7-4e3d-e30f-fe45601d8bad">
      <Execution id="638040db-85bf-4c3e-86f0-46447fbc6770" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.Constructor_WithValidConfiguration_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1110dce6-cdc2-edbd-83e1-76acc885583d">
      <Execution id="b0a09051-22f7-4966-800b-39784144eb86" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="Constructor_WithValidConfiguration_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;&quot;, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8927fc38-5352-5a92-ac17-c0b53db402c5">
      <Execution id="d281f9cc-2e54-4022-bf44-0247ffe485cb" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithText_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a33546b2-5014-1109-76f2-708037dcb465">
      <Execution id="8ac1744d-05b2-403c-bcb0-121e8315d16c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_WithText_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 6, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0acc92da-f9c6-47d9-c5a2-760ab204265e">
      <Execution id="8f24a948-51f0-4a81-9b86-f53019d2e781" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithInvalidHex_ShouldThrowException(invalidHex: &quot;GG&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0e388063-0d1d-fee8-e535-f6ea426ca6a0">
      <Execution id="5af24054-3544-4b74-b005-abf58e048480" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithInvalidHex_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 9600, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="38e1a48a-8126-3cc7-e8ac-60f91e67cb3d">
      <Execution id="7317bc32-b506-49fd-999a-f8a4dbb20e1f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBaudRate_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: null, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5772937f-dba3-ddd7-fc6d-303fd2ade221">
      <Execution id="2895acd1-46b5-42db-8b18-d3ee10af4976" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;你好&quot;, expectedText: &quot;你好&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ae04b4e0-cf08-4697-35c4-f48e7c5131c5">
      <Execution id="fe7604a3-34d7-4a73-a3df-b98a06d5b007" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToText_WithUTF8Encoding_ShouldReturnCorrectText" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 5, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8c638f55-8f9a-3b1d-dff8-8169a30d369f">
      <Execution id="f58c39e4-2321-4339-aa0b-a25fde5f961f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(strategy: DropNewest)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ccb6e43b-ed87-41fb-aacf-8532bac21ec1">
      <Execution id="ed26f225-b745-4a18-8f1f-4160063c0fac" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithValidData_ShouldAddToQueue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="31b4aa6a-561a-ec27-c6d2-f1426e253b35">
      <Execution id="13c1c962-9f93-4ec9-9b91-3670367f1a2a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithValidData_ShouldAddToQueue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 0, maxAttempts: 0, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cf6e81ed-2d36-4cb5-7d19-3207ec4a1e6b">
      <Execution id="e0c80af1-a941-41d4-99c6-e44ce78ad18b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithReconnectSettings_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48:65:6C:6C:6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="faacff99-6157-5e13-4b0f-4d4468903363">
      <Execution id="5e2f053d-e9fa-426e-b4c6-d0d361ca584c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 8, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d095da48-d2a1-b869-306b-68ec74fb6a86">
      <Execution id="b4af8f5c-d0ac-4fb0-a54c-54e8760deacd" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.GetDataCopy_ShouldReturnIndependentCopy" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2f010a59-e386-1c48-7a96-6eaf300b4a00">
      <Execution id="74a06d99-744f-4bae-968b-d4a8cff03982" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="GetDataCopy_ShouldReturnIndependentCopy" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.Constructor_WithNullConfiguration_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="03bac210-5121-4552-6b4a-8ad9c8a4430a">
      <Execution id="19123914-2246-4643-bce1-9450996499f4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="Constructor_WithNullConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;Hello&quot;, expectedText: &quot;Hello&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a81de412-4838-6623-61fa-67ddc3188238">
      <Execution id="ca1d2d60-0c86-4565-ab1e-962731a63e63" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToText_WithUTF8Encoding_ShouldReturnCorrectText" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 115200, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="456b7034-8cd0-175d-c38d-784450059613">
      <Execution id="cfd5d9b8-a3f4-4e87-ae22-b573530b678a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBaudRate_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 0, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3b43f857-4c69-124a-f1e5-4526c8f72ed1">
      <Execution id="57ee19a0-6e77-4ce4-ad82-d56baf312910" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBufferSizes_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: -1, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="09ce9de4-34a5-edec-6b47-7870a0ac8152">
      <Execution id="89b54f00-20e1-4432-9e8a-006e785277a8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithTimeouts_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48-65-6C-6C-6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4fb7a94f-b5ad-a202-33ad-c14f2afe94a5">
      <Execution id="dd9a8120-28f0-4b6e-8850-76bb903e0eb6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithNullData_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="29cc443b-5843-75d0-329d-fdd89478ba0f">
      <Execution id="3da31921-a9a9-4c0e-adb9-3d004870be75" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithNullData_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.TryDequeueData_WithDataInQueue_ShouldReturnData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a70006d6-2422-b7b2-466f-65fda27fd805">
      <Execution id="beaedd90-dc05-4034-ad88-e92875d7e372" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="TryDequeueData_WithDataInQueue_ShouldReturnData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;   &quot;, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bfdab111-1736-9094-751d-b681895868a8">
      <Execution id="f9aae8ab-d75f-4dc5-bc7e-d315d68ecaa7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WhenQueueFull_ShouldTriggerOverflowEvent" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5fcd1008-efe6-9593-2351-b4703f8be1f5">
      <Execution id="efe46473-4f62-4b18-910f-59108b2126dc" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WhenQueueFull_ShouldTriggerOverflowEvent" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: 1000, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="44298843-f215-2386-e136-f2a25ffedf0e">
      <Execution id="9c388f4e-685e-43bf-929c-2dc8fc557f9f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithTimeouts_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 0, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d796aa36-66a9-9e1e-199e-e7f8579e7312">
      <Execution id="a5127d1d-0776-4cb0-895f-d8eb8a7955b4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBaudRate_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 4, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c029ac10-2236-0f3e-f243-4fb9689c404b">
      <Execution id="994c655d-97eb-4edb-aef0-574ebbe154a6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;COM2&quot;, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d42f65dc-b483-693d-547e-03998b4ce142">
      <Execution id="43ef0c38-2ee6-4a63-9dce-c0e659b7c6bb" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [72, 101, 108, 108, 111], expectedHex: &quot;48 65 6C 6C 6F&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ed73d661-31f4-564a-3bca-e7bd1da12bc7">
      <Execution id="59fa376d-ff01-4b4a-b48f-18635236be7d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.GetStatistics_ShouldReturnCorrectInformation" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="14c24c6f-1bd1-8a0b-0fdb-8dccb26c718e">
      <Execution id="ef11da1b-f4e0-49b1-bac5-6fe2dd00be07" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="GetStatistics_ShouldReturnCorrectInformation" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;:&quot;, uppercase: False, expectedHex: &quot;48:65:6c&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="68fa167d-968d-2375-d00a-3a724af909d8">
      <Execution id="0f928ada-588a-432a-98f5-605c14262bf5" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithMoreRequestedThanAvailable_ShouldReturnAllAvailable" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d0e94aff-761a-b752-8ea1-b05e263e6baa">
      <Execution id="fdc2b886-4b70-4dd1-9c8a-57f9b19a33e4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="DequeueBatch_WithMoreRequestedThanAvailable_ShouldReturnAllAvailable" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithMultipleItems_ShouldReturnCorrectCount" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ceb4348a-7f39-5209-f0c5-47a92f0ccf52">
      <Execution id="31d90c2d-9448-49ea-9b03-7dbf3646ac21" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="DequeueBatch_WithMultipleItems_ShouldReturnCorrectCount" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;&quot;, expectedText: &quot;&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="7c790003-2d63-4b93-840e-28312682c36d">
      <Execution id="d3040952-6794-4d91-b526-0d93fa0cadb7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToText_WithUTF8Encoding_ShouldReturnCorrectText" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [], expectedHex: &quot;&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c34da281-7d78-d48e-fd36-b17e7d8c79e7">
      <Execution id="54198639-f49d-4300-aee5-3c4e97b2b616" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.Constructor_ShouldInitializeWithDefaultValues" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1e4bdd15-076b-9af7-45c3-530c1fecf022">
      <Execution id="40f87d66-7c89-431a-b28e-d29b79bb7908" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="Constructor_ShouldInitializeWithDefaultValues" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithByteArray_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f543d2e9-4ced-9d03-4307-68823ad67ec5">
      <Execution id="6c4d3735-fc1a-4227-be98-4bf3ddc6519c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_WithByteArray_ShouldInitializeCorrectly" />
    </UnitTest>
  </TestDefinitions>
  <TestEntries>
    <TestEntry testId="ceb4348a-7f39-5209-f0c5-47a92f0ccf52" executionId="31d90c2d-9448-49ea-9b03-7dbf3646ac21" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4fb7a94f-b5ad-a202-33ad-c14f2afe94a5" executionId="dd9a8120-28f0-4b6e-8850-76bb903e0eb6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="40f0c48a-0bc6-7691-1bcb-bf225bd03b25" executionId="fa24df87-2d38-497a-b47c-226524585332" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="de7a5bdb-03df-5bef-5375-0d9de7fb33cd" executionId="8a6d579f-3a1f-4ba2-a175-3e489658c80a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="35f2d5bf-cf12-732f-0809-1a84cafae57a" executionId="ec803ad9-1e0d-4cd3-8a32-e9f4a574c40b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1fc3a25a-3320-5e0f-9e3e-b9761c8f4549" executionId="0567ab5e-a2d4-4fdc-b869-aeecaeeca907" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="68fa167d-968d-2375-d00a-3a724af909d8" executionId="0f928ada-588a-432a-98f5-605c14262bf5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0acc92da-f9c6-47d9-c5a2-760ab204265e" executionId="8f24a948-51f0-4a81-9b86-f53019d2e781" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3b43f857-4c69-124a-f1e5-4526c8f72ed1" executionId="57ee19a0-6e77-4ce4-ad82-d56baf312910" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d0e94aff-761a-b752-8ea1-b05e263e6baa" executionId="fdc2b886-4b70-4dd1-9c8a-57f9b19a33e4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3f1e1745-b426-f5fa-fc4d-7c36c8ed8987" executionId="6f0b83b5-8acd-42bf-850d-50b89b051be7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7c790003-2d63-4b93-840e-28312682c36d" executionId="d3040952-6794-4d91-b526-0d93fa0cadb7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ae04b4e0-cf08-4697-35c4-f48e7c5131c5" executionId="fe7604a3-34d7-4a73-a3df-b98a06d5b007" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="44298843-f215-2386-e136-f2a25ffedf0e" executionId="9c388f4e-685e-43bf-929c-2dc8fc557f9f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="37b241c9-a28c-3630-63d5-22833c81cd74" executionId="45d62ee6-12eb-48d2-bb29-9b7d4cbeca1f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d42f65dc-b483-693d-547e-03998b4ce142" executionId="43ef0c38-2ee6-4a63-9dce-c0e659b7c6bb" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="31b4aa6a-561a-ec27-c6d2-f1426e253b35" executionId="13c1c962-9f93-4ec9-9b91-3670367f1a2a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="03bac210-5121-4552-6b4a-8ad9c8a4430a" executionId="19123914-2246-4643-bce1-9450996499f4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ee4c97f9-aba3-7fd3-4eff-c0ae32af980a" executionId="a8337ecd-7b20-477c-b847-063946eabbb4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="54219804-c561-b035-0838-e2a41e644606" executionId="68c94aff-c3dc-4d50-a0e4-5dab4b729164" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="14c24c6f-1bd1-8a0b-0fdb-8dccb26c718e" executionId="ef11da1b-f4e0-49b1-bac5-6fe2dd00be07" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="38e1a48a-8126-3cc7-e8ac-60f91e67cb3d" executionId="7317bc32-b506-49fd-999a-f8a4dbb20e1f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5772937f-dba3-ddd7-fc6d-303fd2ade221" executionId="2895acd1-46b5-42db-8b18-d3ee10af4976" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="700480ab-f9a3-c14c-3862-c09cc3b0f4a7" executionId="c8aa1632-f8b6-4909-961f-d556e874b01c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fc0f03d5-65c5-740b-47d9-a78e15ea1d9c" executionId="be35d40b-a7e4-4642-a4d8-248f4ff94427" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1110dce6-cdc2-edbd-83e1-76acc885583d" executionId="b0a09051-22f7-4966-800b-39784144eb86" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bfdab111-1736-9094-751d-b681895868a8" executionId="f9aae8ab-d75f-4dc5-bc7e-d315d68ecaa7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5168b9bd-ef9a-5e12-e4e4-606e2d42d0c0" executionId="68e6d515-b673-421b-87dc-2e2e9fdce721" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="456b7034-8cd0-175d-c38d-784450059613" executionId="cfd5d9b8-a3f4-4e87-ae22-b573530b678a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ac6b9c02-26c7-4e3d-e30f-fe45601d8bad" executionId="638040db-85bf-4c3e-86f0-46447fbc6770" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f543d2e9-4ced-9d03-4307-68823ad67ec5" executionId="6c4d3735-fc1a-4227-be98-4bf3ddc6519c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="011894a4-ce24-7e54-f1d5-7f0ac286b44b" executionId="48826994-4f45-414b-8dc7-4d927451b925" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d095da48-d2a1-b869-306b-68ec74fb6a86" executionId="b4af8f5c-d0ac-4fb0-a54c-54e8760deacd" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c029ac10-2236-0f3e-f243-4fb9689c404b" executionId="994c655d-97eb-4edb-aef0-574ebbe154a6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2f010a59-e386-1c48-7a96-6eaf300b4a00" executionId="74a06d99-744f-4bae-968b-d4a8cff03982" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a81de412-4838-6623-61fa-67ddc3188238" executionId="ca1d2d60-0c86-4565-ab1e-962731a63e63" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="abc2d729-2b39-bae1-e7d9-85d1bd974a73" executionId="d2c2101f-a279-4115-a512-28beef946eeb" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2660b25c-0ab3-7226-1818-3de3d93f7e8b" executionId="0536ebcf-3369-44e3-bf3e-fd12c9e0f2b6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="86618079-abf9-30c9-2492-2a4b2efcc170" executionId="eeae05a0-6287-4873-bf74-1f8098281c27" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fcb4374f-81fa-4153-7c7a-49168b68b53b" executionId="af0f2c6d-daf6-4a06-b2d0-3f946ad550cf" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="db250e33-9c97-4756-436a-16d6e8cf414f" executionId="94a18988-3703-4895-8634-4a57b92d3284" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ccb6e43b-ed87-41fb-aacf-8532bac21ec1" executionId="ed26f225-b745-4a18-8f1f-4160063c0fac" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="df64e4db-ecba-2113-fb43-2182e90a55d5" executionId="1e4227eb-b839-4469-a575-c1f2ed6c0a69" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c34da281-7d78-d48e-fd36-b17e7d8c79e7" executionId="54198639-f49d-4300-aee5-3c4e97b2b616" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="29cc443b-5843-75d0-329d-fdd89478ba0f" executionId="3da31921-a9a9-4c0e-adb9-3d004870be75" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8c638f55-8f9a-3b1d-dff8-8169a30d369f" executionId="f58c39e4-2321-4339-aa0b-a25fde5f961f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="faacff99-6157-5e13-4b0f-4d4468903363" executionId="5e2f053d-e9fa-426e-b4c6-d0d361ca584c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f0dd822e-110a-9423-89f0-2a6bc0510f84" executionId="191cdfda-bf54-439c-9dd8-c359e8229456" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ed73d661-31f4-564a-3bca-e7bd1da12bc7" executionId="59fa376d-ff01-4b4a-b48f-18635236be7d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d8f02cf7-433b-61ec-c449-566982f978ff" executionId="3abf323f-ce2a-4cbb-a74e-c7b22a98c1c9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="969900f1-f9c4-1336-3ebc-d975003306b5" executionId="b8b21815-e1ba-43bb-aea9-d79dd680d262" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cf6e81ed-2d36-4cb5-7d19-3207ec4a1e6b" executionId="e0c80af1-a941-41d4-99c6-e44ce78ad18b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a33546b2-5014-1109-76f2-708037dcb465" executionId="8ac1744d-05b2-403c-bcb0-121e8315d16c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1e4bdd15-076b-9af7-45c3-530c1fecf022" executionId="40f87d66-7c89-431a-b28e-d29b79bb7908" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d915a464-2e21-829b-f426-6772ddf55055" executionId="a751fec9-2798-4e63-9555-d59081079aad" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="53a79683-6e18-bdcc-2942-0ae631602802" executionId="0636d393-e71e-48fc-9445-32469f1863b6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5fcd1008-efe6-9593-2351-b4703f8be1f5" executionId="efe46473-4f62-4b18-910f-59108b2126dc" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8927fc38-5352-5a92-ac17-c0b53db402c5" executionId="d281f9cc-2e54-4022-bf44-0247ffe485cb" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="33597ab1-9d34-65c5-b0a1-f1a694190e93" executionId="22eb96d4-be53-4fee-bfa2-db82a075aa12" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="10ecc65a-1d98-44c2-5793-97f0d54a583d" executionId="22fa4d18-ae3a-4e1c-85ae-eda5c29b5177" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="09ce9de4-34a5-edec-6b47-7870a0ac8152" executionId="89b54f00-20e1-4432-9e8a-006e785277a8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a70006d6-2422-b7b2-466f-65fda27fd805" executionId="beaedd90-dc05-4034-ad88-e92875d7e372" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c783c277-08d3-0e65-e123-287a21c8b95a" executionId="1a71e24f-10e8-4797-b2b7-a12cf96e5af7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bfffdd04-e7b6-b0a1-e537-a650f2f9df27" executionId="82f6f55f-6538-428f-ac94-d1c7635dbe80" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0e388063-0d1d-fee8-e535-f6ea426ca6a0" executionId="5af24054-3544-4b74-b005-abf58e048480" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c53401be-8da1-5d73-868a-6420974db0e4" executionId="f7fd25ff-0d4b-4eb6-8127-654d27c98b51" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d796aa36-66a9-9e1e-199e-e7f8579e7312" executionId="a5127d1d-0776-4cb0-895f-d8eb8a7955b4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5dbf9991-d128-405c-e119-2c5c8fb571d8" executionId="5fb50384-40cc-4b15-81e7-da61d2134dd8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
  </TestEntries>
  <TestLists>
    <TestList name="列表中未列出的结果" id="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestList name="所有已加载的结果" id="19431567-8539-422a-85d7-44ee4e166bda" />
  </TestLists>
  <ResultSummary outcome="Completed">
    <Counters total="68" executed="68" passed="68" failed="0" error="0" timeout="0" aborted="0" inconclusive="0" passedButRunAborted="0" notRunnable="0" notExecuted="0" disconnected="0" warning="0" completed="0" inProgress="0" pending="0" />
    <Output>
      <StdOut>[xUnit.net 00:00:00.00] xUnit.net VSTest Adapter v2.4.5+1caef2f33e (64-bit .NET 8.0.17)&#xD;
[xUnit.net 00:00:00.31]   Discovering: Alicres.SerialPort.Tests&#xD;
[xUnit.net 00:00:00.34]   Discovered:  Alicres.SerialPort.Tests&#xD;
[xUnit.net 00:00:00.35]   Starting:    Alicres.SerialPort.Tests&#xD;
[xUnit.net 00:00:00.44]   Finished:    Alicres.SerialPort.Tests&#xD;
</StdOut>
    </Output>
    <RunInfos>
      <RunInfo computerName="JD-ITA028088-PC" outcome="Warning" timestamp="2025-06-12T09:52:07.9166597+08:00">
        <Text>数据收集: 找不到友好名称为“XPlat Code Coverage”的 datacollector。</Text>
      </RunInfo>
      <RunInfo computerName="JD-ITA028088-PC" outcome="Warning" timestamp="2025-06-12T09:52:07.9172498+08:00">
        <Text>数据收集: 找不到数据收集器“XPlat Code Coverage”</Text>
      </RunInfo>
    </RunInfos>
  </ResultSummary>
</TestRun>