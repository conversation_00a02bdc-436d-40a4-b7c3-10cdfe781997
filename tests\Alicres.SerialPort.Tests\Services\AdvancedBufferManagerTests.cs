using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Alicres.SerialPort.Tests.Services;

/// <summary>
/// 高级缓冲管理器测试
/// </summary>
public class AdvancedBufferManagerTests : IDisposable
{
    private readonly Mock<ILogger> _mockLogger;
    private readonly SerialPortConfiguration _configuration;
    private readonly AdvancedBufferManager _bufferManager;

    public AdvancedBufferManagerTests()
    {
        _mockLogger = new Mock<ILogger>();
        _configuration = new SerialPortConfiguration
        {
            PortName = "COM1",
            DataQueueMaxLength = 10,
            BufferOverflowStrategy = BufferOverflowStrategy.DropOldest,
            BufferCleanupInterval = 1000,
            BufferWarningThreshold = 80
        };
        _bufferManager = new AdvancedBufferManager(_configuration, _mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithValidConfiguration_ShouldInitializeCorrectly()
    {
        // Assert
        _bufferManager.QueueLength.Should().Be(0);
        _bufferManager.QueueUsagePercentage.Should().Be(0);
        _bufferManager.TotalBytesReceived.Should().Be(0);
        _bufferManager.TotalBytesDropped.Should().Be(0);
    }

    [Fact]
    public void Constructor_WithNullConfiguration_ShouldThrowException()
    {
        // Act & Assert
        var action = () => new AdvancedBufferManager(null!, _mockLogger.Object);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void EnqueueData_WithValidData_ShouldAddToQueue()
    {
        // Arrange
        var data = new SerialPortData(new byte[] { 1, 2, 3 }, "COM1", SerialPortDataDirection.Received);

        // Act
        var result = _bufferManager.EnqueueData(data);

        // Assert
        result.Should().BeTrue();
        _bufferManager.QueueLength.Should().Be(1);
        _bufferManager.TotalBytesReceived.Should().Be(3);
    }

    [Fact]
    public void EnqueueData_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        var action = () => _bufferManager.EnqueueData(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void EnqueueData_WhenQueueFull_ShouldTriggerOverflowEvent()
    {
        // Arrange
        var overflowEventTriggered = false;
        _bufferManager.BufferOverflow += (sender, e) => overflowEventTriggered = true;

        // Fill the queue to capacity
        for (int i = 0; i < _configuration.DataQueueMaxLength; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act - Add one more item to trigger overflow
        var overflowData = new SerialPortData(new byte[] { 99 }, "COM1", SerialPortDataDirection.Received);
        var result = _bufferManager.EnqueueData(overflowData);

        // Assert
        overflowEventTriggered.Should().BeTrue();
        result.Should().BeTrue(); // Should succeed with DropOldest strategy
        _bufferManager.QueueLength.Should().Be(_configuration.DataQueueMaxLength);
    }

    [Fact]
    public void EnqueueData_WhenNearCapacity_ShouldTriggerWarningEvent()
    {
        // Arrange
        var warningEventTriggered = false;
        _bufferManager.BufferWarning += (sender, e) => warningEventTriggered = true;

        // Fill queue to warning threshold (80% of 10 = 8 items)
        for (int i = 0; i < 8; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act - Add one more to trigger warning
        var warningData = new SerialPortData(new byte[] { 99 }, "COM1", SerialPortDataDirection.Received);
        _bufferManager.EnqueueData(warningData);

        // Assert
        warningEventTriggered.Should().BeTrue();
        _bufferManager.QueueUsagePercentage.Should().BeGreaterOrEqualTo(80);
    }

    [Fact]
    public void TryDequeueData_WithDataInQueue_ShouldReturnData()
    {
        // Arrange
        var originalData = new SerialPortData(new byte[] { 1, 2, 3 }, "COM1", SerialPortDataDirection.Received);
        _bufferManager.EnqueueData(originalData);

        // Act
        var result = _bufferManager.TryDequeueData(out var dequeuedData);

        // Assert
        result.Should().BeTrue();
        dequeuedData.Should().NotBeNull();
        dequeuedData!.RawData.Should().BeEquivalentTo(originalData.RawData);
        _bufferManager.QueueLength.Should().Be(0);
    }

    [Fact]
    public void TryDequeueData_WithEmptyQueue_ShouldReturnFalse()
    {
        // Act
        var result = _bufferManager.TryDequeueData(out var data);

        // Assert
        result.Should().BeFalse();
        data.Should().BeNull();
    }

    [Fact]
    public void DequeueBatch_WithMultipleItems_ShouldReturnCorrectCount()
    {
        // Arrange
        for (int i = 0; i < 5; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act
        var result = _bufferManager.DequeueBatch(3);

        // Assert
        result.Should().HaveCount(3);
        _bufferManager.QueueLength.Should().Be(2);
    }

    [Fact]
    public void DequeueBatch_WithMoreRequestedThanAvailable_ShouldReturnAllAvailable()
    {
        // Arrange
        for (int i = 0; i < 3; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act
        var result = _bufferManager.DequeueBatch(10);

        // Assert
        result.Should().HaveCount(3);
        _bufferManager.QueueLength.Should().Be(0);
    }

    [Fact]
    public void ClearQueue_WithDataInQueue_ShouldEmptyQueue()
    {
        // Arrange
        for (int i = 0; i < 5; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act
        _bufferManager.ClearQueue();

        // Assert
        _bufferManager.QueueLength.Should().Be(0);
    }

    [Fact]
    public void GetStatistics_ShouldReturnCorrectInformation()
    {
        // Arrange
        var data = new SerialPortData(new byte[] { 1, 2, 3 }, "COM1", SerialPortDataDirection.Received);
        _bufferManager.EnqueueData(data);

        // Act
        var statistics = _bufferManager.GetStatistics();

        // Assert
        statistics.Should().NotBeNull();
        statistics.QueueLength.Should().Be(1);
        statistics.MaxQueueLength.Should().Be(_configuration.DataQueueMaxLength);
        statistics.TotalBytesReceived.Should().Be(3);
        statistics.OverflowStrategy.Should().Be(_configuration.BufferOverflowStrategy);
    }

    [Theory]
    [InlineData(BufferOverflowStrategy.DropNewest)]
    [InlineData(BufferOverflowStrategy.ThrowException)]
    public void EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(BufferOverflowStrategy strategy)
    {
        // Arrange
        _configuration.BufferOverflowStrategy = strategy;
        var bufferManager = new AdvancedBufferManager(_configuration, _mockLogger.Object);

        // Fill the queue to capacity
        for (int i = 0; i < _configuration.DataQueueMaxLength; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            bufferManager.EnqueueData(data);
        }

        // Act & Assert
        var overflowData = new SerialPortData(new byte[] { 99 }, "COM1", SerialPortDataDirection.Received);

        if (strategy == BufferOverflowStrategy.DropNewest)
        {
            var result = bufferManager.EnqueueData(overflowData);
            result.Should().BeFalse();
            bufferManager.TotalBytesDropped.Should().Be(1);
        }
        else if (strategy == BufferOverflowStrategy.ThrowException)
        {
            var action = () => bufferManager.EnqueueData(overflowData);
            action.Should().Throw<InvalidOperationException>();
        }

        bufferManager.Dispose();
    }

    [Fact]
    public void QueueUsagePercentage_ShouldCalculateCorrectly()
    {
        // Arrange & Act
        for (int i = 0; i < 5; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Assert
        var expectedPercentage = (int)((double)5 / _configuration.DataQueueMaxLength * 100);
        _bufferManager.QueueUsagePercentage.Should().Be(expectedPercentage);
    }

    #region 边界条件测试补充 - 第一阶段

    /// <summary>
    /// 测试缓冲区满载情况下的数据处理 - 极限容量测试
    /// </summary>
    [Fact]
    public void EnqueueData_AtMaxCapacity_ShouldHandleCorrectly()
    {
        // Arrange
        var overflowEventCount = 0;
        _bufferManager.BufferOverflow += (sender, e) => overflowEventCount++;

        // 填满缓冲区到最大容量
        for (int i = 0; i < _configuration.DataQueueMaxLength; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act - 尝试添加更多数据
        var additionalData = new SerialPortData(new byte[] { 255 }, "COM1", SerialPortDataDirection.Received);
        var result = _bufferManager.EnqueueData(additionalData);

        // Assert
        result.Should().BeTrue(); // DropOldest 策略应该成功
        _bufferManager.QueueLength.Should().Be(_configuration.DataQueueMaxLength);
        overflowEventCount.Should().Be(1);
        _bufferManager.TotalBytesDropped.Should().Be(1); // 应该丢弃了一个字节
    }

    /// <summary>
    /// 测试内存不足时的异常处理 - 大数据量测试
    /// </summary>
    [Fact]
    public void EnqueueData_WithLargeDataVolume_ShouldHandleMemoryPressure()
    {
        // Arrange
        var largeDataSize = 1024 * 1024; // 1MB 数据
        var largeData = new SerialPortData(new byte[largeDataSize], "COM1", SerialPortDataDirection.Received);

        // Act & Assert
        var action = () => _bufferManager.EnqueueData(largeData);
        action.Should().NotThrow("应该能够处理大数据量");

        _bufferManager.TotalBytesReceived.Should().Be(largeDataSize);
    }

    /// <summary>
    /// 测试缓冲区溢出保护机制 - 连续溢出测试
    /// </summary>
    [Fact]
    public void EnqueueData_ContinuousOverflow_ShouldMaintainStability()
    {
        // Arrange
        var overflowCount = 0;
        _bufferManager.BufferOverflow += (sender, e) => overflowCount++;

        // 填满缓冲区
        for (int i = 0; i < _configuration.DataQueueMaxLength; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act - 连续添加多个数据项触发多次溢出
        for (int i = 0; i < 10; i++)
        {
            var overflowData = new SerialPortData(new byte[] { (byte)(100 + i) }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(overflowData);
        }

        // Assert
        overflowCount.Should().Be(10);
        _bufferManager.QueueLength.Should().Be(_configuration.DataQueueMaxLength);
        _bufferManager.TotalBytesDropped.Should().Be(10);
    }

    /// <summary>
    /// 测试零长度数据的处理
    /// </summary>
    [Fact]
    public void EnqueueData_WithZeroLengthData_ShouldHandleCorrectly()
    {
        // Arrange
        var emptyData = new SerialPortData(Array.Empty<byte>(), "COM1", SerialPortDataDirection.Received);

        // Act
        var result = _bufferManager.EnqueueData(emptyData);

        // Assert
        result.Should().BeTrue();
        _bufferManager.QueueLength.Should().Be(1);
        _bufferManager.TotalBytesReceived.Should().Be(0);
    }

    /// <summary>
    /// 测试极限警告阈值边界
    /// </summary>
    [Theory]
    [InlineData(7, false)] // 添加第7个时检查的是60%（6/10），不触发警告
    [InlineData(8, false)] // 添加第8个时检查的是70%（7/10），不触发警告
    [InlineData(9, true)]  // 添加第9个时检查的是80%（8/10），触发警告
    public void EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(int itemCount, bool shouldTriggerWarning)
    {
        // Arrange
        var warningTriggered = false;
        _bufferManager.BufferWarning += (sender, e) => warningTriggered = true;

        // Act - 添加指定数量的数据项
        // 注意：警告检查是在添加数据到队列之前进行的
        for (int i = 0; i < itemCount; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Assert
        var actualPercentage = _bufferManager.QueueUsagePercentage;
        warningTriggered.Should().Be(shouldTriggerWarning,
            $"项目数量: {itemCount}, 最终使用率: {actualPercentage}%, 警告阈值: {_configuration.BufferWarningThreshold}%");
    }

    /// <summary>
    /// 测试批量出队的边界情况
    /// </summary>
    [Theory]
    [InlineData(0)]     // 请求0个
    [InlineData(-1)]    // 请求负数
    [InlineData(1000)]  // 请求超过队列长度的数量
    public void DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(int requestCount)
    {
        // Arrange
        for (int i = 0; i < 5; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act
        var result = _bufferManager.DequeueBatch(requestCount);

        // Assert
        if (requestCount <= 0)
        {
            result.Should().BeEmpty();
            _bufferManager.QueueLength.Should().Be(5); // 队列不应该改变
        }
        else
        {
            var expectedCount = Math.Min(requestCount, 5);
            result.Should().HaveCount(expectedCount);
            _bufferManager.QueueLength.Should().Be(5 - expectedCount);
        }
    }

    #endregion

    #region 并发场景测试 - 第一阶段

    /// <summary>
    /// 测试多线程同时读写的竞态条件
    /// </summary>
    [Fact]
    public async Task EnqueueDequeue_ConcurrentOperations_ShouldBeThreadSafe()
    {
        // Arrange
        const int threadCount = 10;
        const int operationsPerThread = 100;
        var tasks = new List<Task>();
        var enqueuedCount = 0;
        var dequeuedCount = 0;

        // Act - 启动多个并发任务
        for (int i = 0; i < threadCount; i++)
        {
            var threadId = i;

            // 入队任务
            tasks.Add(Task.Run(() =>
            {
                for (int j = 0; j < operationsPerThread; j++)
                {
                    var data = new SerialPortData(new byte[] { (byte)(threadId * 100 + j) }, "COM1", SerialPortDataDirection.Received);
                    if (_bufferManager.EnqueueData(data))
                    {
                        Interlocked.Increment(ref enqueuedCount);
                    }
                }
            }));

            // 出队任务
            tasks.Add(Task.Run(() =>
            {
                for (int j = 0; j < operationsPerThread / 2; j++)
                {
                    if (_bufferManager.TryDequeueData(out _))
                    {
                        Interlocked.Increment(ref dequeuedCount);
                    }
                    Thread.Sleep(1); // 稍微延迟以增加竞争
                }
            }));
        }

        await Task.WhenAll(tasks);

        // Assert
        var finalQueueLength = _bufferManager.QueueLength;
        var expectedQueueLength = Math.Max(0, enqueuedCount - dequeuedCount);

        // 由于并发操作和缓冲区限制，实际结果可能有所不同
        finalQueueLength.Should().BeGreaterOrEqualTo(0);
        finalQueueLength.Should().BeLessOrEqualTo(_configuration.DataQueueMaxLength);

        // 验证没有数据丢失或损坏
        _bufferManager.TotalBytesReceived.Should().BeGreaterThan(0);
    }

    /// <summary>
    /// 测试异步操作的取消和超时处理
    /// </summary>
    [Fact]
    public async Task ConcurrentOperations_WithCancellation_ShouldHandleGracefully()
    {
        // Arrange
        using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(100));
        var tasks = new List<Task>();
        var completedOperations = 0;

        // Act
        for (int i = 0; i < 50; i++)
        {
            tasks.Add(Task.Run(async () =>
            {
                try
                {
                    while (!cts.Token.IsCancellationRequested)
                    {
                        var data = new SerialPortData(new byte[] { (byte)Random.Shared.Next(256) }, "COM1", SerialPortDataDirection.Received);
                        _bufferManager.EnqueueData(data);

                        if (_bufferManager.TryDequeueData(out _))
                        {
                            Interlocked.Increment(ref completedOperations);
                        }

                        await Task.Delay(1, cts.Token);
                    }
                }
                catch (OperationCanceledException)
                {
                    // 预期的取消异常
                }
            }, cts.Token));
        }

        try
        {
            await Task.WhenAll(tasks);
        }
        catch (OperationCanceledException)
        {
            // 预期的取消异常
        }

        // Assert
        completedOperations.Should().BeGreaterThan(0);
        _bufferManager.QueueLength.Should().BeGreaterOrEqualTo(0);
    }

    /// <summary>
    /// 测试并发连接管理的线程安全
    /// </summary>
    [Fact]
    public async Task ConcurrentBufferOperations_WithEventHandlers_ShouldBeThreadSafe()
    {
        // Arrange
        var warningCount = 0;
        var overflowCount = 0;
        var lockObject = new object();

        _bufferManager.BufferWarning += (sender, e) =>
        {
            lock (lockObject)
            {
                warningCount++;
            }
        };

        _bufferManager.BufferOverflow += (sender, e) =>
        {
            lock (lockObject)
            {
                overflowCount++;
            }
        };

        var tasks = new List<Task>();

        // Act - 并发添加数据直到触发警告和溢出
        for (int i = 0; i < 20; i++)
        {
            tasks.Add(Task.Run(() =>
            {
                for (int j = 0; j < _configuration.DataQueueMaxLength; j++)
                {
                    var data = new SerialPortData(new byte[] { (byte)Random.Shared.Next(256) }, "COM1", SerialPortDataDirection.Received);
                    _bufferManager.EnqueueData(data);
                    Thread.Sleep(1);
                }
            }));
        }

        await Task.WhenAll(tasks);

        // Assert
        lock (lockObject)
        {
            warningCount.Should().BeGreaterThan(0);
            overflowCount.Should().BeGreaterThan(0);
        }

        _bufferManager.QueueLength.Should().Be(_configuration.DataQueueMaxLength);
    }

    /// <summary>
    /// 测试资源竞争和死锁预防
    /// </summary>
    [Fact]
    public async Task ConcurrentOperations_ResourceContention_ShouldNotDeadlock()
    {
        // Arrange
        const int concurrentTasks = 20;
        var tasks = new List<Task>();
        var completedTasks = 0;

        // Act - 创建多个任务同时执行各种操作
        for (int i = 0; i < concurrentTasks; i++)
        {
            tasks.Add(Task.Run(() =>
            {
                try
                {
                    // 混合操作：入队、出队、获取统计、清空队列
                    for (int j = 0; j < 50; j++)
                    {
                        switch (j % 4)
                        {
                            case 0:
                                var data = new SerialPortData(new byte[] { (byte)j }, "COM1", SerialPortDataDirection.Received);
                                _bufferManager.EnqueueData(data);
                                break;
                            case 1:
                                _bufferManager.TryDequeueData(out _);
                                break;
                            case 2:
                                _bufferManager.GetStatistics();
                                break;
                            case 3:
                                if (j % 20 == 0) // 偶尔清空队列
                                    _bufferManager.ClearQueue();
                                break;
                        }

                        Thread.Sleep(Random.Shared.Next(1, 3));
                    }

                    Interlocked.Increment(ref completedTasks);
                }
                catch (Exception ex)
                {
                    // 记录异常但不抛出，以便检查死锁
                    Console.WriteLine($"Task failed: {ex.Message}");
                }
            }));
        }

        // 设置超时以检测死锁
        var timeoutTask = Task.Delay(TimeSpan.FromSeconds(10));
        var completedTask = Task.WhenAll(tasks);

        var finishedTask = await Task.WhenAny(completedTask, timeoutTask);

        // Assert
        finishedTask.Should().Be(completedTask, "操作应该在超时前完成，没有死锁");
        completedTasks.Should().Be(concurrentTasks, "所有任务都应该成功完成");
    }

    #endregion

    #region 性能边界测试 - 第一阶段

    /// <summary>
    /// 测试高频数据传输的性能极限
    /// </summary>
    [Fact]
    public void EnqueueData_HighFrequencyOperations_ShouldMaintainPerformance()
    {
        // Arrange
        const int operationCount = 10000;
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var successCount = 0;

        // Act
        for (int i = 0; i < operationCount; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)(i % 256) }, "COM1", SerialPortDataDirection.Received);
            if (_bufferManager.EnqueueData(data))
            {
                successCount++;
            }

            // 每100次操作出队一次，保持队列不会过满
            if (i % 100 == 0)
            {
                _bufferManager.TryDequeueData(out _);
            }
        }

        stopwatch.Stop();

        // Assert
        var operationsPerSecond = operationCount / stopwatch.Elapsed.TotalSeconds;

        operationsPerSecond.Should().BeGreaterThan(1000, "应该能够处理至少1000次操作/秒");
        successCount.Should().BeGreaterThan((int)(operationCount * 0.8), "至少80%的操作应该成功");

        Console.WriteLine($"性能测试结果: {operationsPerSecond:F0} 操作/秒, 成功率: {(double)successCount/operationCount:P}");
    }

    /// <summary>
    /// 测试长时间运行的内存泄漏检测
    /// </summary>
    [Fact]
    public void BufferManager_LongRunningOperations_ShouldNotLeakMemory()
    {
        // Arrange
        const int cycles = 1000;
        const int operationsPerCycle = 100;

        var initialMemory = GC.GetTotalMemory(true);

        // Act - 模拟长时间运行
        for (int cycle = 0; cycle < cycles; cycle++)
        {
            // 填充缓冲区
            for (int i = 0; i < operationsPerCycle; i++)
            {
                var data = new SerialPortData(new byte[100], "COM1", SerialPortDataDirection.Received);
                _bufferManager.EnqueueData(data);
            }

            // 清空缓冲区
            _bufferManager.ClearQueue();

            // 每100个周期强制垃圾回收
            if (cycle % 100 == 0)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
        }

        // 最终垃圾回收
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var finalMemory = GC.GetTotalMemory(false);
        var memoryIncrease = finalMemory - initialMemory;

        // Assert
        // 允许一定的内存增长，但不应该有显著的泄漏
        // 在测试环境中，由于JIT编译和其他因素，允许更大的内存增长
        memoryIncrease.Should().BeLessThan(50 * 1024 * 1024, "内存增长应该小于50MB，表明没有严重的内存泄漏");

        Console.WriteLine($"内存使用情况: 初始 {initialMemory:N0} 字节, 最终 {finalMemory:N0} 字节, 增长 {memoryIncrease:N0} 字节");
    }

    /// <summary>
    /// 测试CPU使用率在高负载下的监控
    /// </summary>
    [Fact]
    public void BufferManager_HighLoadOperations_ShouldMaintainReasonableCpuUsage()
    {
        // Arrange
        const int duration = 1000; // 1秒测试
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var operationCount = 0;

        // Act - 在指定时间内尽可能多地执行操作
        while (stopwatch.ElapsedMilliseconds < duration)
        {
            var data = new SerialPortData(new byte[] { (byte)(operationCount % 256) }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);

            if (operationCount % 10 == 0)
            {
                _bufferManager.TryDequeueData(out _);
            }

            if (operationCount % 100 == 0)
            {
                _bufferManager.GetStatistics();
            }

            operationCount++;
        }

        stopwatch.Stop();

        // Assert
        var operationsPerSecond = operationCount / stopwatch.Elapsed.TotalSeconds;

        operationsPerSecond.Should().BeGreaterThan(500, "应该能够维持合理的操作频率");
        _bufferManager.QueueLength.Should().BeGreaterOrEqualTo(0);

        Console.WriteLine($"高负载测试结果: {operationsPerSecond:F0} 操作/秒, 队列长度: {_bufferManager.QueueLength}");
    }

    /// <summary>
    /// 测试大批量数据处理的性能
    /// </summary>
    [Theory]
    [InlineData(1000)]   // 1KB
    [InlineData(10000)]  // 10KB
    [InlineData(100000)] // 100KB
    public void EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(int dataSize)
    {
        // Arrange
        var largeData = new byte[dataSize];
        Random.Shared.NextBytes(largeData);
        var data = new SerialPortData(largeData, "COM1", SerialPortDataDirection.Received);

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var result = _bufferManager.EnqueueData(data);

        stopwatch.Stop();

        // Assert
        result.Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(100, $"处理{dataSize}字节数据应该在100ms内完成");
        _bufferManager.TotalBytesReceived.Should().Be(dataSize);

        Console.WriteLine($"大数据处理: {dataSize:N0} 字节, 耗时: {stopwatch.ElapsedMilliseconds}ms");
    }

    /// <summary>
    /// 测试统计信息计算的性能影响
    /// </summary>
    [Fact]
    public void GetStatistics_FrequentCalls_ShouldNotImpactPerformance()
    {
        // Arrange
        const int statisticsCallCount = 1000;

        // 先填充一些数据
        for (int i = 0; i < 50; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act - 频繁调用统计信息
        for (int i = 0; i < statisticsCallCount; i++)
        {
            var stats = _bufferManager.GetStatistics();
            stats.Should().NotBeNull();
        }

        stopwatch.Stop();

        // Assert
        var avgTimePerCall = stopwatch.Elapsed.TotalMilliseconds / statisticsCallCount;
        avgTimePerCall.Should().BeLessThan(1.0, "每次统计调用应该在1ms内完成");

        Console.WriteLine($"统计信息性能: {statisticsCallCount} 次调用, 平均 {avgTimePerCall:F3}ms/次");
    }

    #endregion

    public void Dispose()
    {
        _bufferManager?.Dispose();
    }
}
