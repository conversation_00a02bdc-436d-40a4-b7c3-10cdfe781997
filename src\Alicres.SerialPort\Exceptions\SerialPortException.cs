namespace Alicres.SerialPort.Exceptions;

/// <summary>
/// 串口通讯基础异常类
/// </summary>
public class SerialPortException : Exception
{
    /// <summary>
    /// 端口名称
    /// </summary>
    public string? PortName { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortException()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    public SerialPortException(string message) : base(message)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    public SerialPortException(string message, string portName) : base(message)
    {
        PortName = portName;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortException(string message, string portName, Exception innerException) : base(message, innerException)
    {
        PortName = portName;
    }
}

/// <summary>
/// 串口连接异常
/// </summary>
public class SerialPortConnectionException : SerialPortException
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortConnectionException()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    public SerialPortConnectionException(string message) : base(message)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortConnectionException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    public SerialPortConnectionException(string message, string portName) : base(message, portName)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortConnectionException(string message, string portName, Exception innerException) : base(message, portName, innerException)
    {
    }
}

/// <summary>
/// 串口配置异常
/// </summary>
public class SerialPortConfigurationException : SerialPortException
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortConfigurationException()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    public SerialPortConfigurationException(string message) : base(message)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortConfigurationException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    public SerialPortConfigurationException(string message, string portName) : base(message, portName)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortConfigurationException(string message, string portName, Exception innerException) : base(message, portName, innerException)
    {
    }
}

/// <summary>
/// 串口数据传输异常
/// </summary>
public class SerialPortDataException : SerialPortException
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortDataException()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    public SerialPortDataException(string message) : base(message)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortDataException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    public SerialPortDataException(string message, string portName) : base(message, portName)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortDataException(string message, string portName, Exception innerException) : base(message, portName, innerException)
    {
    }
}
