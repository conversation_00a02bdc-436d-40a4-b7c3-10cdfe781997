using Alicres.Protocol.Models.EventArgs;

namespace Alicres.Protocol.Interfaces;

/// <summary>
/// 协议管理器接口，统一管理协议解析和传输
/// </summary>
public interface IProtocolManager : IDisposable
{
    /// <summary>
    /// 协议解析器
    /// </summary>
    IProtocolParser ProtocolParser { get; }

    /// <summary>
    /// 传输适配器
    /// </summary>
    ITransportAdapter TransportAdapter { get; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// 是否正在运行
    /// </summary>
    bool IsRunning { get; }

    /// <summary>
    /// 打开连接并开始协议处理
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功连接返回 true，否则返回 false</returns>
    Task<bool> ConnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 关闭连接并停止协议处理
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功断开返回 true，否则返回 false</returns>
    Task<bool> DisconnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送协议消息
    /// </summary>
    /// <param name="message">要发送的协议消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功发送返回 true，否则返回 false</returns>
    Task<bool> SendMessageAsync(IProtocolMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送原始数据
    /// </summary>
    /// <param name="data">要发送的原始数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功发送返回 true，否则返回 false</returns>
    Task<bool> SendRawDataAsync(byte[] data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送消息并等待响应
    /// </summary>
    /// <param name="request">请求消息</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应消息，如果超时或失败返回 null</returns>
    Task<IProtocolMessage?> SendAndWaitAsync(IProtocolMessage request, TimeSpan timeout, CancellationToken cancellationToken = default);

    /// <summary>
    /// 协议消息接收事件
    /// </summary>
    event EventHandler<ProtocolMessageEventArgs>? MessageReceived;

    /// <summary>
    /// 原始数据接收事件
    /// </summary>
    event EventHandler<TransportDataEventArgs>? RawDataReceived;

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    event EventHandler<TransportStatusEventArgs>? ConnectionStatusChanged;

    /// <summary>
    /// 错误发生事件
    /// </summary>
    event EventHandler<ProtocolErrorEventArgs>? ErrorOccurred;
}
