using Alicres.Protocol.Interfaces;
using Alicres.Protocol.Protocols.Modbus;
using Alicres.Protocol.Services;
using Alicres.Protocol.Validators;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Alicres.Protocol.Extensions;

/// <summary>
/// 依赖注入扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 Alicres.Protocol 服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddAlicresProtocol(this IServiceCollection services)
    {
        return services.AddAlicresProtocol(_ => { });
    }

    /// <summary>
    /// 添加 Alicres.Protocol 服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddAlicresProtocol(this IServiceCollection services, Action<ProtocolOptions> configureOptions)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configureOptions);

        // 配置选项
        services.Configure(configureOptions);

        // 注册核心服务
        services.TryAddSingleton<IProtocolManager, ProtocolManager>();

        // 注册协议解析器
        services.TryAddTransient<ModbusRtuProtocol>();

        // 注册数据校验器
        services.TryAddTransient<Crc16Validator>();
        services.TryAddTransient<IProtocolValidator, Crc16Validator>();

        return services;
    }

    /// <summary>
    /// 添加 Modbus RTU 协议支持
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddModbusRtuProtocol(this IServiceCollection services)
    {
        ArgumentNullException.ThrowIfNull(services);

        services.TryAddTransient<ModbusRtuProtocol>();
        services.TryAddTransient<IProtocolParser, ModbusRtuProtocol>();

        return services;
    }

    /// <summary>
    /// 添加 CRC16 校验器
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCrc16Validator(this IServiceCollection services)
    {
        ArgumentNullException.ThrowIfNull(services);

        services.TryAddTransient<Crc16Validator>();
        services.TryAddTransient<IProtocolValidator, Crc16Validator>();

        return services;
    }
}

/// <summary>
/// 协议配置选项
/// </summary>
public class ProtocolOptions
{
    /// <summary>
    /// 默认协议类型
    /// </summary>
    public ProtocolType DefaultProtocol { get; set; } = ProtocolType.ModbusRtu;

    /// <summary>
    /// 是否启用数据校验
    /// </summary>
    public bool EnableDataValidation { get; set; } = true;

    /// <summary>
    /// 接收缓冲区大小
    /// </summary>
    public int ReceiveBufferSize { get; set; } = 4096;

    /// <summary>
    /// 消息解析超时时间（毫秒）
    /// </summary>
    public int ParseTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableVerboseLogging { get; set; } = false;
}

/// <summary>
/// 协议类型枚举
/// </summary>
public enum ProtocolType
{
    /// <summary>
    /// Modbus RTU 协议
    /// </summary>
    ModbusRtu,

    /// <summary>
    /// Modbus ASCII 协议
    /// </summary>
    ModbusAscii,

    /// <summary>
    /// 自定义协议
    /// </summary>
    Custom
}
