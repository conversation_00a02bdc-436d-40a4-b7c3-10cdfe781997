using System.Text;

namespace Alicres.SerialPort.Models;

/// <summary>
/// 串口数据传输模型
/// </summary>
public class SerialPortData
{
    /// <summary>
    /// 原始字节数据
    /// </summary>
    public byte[] RawData { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// 数据长度
    /// </summary>
    public int Length => RawData.Length;

    /// <summary>
    /// 接收时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 端口名称
    /// </summary>
    public string PortName { get; set; } = string.Empty;

    /// <summary>
    /// 数据方向（发送/接收）
    /// </summary>
    public SerialPortDataDirection Direction { get; set; } = SerialPortDataDirection.Received;

    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortData()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="data">字节数据</param>
    /// <param name="portName">端口名称</param>
    /// <param name="direction">数据方向</param>
    public SerialPortData(byte[] data, string portName, SerialPortDataDirection direction = SerialPortDataDirection.Received)
    {
        RawData = data ?? Array.Empty<byte>();
        PortName = portName;
        Direction = direction;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="text">文本数据</param>
    /// <param name="portName">端口名称</param>
    /// <param name="encoding">编码方式</param>
    /// <param name="direction">数据方向</param>
    public SerialPortData(string text, string portName, Encoding? encoding = null, SerialPortDataDirection direction = SerialPortDataDirection.Received)
    {
        encoding ??= Encoding.UTF8;
        RawData = encoding.GetBytes(text ?? string.Empty);
        PortName = portName;
        Direction = direction;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 将数据转换为字符串
    /// </summary>
    /// <param name="encoding">编码方式，默认为 UTF-8</param>
    /// <returns>字符串表示</returns>
    public string ToText(Encoding? encoding = null)
    {
        encoding ??= Encoding.UTF8;
        return encoding.GetString(RawData);
    }

    /// <summary>
    /// 将数据转换为十六进制字符串
    /// </summary>
    /// <param name="separator">分隔符，默认为空格</param>
    /// <param name="uppercase">是否使用大写，默认为 true</param>
    /// <returns>十六进制字符串</returns>
    public string ToHexString(string separator = " ", bool uppercase = true)
    {
        if (RawData.Length == 0)
            return string.Empty;

        var format = uppercase ? "X2" : "x2";
        return string.Join(separator, RawData.Select(b => b.ToString(format)));
    }

    /// <summary>
    /// 从十六进制字符串创建数据
    /// </summary>
    /// <param name="hexString">十六进制字符串</param>
    /// <param name="portName">端口名称</param>
    /// <param name="direction">数据方向</param>
    /// <returns>串口数据实例</returns>
    public static SerialPortData FromHexString(string hexString, string portName, SerialPortDataDirection direction = SerialPortDataDirection.Sent)
    {
        if (string.IsNullOrWhiteSpace(hexString))
            return new SerialPortData(Array.Empty<byte>(), portName, direction);

        // 移除空格和其他分隔符
        var cleanHex = hexString.Replace(" ", "").Replace("-", "").Replace(":", "");
        
        if (cleanHex.Length % 2 != 0)
            throw new ArgumentException("十六进制字符串长度必须为偶数", nameof(hexString));

        var bytes = new byte[cleanHex.Length / 2];
        for (int i = 0; i < bytes.Length; i++)
        {
            bytes[i] = Convert.ToByte(cleanHex.Substring(i * 2, 2), 16);
        }

        return new SerialPortData(bytes, portName, direction);
    }

    /// <summary>
    /// 检查数据是否为空
    /// </summary>
    /// <returns>如果数据为空返回 true，否则返回 false</returns>
    public bool IsEmpty => RawData.Length == 0;

    /// <summary>
    /// 获取数据的副本
    /// </summary>
    /// <returns>数据副本</returns>
    public byte[] GetDataCopy()
    {
        var copy = new byte[RawData.Length];
        Array.Copy(RawData, copy, RawData.Length);
        return copy;
    }

    /// <summary>
    /// 重写 ToString 方法
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        var directionText = Direction == SerialPortDataDirection.Sent ? "发送" : "接收";
        return $"[{Timestamp:HH:mm:ss.fff}] {PortName} {directionText}: {Length} 字节";
    }
}

/// <summary>
/// 串口数据方向枚举
/// </summary>
public enum SerialPortDataDirection
{
    /// <summary>
    /// 接收的数据
    /// </summary>
    Received,

    /// <summary>
    /// 发送的数据
    /// </summary>
    Sent
}
