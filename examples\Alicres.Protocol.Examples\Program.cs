using Alicres.Protocol.Adapters;
using Alicres.Protocol.Extensions;
using Alicres.Protocol.Protocols.Modbus;
using Alicres.Protocol.Protocols.Modbus.Messages;
using Alicres.Protocol.Services;
using Alicres.SerialPort.Extensions;
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Examples;

/// <summary>
/// Alicres.Protocol 示例程序
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Alicres.Protocol 示例程序 ===\n");

        // 创建主机构建器
        var hostBuilder = Host.CreateDefaultBuilder(args)
            .ConfigureServices((context, services) =>
            {
                // 添加 Alicres.SerialPort 服务
                services.AddAlicresSerialPort();
                
                // 添加 Alicres.Protocol 服务
                services.AddAlicresProtocol(options =>
                {
                    options.DefaultProtocol = ProtocolType.ModbusRtu;
                    options.EnableDataValidation = true;
                    options.EnableVerboseLogging = true;
                });
            })
            .ConfigureLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.SetMinimumLevel(LogLevel.Debug);
            });

        using var host = hostBuilder.Build();
        await host.StartAsync();

        var serviceProvider = host.Services;

        // 显示菜单
        while (true)
        {
            ShowMenu();
            var choice = Console.ReadLine();

            try
            {
                switch (choice)
                {
                    case "1":
                        await BasicProtocolExample(serviceProvider);
                        break;
                    case "2":
                        await ModbusRtuExample(serviceProvider);
                        break;
                    case "3":
                        await Crc16ValidatorExample();
                        break;
                    case "4":
                        await DependencyInjectionExample(serviceProvider);
                        break;
                    case "0":
                        Console.WriteLine("退出程序...");
                        return;
                    default:
                        Console.WriteLine("无效选择，请重新输入。");
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }

            Console.WriteLine("\n按任意键继续...");
            Console.ReadKey();
            Console.Clear();
        }
    }

    /// <summary>
    /// 显示主菜单
    /// </summary>
    static void ShowMenu()
    {
        Console.WriteLine("请选择示例:");
        Console.WriteLine("1. 基础协议解析示例");
        Console.WriteLine("2. Modbus RTU 协议示例");
        Console.WriteLine("3. CRC16 校验器示例");
        Console.WriteLine("4. 依赖注入示例");
        Console.WriteLine("0. 退出");
        Console.Write("请输入选择 (0-4): ");
    }

    /// <summary>
    /// 基础协议解析示例
    /// </summary>
    static async Task BasicProtocolExample(IServiceProvider serviceProvider)
    {
        Console.WriteLine("\n=== 基础协议解析示例 ===");

        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

        // 创建 Modbus RTU 协议解析器
        var protocol = new ModbusRtuProtocol(serviceProvider.GetService<ILogger<ModbusRtuProtocol>>());

        Console.WriteLine($"协议名称: {protocol.ProtocolName}");
        Console.WriteLine($"协议版本: {protocol.ProtocolVersion}");
        Console.WriteLine($"数据校验: {(protocol.ValidationEnabled ? "启用" : "禁用")}");

        // 测试数据（Modbus RTU 读取保持寄存器请求）
        var testData = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x84, 0x0A };

        Console.WriteLine($"\n测试数据: {string.Join(" ", testData.Select(b => $"0x{b:X2}"))}");

        // 检查是否为完整消息
        var isComplete = protocol.IsCompleteMessage(testData);
        Console.WriteLine($"是否为完整消息: {isComplete}");

        if (isComplete)
        {
            // 解析消息
            var message = await protocol.ParseAsync(testData);
            if (message != null)
            {
                Console.WriteLine($"解析成功: {message}");
                Console.WriteLine($"消息类型: {message.MessageType}");
                Console.WriteLine($"消息有效性: {message.IsValid}");

                if (message is ModbusReadHoldingRegistersRequest request)
                {
                    Console.WriteLine($"从站地址: {request.SlaveAddress}");
                    Console.WriteLine($"功能码: 0x{request.FunctionCode:X2}");
                    Console.WriteLine($"起始地址: {request.StartAddress}");
                    Console.WriteLine($"寄存器数量: {request.RegisterCount}");
                }
            }
            else
            {
                Console.WriteLine("解析失败");
            }
        }
    }

    /// <summary>
    /// Modbus RTU 协议示例
    /// </summary>
    static async Task ModbusRtuExample(IServiceProvider serviceProvider)
    {
        Console.WriteLine("\n=== Modbus RTU 协议示例 ===");

        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

        // 注意：这个示例不会实际连接串口，仅演示协议功能
        Console.WriteLine("注意：此示例仅演示协议功能，不会实际连接串口设备。");

        try
        {
            // 创建串口配置（虚拟配置）
            var serialConfig = new SerialPortConfiguration
            {
                PortName = "COM1", // 虚拟端口
                BaudRate = 9600,
                DataBits = 8,
                Parity = System.IO.Ports.Parity.None,
                StopBits = System.IO.Ports.StopBits.One
            };

            // 创建串口服务（不实际打开）
            var serialPort = new SerialPortService(serialConfig,
                serviceProvider.GetService<ILogger<SerialPortService>>()!);

            // 创建协议解析器
            var modbusProtocol = new ModbusRtuProtocol(
                serviceProvider.GetService<ILogger<ModbusRtuProtocol>>());

            // 创建串口适配器
            var serialAdapter = new SerialPortAdapter(serialPort,
                serviceProvider.GetService<ILogger<SerialPortAdapter>>());

            // 创建协议管理器
            var protocolManager = new ProtocolManager(modbusProtocol, serialAdapter,
                serviceProvider.GetService<ILogger<ProtocolManager>>());

            // 订阅事件
            protocolManager.MessageReceived += (sender, e) =>
            {
                Console.WriteLine($"[事件] 接收到协议消息: {e.Message}");
            };

            protocolManager.ErrorOccurred += (sender, e) =>
            {
                Console.WriteLine($"[事件] 协议错误: {e.ErrorMessage}");
            };

            Console.WriteLine("协议管理器已创建并配置事件处理。");

            // 创建测试消息
            var readRequest = new ModbusReadHoldingRegistersRequest(1, 0, 10);
            Console.WriteLine($"创建读取请求: {readRequest}");

            // 序列化消息
            var serializedData = await modbusProtocol.SerializeAsync(readRequest);
            Console.WriteLine($"序列化数据: {string.Join(" ", serializedData.Select(b => $"0x{b:X2}"))}");

            // 模拟响应数据
            var responseData = new ushort[] { 100, 200, 300, 400, 500 };
            var readResponse = new ModbusReadHoldingRegistersResponse(1, responseData);
            Console.WriteLine($"创建读取响应: {readResponse}");

            // 序列化响应
            var responseBytes = await modbusProtocol.SerializeAsync(readResponse);
            Console.WriteLine($"响应数据: {string.Join(" ", responseBytes.Select(b => $"0x{b:X2}"))}");

            // 解析响应
            var parsedResponse = await modbusProtocol.ParseAsync(responseBytes);
            if (parsedResponse is ModbusReadHoldingRegistersResponse response)
            {
                Console.WriteLine($"解析响应成功，寄存器值: [{string.Join(", ", response.RegisterValues)}]");
            }

            // 释放资源
            protocolManager.Dispose();
            serialAdapter.Dispose();

            Console.WriteLine("Modbus RTU 协议示例完成。");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Modbus RTU 示例执行失败: {ex.Message}");
        }
    }

    /// <summary>
    /// CRC16 校验器示例
    /// </summary>
    static async Task Crc16ValidatorExample()
    {
        Console.WriteLine("\n=== CRC16 校验器示例 ===");

        var validator = new Alicres.Protocol.Validators.Crc16Validator();
        Console.WriteLine($"校验器名称: {validator.ValidatorName}");

        // 测试数据
        var testData = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 };
        Console.WriteLine($"原始数据: {string.Join(" ", testData.Select(b => $"0x{b:X2}"))}");

        // 计算 CRC
        var checksum = validator.CalculateChecksum(testData);
        Console.WriteLine($"CRC16 校验值: {string.Join(" ", checksum.Select(b => $"0x{b:X2}"))}");

        // 添加校验值
        var dataWithCrc = validator.AddChecksum(testData);
        Console.WriteLine($"带校验的数据: {string.Join(" ", dataWithCrc.Select(b => $"0x{b:X2}"))}");

        // 验证数据
        var isValid = await validator.ValidateAsync(dataWithCrc);
        Console.WriteLine($"数据校验结果: {(isValid ? "通过" : "失败")}");

        // 移除校验值
        var originalData = validator.RemoveChecksum(dataWithCrc);
        Console.WriteLine($"移除校验后: {string.Join(" ", originalData.Select(b => $"0x{b:X2}"))}");

        // 验证是否与原始数据一致
        var isIdentical = originalData.SequenceEqual(testData);
        Console.WriteLine($"数据一致性: {(isIdentical ? "一致" : "不一致")}");

        // 测试错误数据
        var corruptedData = (byte[])dataWithCrc.Clone();
        corruptedData[^1] = 0x00; // 破坏最后一个字节
        Console.WriteLine($"损坏的数据: {string.Join(" ", corruptedData.Select(b => $"0x{b:X2}"))}");

        var corruptedValid = await validator.ValidateAsync(corruptedData);
        Console.WriteLine($"损坏数据校验: {(corruptedValid ? "通过" : "失败")}");
    }

    /// <summary>
    /// 依赖注入示例
    /// </summary>
    static async Task DependencyInjectionExample(IServiceProvider serviceProvider)
    {
        Console.WriteLine("\n=== 依赖注入示例 ===");

        try
        {
            // 从依赖注入容器获取服务
            var modbusProtocol = serviceProvider.GetRequiredService<ModbusRtuProtocol>();
            Console.WriteLine($"获取到 Modbus RTU 协议: {modbusProtocol.ProtocolName}");

            var validator = serviceProvider.GetRequiredService<Alicres.Protocol.Validators.Crc16Validator>();
            Console.WriteLine($"获取到 CRC16 校验器: {validator.ValidatorName}");

            // 测试协议功能
            var testMessage = new ModbusReadHoldingRegistersRequest(1, 100, 5);
            Console.WriteLine($"创建测试消息: {testMessage}");

            var serializedData = await modbusProtocol.SerializeAsync(testMessage);
            Console.WriteLine($"序列化成功，数据长度: {serializedData.Length} 字节");

            var parsedMessage = await modbusProtocol.ParseAsync(serializedData);
            Console.WriteLine($"解析成功: {parsedMessage?.MessageType}");

            Console.WriteLine("依赖注入示例完成。");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"依赖注入示例失败: {ex.Message}");
        }
    }
}
