using System.Text;
using Alicres.SerialPort.Models;
using FluentAssertions;
using Xunit;

namespace Alicres.SerialPort.Tests.Models;

/// <summary>
/// SerialPortData 测试类
/// </summary>
public class SerialPortDataTests
{
    [Fact]
    public void Constructor_Default_ShouldInitializeWithEmptyData()
    {
        // Arrange & Act
        var data = new SerialPortData();

        // Assert
        data.RawData.Should().BeEmpty();
        data.Length.Should().Be(0);
        data.PortName.Should().BeEmpty();
        data.Direction.Should().Be(SerialPortDataDirection.Received);
        data.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
        data.IsEmpty.Should().BeTrue();
    }

    [Fact]
    public void Constructor_WithByteArray_ShouldInitializeCorrectly()
    {
        // Arrange
        var bytes = new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F }; // "Hello"
        const string portName = "COM1";
        const SerialPortDataDirection direction = SerialPortDataDirection.Sent;

        // Act
        var data = new SerialPortData(bytes, portName, direction);

        // Assert
        data.RawData.Should().BeEquivalentTo(bytes);
        data.Length.Should().Be(5);
        data.PortName.Should().Be(portName);
        data.Direction.Should().Be(direction);
        data.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
        data.IsEmpty.Should().BeFalse();
    }

    [Fact]
    public void Constructor_WithText_ShouldInitializeCorrectly()
    {
        // Arrange
        const string text = "Hello, World!";
        const string portName = "COM2";
        var encoding = Encoding.UTF8;
        const SerialPortDataDirection direction = SerialPortDataDirection.Received;

        // Act
        var data = new SerialPortData(text, portName, encoding, direction);

        // Assert
        data.RawData.Should().BeEquivalentTo(encoding.GetBytes(text));
        data.Length.Should().Be(encoding.GetBytes(text).Length);
        data.PortName.Should().Be(portName);
        data.Direction.Should().Be(direction);
        data.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
        data.IsEmpty.Should().BeFalse();
    }

    [Fact]
    public void Constructor_WithNullByteArray_ShouldInitializeWithEmptyData()
    {
        // Arrange
        byte[]? bytes = null;
        const string portName = "COM1";

        // Act
        var data = new SerialPortData(bytes!, portName);

        // Assert
        data.RawData.Should().BeEmpty();
        data.Length.Should().Be(0);
        data.IsEmpty.Should().BeTrue();
    }

    [Fact]
    public void Constructor_WithNullText_ShouldInitializeWithEmptyData()
    {
        // Arrange
        string? text = null;
        const string portName = "COM1";

        // Act
        var data = new SerialPortData(text!, portName);

        // Assert
        data.RawData.Should().BeEmpty();
        data.Length.Should().Be(0);
        data.IsEmpty.Should().BeTrue();
    }

    [Theory]
    [InlineData("Hello", "Hello")]
    [InlineData("你好", "你好")]
    [InlineData("", "")]
    public void ToText_WithUTF8Encoding_ShouldReturnCorrectText(string originalText, string expectedText)
    {
        // Arrange
        var data = new SerialPortData(originalText, "COM1", Encoding.UTF8);

        // Act
        var result = data.ToText(Encoding.UTF8);

        // Assert
        result.Should().Be(expectedText);
    }

    [Theory]
    [InlineData(new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F }, "48 65 6C 6C 6F")]
    [InlineData(new byte[] { 0xFF, 0x00, 0xAB }, "FF 00 AB")]
    [InlineData(new byte[] { }, "")]
    public void ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(byte[] bytes, string expectedHex)
    {
        // Arrange
        var data = new SerialPortData(bytes, "COM1");

        // Act
        var result = data.ToHexString();

        // Assert
        result.Should().Be(expectedHex);
    }

    [Theory]
    [InlineData(new byte[] { 0x48, 0x65, 0x6C }, "-", true, "48-65-6C")]
    [InlineData(new byte[] { 0x48, 0x65, 0x6C }, ":", false, "48:65:6c")]
    [InlineData(new byte[] { 0x48, 0x65, 0x6C }, "", true, "48656C")]
    public void ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(
        byte[] bytes, string separator, bool uppercase, string expectedHex)
    {
        // Arrange
        var data = new SerialPortData(bytes, "COM1");

        // Act
        var result = data.ToHexString(separator, uppercase);

        // Assert
        result.Should().Be(expectedHex);
    }

    [Theory]
    [InlineData("48 65 6C 6C 6F", new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F })]
    [InlineData("48-65-6C-6C-6F", new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F })]
    [InlineData("48:65:6C:6C:6F", new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F })]
    [InlineData("48656C6C6F", new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F })]
    [InlineData("", new byte[] { })]
    public void FromHexString_WithValidHex_ShouldReturnCorrectData(string hexString, byte[] expectedBytes)
    {
        // Arrange
        const string portName = "COM1";

        // Act
        var data = SerialPortData.FromHexString(hexString, portName);

        // Assert
        data.RawData.Should().BeEquivalentTo(expectedBytes);
        data.PortName.Should().Be(portName);
        data.Direction.Should().Be(SerialPortDataDirection.Sent);
    }

    [Theory]
    [InlineData("48 65 6C 6C 6")]  // 奇数长度
    [InlineData("GG")]             // 无效字符
    public void FromHexString_WithInvalidHex_ShouldThrowException(string invalidHex)
    {
        // Arrange
        const string portName = "COM1";

        // Act & Assert
        var action = () => SerialPortData.FromHexString(invalidHex, portName);
        action.Should().Throw<Exception>();
    }

    [Fact]
    public void FromHexString_WithNullOrWhiteSpace_ShouldReturnEmptyData()
    {
        // Arrange
        const string portName = "COM1";

        // Act
        var data1 = SerialPortData.FromHexString("", portName);
        var data2 = SerialPortData.FromHexString("   ", portName);

        // Assert
        data1.RawData.Should().BeEmpty();
        data1.IsEmpty.Should().BeTrue();
        data2.RawData.Should().BeEmpty();
        data2.IsEmpty.Should().BeTrue();
    }

    [Fact]
    public void GetDataCopy_ShouldReturnIndependentCopy()
    {
        // Arrange
        var originalBytes = new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F };
        var data = new SerialPortData(originalBytes, "COM1");

        // Act
        var copy = data.GetDataCopy();
        copy[0] = 0xFF; // 修改副本

        // Assert
        copy.Should().NotBeSameAs(data.RawData);
        data.RawData[0].Should().Be(0x48); // 原始数据不应被修改
        copy[0].Should().Be(0xFF);
    }

    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var bytes = new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F };
        var data = new SerialPortData(bytes, "COM1", SerialPortDataDirection.Sent);

        // Act
        var result = data.ToString();

        // Assert
        result.Should().Contain("COM1");
        result.Should().Contain("发送");
        result.Should().Contain("5 字节");
    }
}
