using System.Collections.Concurrent;
using System.Text;
using Microsoft.Extensions.Logging;
using Alicres.SerialPort.Interfaces;
using Alicres.SerialPort.Models;

namespace Alicres.SerialPort.Services;

/// <summary>
/// 串口管理器实现
/// </summary>
public class SerialPortManager : ISerialPortManager
{
    private readonly ILogger<SerialPortManager> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly ConcurrentDictionary<string, ISerialPortService> _serialPorts;
    private bool _disposed = false;

    /// <summary>
    /// 获取所有管理的串口服务
    /// </summary>
    public IReadOnlyDictionary<string, ISerialPortService> SerialPorts => _serialPorts;

    /// <summary>
    /// 串口添加事件
    /// </summary>
    public event EventHandler<SerialPortAddedEventArgs>? SerialPortAdded;

    /// <summary>
    /// 串口移除事件
    /// </summary>
    public event EventHandler<SerialPortRemovedEventArgs>? SerialPortRemoved;

    /// <summary>
    /// 全局数据接收事件
    /// </summary>
    public event EventHandler<SerialPortDataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 全局状态变化事件
    /// </summary>
    public event EventHandler<SerialPortStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 全局错误事件
    /// </summary>
    public event EventHandler<SerialPortErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="serviceProvider">服务提供程序</param>
    public SerialPortManager(ILogger<SerialPortManager> logger, IServiceProvider serviceProvider)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _serialPorts = new ConcurrentDictionary<string, ISerialPortService>();
    }

    /// <summary>
    /// 创建并添加串口服务
    /// </summary>
    /// <param name="configuration">串口配置</param>
    /// <returns>创建的串口服务</returns>
    /// <exception cref="ArgumentException">端口已存在时抛出</exception>
    /// <exception cref="Exceptions.SerialPortConfigurationException">配置无效时抛出</exception>
    public ISerialPortService CreateSerialPort(SerialPortConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        ThrowIfDisposed();

        if (_serialPorts.ContainsKey(configuration.PortName))
        {
            throw new ArgumentException($"端口 {configuration.PortName} 已存在", nameof(configuration));
        }

        var logger = _serviceProvider.GetService(typeof(ILogger<SerialPortService>)) as ILogger<SerialPortService>
                    ?? throw new InvalidOperationException("无法获取 ILogger<SerialPortService> 服务");

        var serialPortService = new SerialPortService(configuration, logger);
        AddSerialPort(serialPortService);

        _logger.LogInformation("创建串口服务: {PortName}", configuration.PortName);
        return serialPortService;
    }

    /// <summary>
    /// 添加现有的串口服务
    /// </summary>
    /// <param name="serialPortService">串口服务实例</param>
    /// <exception cref="ArgumentException">端口已存在时抛出</exception>
    public void AddSerialPort(ISerialPortService serialPortService)
    {
        ArgumentNullException.ThrowIfNull(serialPortService);
        ThrowIfDisposed();

        var portName = serialPortService.Configuration.PortName;
        
        if (!_serialPorts.TryAdd(portName, serialPortService))
        {
            throw new ArgumentException($"端口 {portName} 已存在", nameof(serialPortService));
        }

        // 订阅事件
        serialPortService.DataReceived += OnSerialPortDataReceived;
        serialPortService.StatusChanged += OnSerialPortStatusChanged;
        serialPortService.ErrorOccurred += OnSerialPortErrorOccurred;

        OnSerialPortAdded(new SerialPortAddedEventArgs(serialPortService));
        _logger.LogInformation("添加串口服务: {PortName}", portName);
    }

    /// <summary>
    /// 移除串口服务
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>如果成功移除返回 true，否则返回 false</returns>
    public bool RemoveSerialPort(string portName)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(portName);
        ThrowIfDisposed();

        if (_serialPorts.TryRemove(portName, out var serialPortService))
        {
            // 取消订阅事件
            serialPortService.DataReceived -= OnSerialPortDataReceived;
            serialPortService.StatusChanged -= OnSerialPortStatusChanged;
            serialPortService.ErrorOccurred -= OnSerialPortErrorOccurred;

            // 关闭并释放串口服务
            try
            {
                _ = serialPortService.CloseAsync();
                serialPortService.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "释放串口服务 {PortName} 时发生异常", portName);
            }

            OnSerialPortRemoved(new SerialPortRemovedEventArgs(portName));
            _logger.LogInformation("移除串口服务: {PortName}", portName);
            return true;
        }

        return false;
    }

    /// <summary>
    /// 获取指定端口的串口服务
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>串口服务实例，如果不存在返回 null</returns>
    public ISerialPortService? GetSerialPort(string portName)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(portName);
        ThrowIfDisposed();

        return _serialPorts.TryGetValue(portName, out var serialPortService) ? serialPortService : null;
    }

    /// <summary>
    /// 检查端口是否存在
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>如果存在返回 true，否则返回 false</returns>
    public bool ContainsPort(string portName)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(portName);
        ThrowIfDisposed();

        return _serialPorts.ContainsKey(portName);
    }

    /// <summary>
    /// 打开所有串口
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功打开的端口数量</returns>
    public async Task<int> OpenAllAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        var tasks = _serialPorts.Values.Select(async port =>
        {
            try
            {
                return await port.OpenAsync(cancellationToken) ? 1 : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开串口 {PortName} 失败", port.Configuration.PortName);
                return 0;
            }
        });

        var results = await Task.WhenAll(tasks);
        var successCount = results.Sum();

        _logger.LogInformation("批量打开串口完成，成功: {Success}/{Total}", successCount, _serialPorts.Count);
        return successCount;
    }

    /// <summary>
    /// 关闭所有串口
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功关闭的端口数量</returns>
    public async Task<int> CloseAllAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        var tasks = _serialPorts.Values.Select(async port =>
        {
            try
            {
                return await port.CloseAsync(cancellationToken) ? 1 : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "关闭串口 {PortName} 失败", port.Configuration.PortName);
                return 0;
            }
        });

        var results = await Task.WhenAll(tasks);
        var successCount = results.Sum();

        _logger.LogInformation("批量关闭串口完成，成功: {Success}/{Total}", successCount, _serialPorts.Count);
        return successCount;
    }

    /// <summary>
    /// 获取所有串口的状态
    /// </summary>
    /// <returns>端口状态字典</returns>
    public Dictionary<string, SerialPortStatus> GetAllStatus()
    {
        ThrowIfDisposed();

        return _serialPorts.ToDictionary(
            kvp => kvp.Key,
            kvp => kvp.Value.Status
        );
    }

    /// <summary>
    /// 获取系统中可用的串口列表
    /// </summary>
    /// <returns>可用串口名称数组</returns>
    public string[] GetAvailablePorts()
    {
        try
        {
            return System.IO.Ports.SerialPort.GetPortNames();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可用串口列表失败");
            return Array.Empty<string>();
        }
    }

    /// <summary>
    /// 广播数据到所有已连接的串口
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功发送的端口数量</returns>
    public async Task<int> BroadcastAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(data);
        ThrowIfDisposed();

        var connectedPorts = _serialPorts.Values.Where(port => port.IsConnected).ToList();

        if (connectedPorts.Count == 0)
        {
            _logger.LogWarning("没有已连接的串口可用于广播");
            return 0;
        }

        var tasks = connectedPorts.Select(async port =>
        {
            try
            {
                await port.SendAsync(data, cancellationToken);
                return 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "向串口 {PortName} 广播数据失败", port.Configuration.PortName);
                return 0;
            }
        });

        var results = await Task.WhenAll(tasks);
        var successCount = results.Sum();

        _logger.LogInformation("广播数据完成，成功: {Success}/{Total}，数据长度: {Length} 字节",
            successCount, connectedPorts.Count, data.Length);

        return successCount;
    }

    /// <summary>
    /// 广播文本到所有已连接的串口
    /// </summary>
    /// <param name="text">要发送的文本</param>
    /// <param name="encoding">编码方式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功发送的端口数量</returns>
    public async Task<int> BroadcastTextAsync(string text, Encoding? encoding = null, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(text);

        encoding ??= Encoding.UTF8;
        var data = encoding.GetBytes(text);

        return await BroadcastAsync(data, cancellationToken);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            // 关闭并释放所有串口服务
            var portNames = _serialPorts.Keys.ToList();
            foreach (var portName in portNames)
            {
                RemoveSerialPort(portName);
            }

            _serialPorts.Clear();
            _disposed = true;

            _logger.LogInformation("串口管理器已释放");
        }
    }

    #region 私有方法

    /// <summary>
    /// 串口数据接收事件处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnSerialPortDataReceived(object? sender, SerialPortDataReceivedEventArgs e)
    {
        OnDataReceived(e);
    }

    /// <summary>
    /// 串口状态变化事件处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnSerialPortStatusChanged(object? sender, SerialPortStatusChangedEventArgs e)
    {
        OnStatusChanged(e);
    }

    /// <summary>
    /// 串口错误事件处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnSerialPortErrorOccurred(object? sender, SerialPortErrorEventArgs e)
    {
        OnErrorOccurred(e);
    }

    /// <summary>
    /// 触发串口添加事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnSerialPortAdded(SerialPortAddedEventArgs e)
    {
        SerialPortAdded?.Invoke(this, e);
    }

    /// <summary>
    /// 触发串口移除事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnSerialPortRemoved(SerialPortRemovedEventArgs e)
    {
        SerialPortRemoved?.Invoke(this, e);
    }

    /// <summary>
    /// 触发数据接收事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnDataReceived(SerialPortDataReceivedEventArgs e)
    {
        DataReceived?.Invoke(this, e);
    }

    /// <summary>
    /// 触发状态变化事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnStatusChanged(SerialPortStatusChangedEventArgs e)
    {
        StatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// 触发错误事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnErrorOccurred(SerialPortErrorEventArgs e)
    {
        ErrorOccurred?.Invoke(this, e);
    }

    /// <summary>
    /// 检查是否已释放
    /// </summary>
    /// <exception cref="ObjectDisposedException">已释放时抛出</exception>
    private void ThrowIfDisposed()
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(SerialPortManager));
        }
    }

    #endregion
}
