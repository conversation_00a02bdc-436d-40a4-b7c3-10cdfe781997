# Alicres.Protocol

[![NuGet Version](https://img.shields.io/nuget/v/Alicres.Protocol.svg)](https://www.nuget.org/packages/Alicres.Protocol/)
[![NuGet Downloads](https://img.shields.io/nuget/dt/Alicres.Protocol.svg)](https://www.nuget.org/packages/Alicres.Protocol/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

Alicres.Protocol 是一个专业的 C# 通讯协议解析库，旨在为 .NET 应用程序提供可扩展、高性能的协议解析解决方案。本项目严格遵循 Alicres 系列库的开发规范，确保代码质量、一致性和可维护性。

## ✨ 核心特性

### 🔧 可扩展的协议框架
- 基于接口的协议解析器设计
- 支持自定义协议实现
- 插件化的协议注册机制

### 🚀 多传输层支持
- 串口传输适配器（与 Alicres.SerialPort 集成）
- TCP 传输适配器
- UDP 传输适配器
- 可扩展的传输层接口

### 📡 内置协议支持
- **Modbus RTU**：完整的 Modbus RTU 协议实现
- **Modbus ASCII**：Modbus ASCII 协议支持
- **自定义协议**：灵活的自定义协议框架

### 🛡️ 数据校验与完整性
- CRC16 校验算法
- 校验和计算
- 可扩展的数据校验器接口
- 自动数据完整性验证

### 📊 消息处理
- 异步消息解析
- 事件驱动的消息通知
- 消息序列化与反序列化
- 消息帧处理

### 🔧 依赖注入支持
- 原生支持 .NET 依赖注入
- 灵活的配置选项
- 易于集成到现有项目

## 📦 项目结构

```
Alicres.Protocol/
├── Interfaces/                      # 核心接口定义
│   ├── IProtocolParser.cs           # 协议解析器接口
│   ├── ITransportAdapter.cs         # 传输适配器接口
│   ├── IProtocolMessage.cs          # 协议消息接口
│   └── IProtocolValidator.cs        # 协议校验器接口
├── Models/                          # 数据模型
│   ├── ProtocolMessage.cs           # 协议消息基类
│   ├── ProtocolConfiguration.cs     # 协议配置
│   └── EventArgs/                   # 事件参数
├── Protocols/                       # 具体协议实现
│   ├── Modbus/                      # Modbus 协议族
│   │   ├── ModbusRtuProtocol.cs     # Modbus RTU 实现
│   │   ├── ModbusAsciiProtocol.cs   # Modbus ASCII 实现
│   │   └── Messages/                # Modbus 消息模型
│   └── Custom/                      # 自定义协议
├── Adapters/                        # 传输适配器
│   ├── SerialPortAdapter.cs         # 串口适配器
│   ├── TcpAdapter.cs                # TCP 适配器
│   └── UdpAdapter.cs                # UDP 适配器
├── Validators/                      # 数据校验器
│   ├── Crc16Validator.cs            # CRC16 校验
│   └── ChecksumValidator.cs         # 校验和校验
├── Services/                        # 核心服务
│   └── ProtocolManager.cs           # 协议管理器
├── Extensions/                      # 扩展方法
│   └── ServiceCollectionExtensions.cs # 依赖注入扩展
└── Constants/                       # 常量定义
    └── ProtocolConstants.cs         # 协议常量
```

## 🚀 快速开始

### 安装

```bash
dotnet add package Alicres.Protocol
dotnet add package Alicres.SerialPort  # 如果使用串口传输
```

### 基本使用

```csharp
using Alicres.Protocol.Services;
using Alicres.Protocol.Protocols.Modbus;
using Alicres.Protocol.Adapters;
using Alicres.SerialPort.Services;

// 1. 创建串口服务
var serialConfig = new SerialPortConfiguration 
{ 
    PortName = "COM1", 
    BaudRate = 9600 
};
var serialPort = new SerialPortService(serialConfig, logger);

// 2. 创建协议解析器
var modbusProtocol = new ModbusRtuProtocol();

// 3. 创建串口适配器
var serialAdapter = new SerialPortAdapter(serialPort);

// 4. 创建协议管理器
var protocolManager = new ProtocolManager(modbusProtocol, serialAdapter);

// 5. 订阅协议消息事件
protocolManager.MessageReceived += (sender, e) =>
{
    if (e.Message is ModbusReadHoldingRegistersResponse response)
    {
        Console.WriteLine($"读取到寄存器值: {string.Join(", ", response.RegisterValues)}");
    }
};

// 6. 打开连接并发送协议消息
await protocolManager.ConnectAsync();

var readRequest = new ModbusReadHoldingRegistersRequest
{
    SlaveAddress = 1,
    StartAddress = 0,
    RegisterCount = 10
};

await protocolManager.SendMessageAsync(readRequest);
```

### 依赖注入使用

```csharp
// 注册服务
services.AddAlicresProtocol(options =>
{
    options.DefaultProtocol = ProtocolType.ModbusRtu;
    options.EnableDataValidation = true;
});

// 使用服务
var protocolManager = serviceProvider.GetRequiredService<IProtocolManager>();
```

## 📋 技术规格

- **目标框架**: .NET 8.0
- **语言版本**: C# 12.0
- **包管理**: 中央包版本管理
- **测试框架**: xUnit + FluentAssertions + Moq
- **代码覆盖率**: ≥ 85%
- **文档**: 完整的 XML 注释

## 🧪 质量保证

### 单元测试
- **测试覆盖率**: 85%+ 代码覆盖率
- **测试框架**: xUnit, FluentAssertions, Moq
- **协议测试**: 完整的协议解析测试套件

### 代码质量
- **代码分析**: 启用 .NET 分析器
- **警告处理**: 将警告视为错误
- **代码格式**: EditorConfig 统一格式

### 构建配置
- **持续集成**: 自动化构建和测试
- **包生成**: 自动生成 NuGet 包
- **符号包**: 包含调试符号

## 📚 API 文档

### 核心接口

#### IProtocolParser
协议解析器接口，定义协议解析的核心功能。

#### ITransportAdapter
传输适配器接口，抽象不同的传输层实现。

#### IProtocolManager
协议管理器接口，统一管理协议解析和传输。

### 协议实现

#### ModbusRtuProtocol
完整的 Modbus RTU 协议实现，支持所有标准功能码。

#### ModbusAsciiProtocol
Modbus ASCII 协议实现，支持文本格式的 Modbus 通讯。

## 🔧 高级功能

### 自定义协议

```csharp
public class CustomProtocol : AbstractProtocolParser
{
    public override string ProtocolName => "CustomProtocol";
    
    public override async Task<IProtocolMessage?> ParseAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        // 实现自定义协议解析逻辑
        return new CustomProtocolMessage(data);
    }
}
```

### 数据校验

```csharp
// 使用 CRC16 校验
var validator = new Crc16Validator();
var isValid = validator.Validate(data);

// 自定义校验器
public class CustomValidator : IProtocolValidator
{
    public bool Validate(byte[] data) => /* 自定义校验逻辑 */;
}
```

## 🛠️ 开发环境

- **IDE**: Visual Studio 2022 / VS Code
- **SDK**: .NET 8.0 SDK
- **包管理器**: NuGet
- **版本控制**: Git

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！请遵循以下规范：

1. 遵循 Alicres 系列库开发规范
2. 确保代码覆盖率 ≥ 85%
3. 添加完整的 XML 文档注释
4. 通过所有单元测试

## 📞 支持与反馈

- **Gitee**: https://gitee.com/liam-gitee/alicres.git
- **Issues**: https://gitee.com/liam-gitee/alicres/issues

---

**Alicres.Protocol** - 让协议解析更简单！
