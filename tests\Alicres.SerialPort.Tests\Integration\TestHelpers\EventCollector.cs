using System.Collections.Concurrent;

namespace Alicres.SerialPort.Tests.Integration.TestHelpers;

/// <summary>
/// 事件收集器，用于收集和验证事件触发
/// </summary>
/// <typeparam name="T">事件参数类型</typeparam>
public class EventCollector<T> : IDisposable where T : EventArgs
{
    private readonly ConcurrentQueue<EventData<T>> _events = new();
    private readonly object _lockObject = new();
    private bool _disposed;

    /// <summary>
    /// 收集的事件数量
    /// </summary>
    public int Count => _events.Count;

    /// <summary>
    /// 是否有事件
    /// </summary>
    public bool HasEvents => !_events.IsEmpty;

    /// <summary>
    /// 所有收集的事件
    /// </summary>
    public IReadOnlyList<EventData<T>> Events
    {
        get
        {
            lock (_lockObject)
            {
                return _events.ToList();
            }
        }
    }

    /// <summary>
    /// 事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="args">事件参数</param>
    public void EventHandler(object? sender, T args)
    {
        if (_disposed) return;

        var eventData = new EventData<T>
        {
            Sender = sender,
            Args = args,
            Timestamp = DateTime.UtcNow
        };

        _events.Enqueue(eventData);
    }

    /// <summary>
    /// 等待指定数量的事件
    /// </summary>
    /// <param name="expectedCount">期望的事件数量</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>是否收集到足够的事件</returns>
    public async Task<bool> WaitForEventsAsync(int expectedCount, TimeSpan timeout)
    {
        var endTime = DateTime.UtcNow.Add(timeout);

        while (DateTime.UtcNow < endTime && Count < expectedCount)
        {
            await Task.Delay(50);
        }

        return Count >= expectedCount;
    }

    /// <summary>
    /// 等待第一个事件
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <returns>第一个事件数据，如果超时返回null</returns>
    public async Task<EventData<T>?> WaitForFirstEventAsync(TimeSpan timeout)
    {
        var success = await WaitForEventsAsync(1, timeout);
        return success ? GetFirstEvent() : null;
    }

    /// <summary>
    /// 获取第一个事件
    /// </summary>
    /// <returns>第一个事件数据</returns>
    public EventData<T>? GetFirstEvent()
    {
        return _events.TryDequeue(out var eventData) ? eventData : null;
    }

    /// <summary>
    /// 获取最后一个事件
    /// </summary>
    /// <returns>最后一个事件数据</returns>
    public EventData<T>? GetLastEvent()
    {
        lock (_lockObject)
        {
            return _events.LastOrDefault();
        }
    }

    /// <summary>
    /// 获取指定索引的事件
    /// </summary>
    /// <param name="index">索引</param>
    /// <returns>事件数据</returns>
    public EventData<T>? GetEvent(int index)
    {
        lock (_lockObject)
        {
            var events = _events.ToArray();
            return index >= 0 && index < events.Length ? events[index] : null;
        }
    }

    /// <summary>
    /// 获取指定时间范围内的事件
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns>事件列表</returns>
    public List<EventData<T>> GetEventsInTimeRange(DateTime startTime, DateTime endTime)
    {
        lock (_lockObject)
        {
            return _events
                .Where(e => e.Timestamp >= startTime && e.Timestamp <= endTime)
                .ToList();
        }
    }

    /// <summary>
    /// 按条件筛选事件
    /// </summary>
    /// <param name="predicate">筛选条件</param>
    /// <returns>筛选后的事件列表</returns>
    public List<EventData<T>> FilterEvents(Func<EventData<T>, bool> predicate)
    {
        lock (_lockObject)
        {
            return _events.Where(predicate).ToList();
        }
    }

    /// <summary>
    /// 验证事件序列
    /// </summary>
    /// <param name="validators">验证器列表</param>
    /// <returns>是否所有验证器都通过</returns>
    public bool ValidateEventSequence(params Func<EventData<T>, bool>[] validators)
    {
        lock (_lockObject)
        {
            var events = _events.ToArray();
            
            if (events.Length < validators.Length)
                return false;

            for (int i = 0; i < validators.Length; i++)
            {
                if (!validators[i](events[i]))
                    return false;
            }

            return true;
        }
    }

    /// <summary>
    /// 验证事件时间间隔
    /// </summary>
    /// <param name="expectedInterval">期望的时间间隔</param>
    /// <param name="tolerance">容差</param>
    /// <returns>是否在容差范围内</returns>
    public bool ValidateEventTiming(TimeSpan expectedInterval, TimeSpan tolerance)
    {
        lock (_lockObject)
        {
            var events = _events.ToArray();
            
            if (events.Length < 2)
                return false;

            for (int i = 1; i < events.Length; i++)
            {
                var actualInterval = events[i].Timestamp - events[i - 1].Timestamp;
                var difference = Math.Abs((actualInterval - expectedInterval).TotalMilliseconds);
                
                if (difference > tolerance.TotalMilliseconds)
                    return false;
            }

            return true;
        }
    }

    /// <summary>
    /// 清空收集的事件
    /// </summary>
    public void Clear()
    {
        lock (_lockObject)
        {
            while (_events.TryDequeue(out _)) { }
        }
    }

    /// <summary>
    /// 获取事件统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public EventStatistics GetStatistics()
    {
        lock (_lockObject)
        {
            var events = _events.ToArray();
            
            if (events.Length == 0)
            {
                return new EventStatistics
                {
                    TotalCount = 0,
                    FirstEventTime = null,
                    LastEventTime = null,
                    AverageInterval = TimeSpan.Zero,
                    MinInterval = TimeSpan.Zero,
                    MaxInterval = TimeSpan.Zero
                };
            }

            var intervals = new List<TimeSpan>();
            for (int i = 1; i < events.Length; i++)
            {
                intervals.Add(events[i].Timestamp - events[i - 1].Timestamp);
            }

            return new EventStatistics
            {
                TotalCount = events.Length,
                FirstEventTime = events[0].Timestamp,
                LastEventTime = events[^1].Timestamp,
                AverageInterval = intervals.Count > 0 
                    ? TimeSpan.FromTicks((long)intervals.Average(i => i.Ticks)) 
                    : TimeSpan.Zero,
                MinInterval = intervals.Count > 0 ? intervals.Min() : TimeSpan.Zero,
                MaxInterval = intervals.Count > 0 ? intervals.Max() : TimeSpan.Zero
            };
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            Clear();
            _disposed = true;
        }
    }
}

/// <summary>
/// 事件数据
/// </summary>
/// <typeparam name="T">事件参数类型</typeparam>
public class EventData<T> where T : EventArgs
{
    /// <summary>
    /// 发送者
    /// </summary>
    public object? Sender { get; set; }

    /// <summary>
    /// 事件参数
    /// </summary>
    public T Args { get; set; } = default!;

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 事件统计信息
/// </summary>
public class EventStatistics
{
    /// <summary>
    /// 总事件数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 第一个事件时间
    /// </summary>
    public DateTime? FirstEventTime { get; set; }

    /// <summary>
    /// 最后一个事件时间
    /// </summary>
    public DateTime? LastEventTime { get; set; }

    /// <summary>
    /// 平均间隔
    /// </summary>
    public TimeSpan AverageInterval { get; set; }

    /// <summary>
    /// 最小间隔
    /// </summary>
    public TimeSpan MinInterval { get; set; }

    /// <summary>
    /// 最大间隔
    /// </summary>
    public TimeSpan MaxInterval { get; set; }

    /// <summary>
    /// 总持续时间
    /// </summary>
    public TimeSpan TotalDuration => LastEventTime.HasValue && FirstEventTime.HasValue 
        ? LastEventTime.Value - FirstEventTime.Value 
        : TimeSpan.Zero;
}
