using Alicres.SerialPort.Interfaces;
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Tests.Integration.TestHelpers;
using FluentAssertions;
using System.Text;
using Xunit;

namespace Alicres.SerialPort.Tests.Integration;

/// <summary>
/// 基础集成测试
/// </summary>
public class BasicIntegrationTests : IntegrationTestBase
{
    /// <summary>
    /// 测试基本的串口服务生命周期
    /// </summary>
    [Fact]
    public async Task SerialPortService_BasicLifecycle_ShouldWorkCorrectly()
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_BASIC_TEST");
        var service = CreateSerialPortService(configuration);

        var statusCollector = CreateEventCollector<SerialPortStatusChangedEventArgs>();
        service.StatusChanged += statusCollector.EventHandler;

        try
        {
            // Act & Assert - 步骤1：验证初始状态
            service.IsConnected.Should().BeFalse("初始状态应该是未连接");
            service.Status.ConnectionState.Should().Be(SerialPortConnectionState.Disconnected);
            service.Configuration.PortName.Should().Be("COM_BASIC_TEST");

            // 步骤2：打开连接
            var openResult = await service.OpenAsync();
            openResult.Should().BeTrue("串口应该成功打开");
            service.IsConnected.Should().BeTrue("打开后应该处于连接状态");

            // 验证状态变化事件（可能有多个状态变化：Connecting -> Connected）
            var statusEventReceived = await statusCollector.WaitForEventsAsync(1, TimeSpan.FromSeconds(3));
            statusEventReceived.Should().BeTrue("应该触发状态变化事件");

            // 获取最后一个状态变化事件（应该是Connected状态）
            var allEvents = statusCollector.Events;
            allEvents.Should().NotBeEmpty("应该有状态变化事件");
            var lastEvent = allEvents.Last();
            lastEvent.Args.CurrentState.Should().Be(SerialPortConnectionState.Connected);

            // 步骤3：发送数据
            var testMessage = "Hello, Serial Port Integration Test!";
            var sentBytes = await service.SendTextAsync(testMessage);
            sentBytes.Should().BeGreaterThan(0, "应该成功发送数据");
            sentBytes.Should().Be(Encoding.UTF8.GetByteCount(testMessage), "发送的字节数应该与数据长度一致");

            // 验证统计信息
            service.Status.BytesSent.Should().BeGreaterThan(0, "应该记录发送的字节数");

            // 步骤4：关闭连接
            var closeResult = await service.CloseAsync();
            closeResult.Should().BeTrue("串口应该成功关闭");
            service.IsConnected.Should().BeFalse("关闭后应该处于断开状态");

            // 验证最终状态
            service.Status.ConnectionState.Should().Be(SerialPortConnectionState.Disconnected);
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试串口管理器基本功能
    /// </summary>
    [Fact]
    public async Task SerialPortManager_BasicOperations_ShouldWorkCorrectly()
    {
        // Arrange
        var manager = CreateSerialPortManager();
        var config1 = TestDataGenerator.CreateStandardConfiguration("COM_MGR_1");
        var config2 = TestDataGenerator.CreateStandardConfiguration("COM_MGR_2");

        try
        {
            // Act & Assert - 步骤1：创建串口服务
            var service1 = manager.CreateSerialPort(config1);
            var service2 = manager.CreateSerialPort(config2);

            manager.SerialPorts.Should().HaveCount(2, "管理器应该包含2个串口服务");
            manager.ContainsPort("COM_MGR_1").Should().BeTrue();
            manager.ContainsPort("COM_MGR_2").Should().BeTrue();

            // 步骤2：批量打开串口
            var openedCount = await manager.OpenAllAsync();
            openedCount.Should().Be(2, "应该成功打开2个串口");

            service1.IsConnected.Should().BeTrue();
            service2.IsConnected.Should().BeTrue();

            // 步骤3：测试广播功能
            var broadcastMessage = "Broadcast test message";
            var broadcastData = Encoding.UTF8.GetBytes(broadcastMessage);
            
            var broadcastCount = await manager.BroadcastAsync(broadcastData);
            broadcastCount.Should().Be(2, "应该向2个串口广播数据");

            // 验证每个服务都收到了数据
            service1.Status.BytesSent.Should().BeGreaterThan(0);
            service2.Status.BytesSent.Should().BeGreaterThan(0);

            // 步骤4：获取所有状态
            var allStatuses = manager.GetAllStatus();
            allStatuses.Should().HaveCount(2, "应该返回2个串口的状态");
            allStatuses.Keys.Should().Contain(new[] { "COM_MGR_1", "COM_MGR_2" });

            // 步骤5：移除一个串口
            var removeResult = manager.RemoveSerialPort("COM_MGR_2");
            removeResult.Should().BeTrue("应该成功移除串口");
            manager.SerialPorts.Should().HaveCount(1, "移除后应该剩余1个串口");

            // 步骤6：关闭剩余串口
            var closedCount = await manager.CloseAllAsync();
            closedCount.Should().Be(1, "应该成功关闭剩余的1个串口");
        }
        finally
        {
            await manager.CloseAllAsync();
        }
    }

    /// <summary>
    /// 测试配置更新功能
    /// </summary>
    [Fact]
    public async Task SerialPortService_ConfigurationUpdate_ShouldApplyCorrectly()
    {
        // Arrange
        var initialConfig = TestDataGenerator.CreateStandardConfiguration("COM_CONFIG_UPDATE");
        var service = CreateSerialPortService(initialConfig);

        try
        {
            // Act & Assert - 步骤1：验证初始配置
            service.Configuration.Should().BeEquivalentTo(initialConfig);
            service.Configuration.BaudRate.Should().Be(9600);

            // 步骤2：更新配置
            var newConfig = TestDataGenerator.CreateStandardConfiguration("COM_CONFIG_UPDATE");
            newConfig.BaudRate = 115200;
            newConfig.DataBits = 7;
            newConfig.EnableAutoReconnect = true;

            service.Configure(newConfig);

            // 验证配置更新
            service.Configuration.Should().BeEquivalentTo(newConfig);
            service.Configuration.BaudRate.Should().Be(115200);
            service.Configuration.DataBits.Should().Be(7);
            service.Configuration.EnableAutoReconnect.Should().BeTrue();

            // 步骤3：使用新配置打开连接
            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // 验证新配置生效
            service.Status.PortName.Should().Be(newConfig.PortName);
            service.Status.ConnectionState.Should().Be(SerialPortConnectionState.Connected);
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试数据传输完整性
    /// </summary>
    [Theory]
    [InlineData(100)]
    [InlineData(1024)]
    [InlineData(4096)]
    public async Task DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(int dataSize)
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration($"COM_DATA_{dataSize}");
        var service = CreateSerialPortService(configuration);
        var originalData = TestDataGenerator.GenerateTestData(dataSize);

        try
        {
            // Act
            await service.OpenAsync();
            var sentBytes = await service.SendAsync(originalData);

            // Assert
            sentBytes.Should().Be(originalData.Length, "发送的字节数应该与原始数据长度一致");
            service.Status.BytesSent.Should().Be(originalData.Length, "统计信息应该正确记录发送字节数");
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试错误处理
    /// </summary>
    [Fact]
    public async Task SerialPortService_ErrorHandling_ShouldHandleErrorsGracefully()
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_NORMAL_TEST");
        var service = CreateSerialPortService(configuration);

        var errorCollector = CreateEventCollector<SerialPortErrorEventArgs>();
        service.ErrorOccurred += errorCollector.EventHandler;

        try
        {
            // Act & Assert - 步骤1：在未连接状态下尝试发送数据
            var sendAction = async () => await service.SendTextAsync("Should fail");
            await sendAction.Should().ThrowAsync<Exception>("未连接状态下发送应该失败");

            // 步骤2：打开服务并尝试发送空数据
            var openResult = await service.OpenAsync();
            openResult.Should().BeTrue("服务应该能够正常打开");

            var nullDataAction = async () => await service.SendAsync(null!);
            await nullDataAction.Should().ThrowAsync<ArgumentNullException>("发送空数据应该抛出异常");

            // 步骤3：验证服务仍然可用
            service.IsConnected.Should().BeTrue("错误处理后服务应该仍然可用");

            var validData = TestDataGenerator.GenerateTestText(50);
            var sentBytes = await service.SendTextAsync(validData);
            sentBytes.Should().BeGreaterThan(0, "错误处理后应该能正常发送数据");
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试并发操作
    /// </summary>
    [Fact]
    public async Task SerialPortService_ConcurrentOperations_ShouldHandleParallelRequests()
    {
        // Arrange
        var configuration = TestDataGenerator.CreateStandardConfiguration("COM_CONCURRENT");
        var service = CreateSerialPortService(configuration);

        try
        {
            // Act
            await service.OpenAsync();

            // 并发发送多个消息
            var tasks = new List<Task<int>>();
            for (int i = 0; i < 5; i++)
            {
                var message = $"Concurrent message {i}";
                tasks.Add(service.SendTextAsync(message));
            }

            var results = await Task.WhenAll(tasks);

            // Assert
            results.Should().OnlyContain(r => r > 0, "所有并发发送操作都应该成功");
            service.Status.BytesSent.Should().BeGreaterThan(0, "应该记录所有发送的字节数");
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }

    /// <summary>
    /// 测试高级缓冲配置
    /// </summary>
    [Fact]
    public async Task SerialPortService_AdvancedBuffering_ShouldConfigureCorrectly()
    {
        // Arrange
        var configuration = TestDataGenerator.CreateAdvancedBufferingConfiguration("COM_ADVANCED");
        var service = CreateSerialPortService(configuration);

        try
        {
            // Act & Assert
            service.Configuration.EnableAdvancedBuffering.Should().BeTrue("应该启用高级缓冲");
            service.Configuration.DataQueueMaxLength.Should().Be(1000);
            service.Configuration.BufferOverflowStrategy.Should().Be(BufferOverflowStrategy.DropOldest);

            await service.OpenAsync();
            service.IsConnected.Should().BeTrue();

            // 发送一些数据测试缓冲功能
            var testData = TestDataGenerator.GenerateTestData(2048);
            var sentBytes = await service.SendAsync(testData);
            sentBytes.Should().Be(testData.Length);
        }
        finally
        {
            if (service.IsConnected)
            {
                await service.CloseAsync();
            }
        }
    }
}
