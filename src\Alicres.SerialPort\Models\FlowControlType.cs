namespace Alicres.SerialPort.Models;

/// <summary>
/// 流控制类型枚举
/// </summary>
public enum FlowControlType
{
    /// <summary>
    /// 无流控制
    /// </summary>
    None,

    /// <summary>
    /// RTS/CTS 硬件流控制
    /// </summary>
    RtsCts,

    /// <summary>
    /// XON/XOFF 软件流控制
    /// </summary>
    XonXoff,

    /// <summary>
    /// 同时使用硬件和软件流控制
    /// </summary>
    Both
}

/// <summary>
/// 流控制状态枚举
/// </summary>
public enum FlowControlStatus
{
    /// <summary>
    /// 正常状态，可以发送数据
    /// </summary>
    Normal,

    /// <summary>
    /// 暂停状态，不能发送数据
    /// </summary>
    Paused,

    /// <summary>
    /// 拥塞状态，发送速度受限
    /// </summary>
    Congested,

    /// <summary>
    /// 错误状态，流控制出现问题
    /// </summary>
    Error
}
