using Alicres.Protocol.Interfaces;

namespace Alicres.Protocol.Validators;

/// <summary>
/// CRC16 校验器，实现 CRC16-MODBUS 算法
/// </summary>
public class Crc16Validator : IProtocolValidator
{
    /// <summary>
    /// CRC16 查找表
    /// </summary>
    private static readonly ushort[] Crc16Table = new ushort[256];

    /// <summary>
    /// 校验器名称
    /// </summary>
    public string ValidatorName => "CRC16-MODBUS";

    /// <summary>
    /// 静态构造函数，初始化 CRC16 查找表
    /// </summary>
    static Crc16Validator()
    {
        const ushort polynomial = 0xA001; // CRC16-MODBUS 多项式

        for (int i = 0; i < 256; i++)
        {
            ushort crc = (ushort)i;
            for (int j = 0; j < 8; j++)
            {
                if ((crc & 1) != 0)
                {
                    crc = (ushort)((crc >> 1) ^ polynomial);
                }
                else
                {
                    crc >>= 1;
                }
            }
            Crc16Table[i] = crc;
        }
    }

    /// <summary>
    /// 验证数据完整性
    /// </summary>
    /// <param name="data">待验证的数据</param>
    /// <returns>如果数据有效返回 true，否则返回 false</returns>
    public bool Validate(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (data.Length < 2)
            return false;

        // 分离数据和CRC
        var dataWithoutCrc = new byte[data.Length - 2];
        Array.Copy(data, dataWithoutCrc, data.Length - 2);

        var expectedCrc = BitConverter.ToUInt16(data, data.Length - 2);
        var actualCrc = CalculateCrc16(dataWithoutCrc);

        return expectedCrc == actualCrc;
    }

    /// <summary>
    /// 验证数据完整性（异步）
    /// </summary>
    /// <param name="data">待验证的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果数据有效返回 true，否则返回 false</returns>
    public Task<bool> ValidateAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(Validate(data));
    }

    /// <summary>
    /// 计算数据的校验值
    /// </summary>
    /// <param name="data">待计算的数据</param>
    /// <returns>校验值</returns>
    public byte[] CalculateChecksum(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        var crc = CalculateCrc16(data);
        return BitConverter.GetBytes(crc);
    }

    /// <summary>
    /// 为数据添加校验信息
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <returns>包含校验信息的数据</returns>
    public byte[] AddChecksum(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        var checksum = CalculateChecksum(data);
        var result = new byte[data.Length + checksum.Length];
        
        Array.Copy(data, result, data.Length);
        Array.Copy(checksum, 0, result, data.Length, checksum.Length);
        
        return result;
    }

    /// <summary>
    /// 从数据中移除校验信息
    /// </summary>
    /// <param name="data">包含校验信息的数据</param>
    /// <returns>移除校验信息后的原始数据</returns>
    public byte[] RemoveChecksum(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (data.Length < 2)
            throw new ArgumentException("数据长度不足，无法移除CRC校验", nameof(data));

        var result = new byte[data.Length - 2];
        Array.Copy(data, result, data.Length - 2);
        return result;
    }

    /// <summary>
    /// 计算 CRC16 值
    /// </summary>
    /// <param name="data">待计算的数据</param>
    /// <returns>CRC16 值</returns>
    public static ushort CalculateCrc16(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        ushort crc = 0xFFFF;

        foreach (byte b in data)
        {
            byte tableIndex = (byte)(crc ^ b);
            crc = (ushort)((crc >> 8) ^ Crc16Table[tableIndex]);
        }

        return crc;
    }

    /// <summary>
    /// 计算 CRC16 值（指定数据范围）
    /// </summary>
    /// <param name="data">数据数组</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="count">计算字节数</param>
    /// <returns>CRC16 值</returns>
    public static ushort CalculateCrc16(byte[] data, int offset, int count)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (offset < 0 || offset >= data.Length)
            throw new ArgumentOutOfRangeException(nameof(offset));

        if (count < 0 || offset + count > data.Length)
            throw new ArgumentOutOfRangeException(nameof(count));

        ushort crc = 0xFFFF;

        for (int i = offset; i < offset + count; i++)
        {
            byte tableIndex = (byte)(crc ^ data[i]);
            crc = (ushort)((crc >> 8) ^ Crc16Table[tableIndex]);
        }

        return crc;
    }

    /// <summary>
    /// 验证 CRC16 校验值
    /// </summary>
    /// <param name="data">包含CRC的完整数据</param>
    /// <returns>如果校验通过返回 true，否则返回 false</returns>
    public static bool VerifyCrc16(byte[] data)
    {
        var validator = new Crc16Validator();
        return validator.Validate(data);
    }
}
