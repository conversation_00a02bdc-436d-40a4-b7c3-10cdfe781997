using Alicres.Protocol.Models.EventArgs;

namespace Alicres.Protocol.Interfaces;

/// <summary>
/// 消息帧处理接口，定义消息分帧和重组的功能
/// </summary>
public interface IMessageFraming
{
    /// <summary>
    /// 帧处理器名称
    /// </summary>
    string FramingName { get; }

    /// <summary>
    /// 帧模式
    /// </summary>
    FramingMode Mode { get; }

    /// <summary>
    /// 是否启用帧处理
    /// </summary>
    bool IsEnabled { get; set; }

    /// <summary>
    /// 处理接收到的数据，提取完整的消息帧
    /// </summary>
    /// <param name="data">接收到的原始数据</param>
    /// <returns>提取出的完整消息帧列表</returns>
    List<byte[]> ProcessIncomingData(byte[] data);

    /// <summary>
    /// 为发送的消息添加帧信息
    /// </summary>
    /// <param name="message">原始消息数据</param>
    /// <returns>添加帧信息后的数据</returns>
    byte[] FrameMessage(byte[] message);

    /// <summary>
    /// 从帧数据中提取原始消息
    /// </summary>
    /// <param name="framedData">包含帧信息的数据</param>
    /// <returns>提取出的原始消息数据</returns>
    byte[] UnframeMessage(byte[] framedData);

    /// <summary>
    /// 检查数据是否包含完整的帧
    /// </summary>
    /// <param name="data">待检查的数据</param>
    /// <returns>如果包含完整帧返回 true，否则返回 false</returns>
    bool HasCompleteFrame(byte[] data);

    /// <summary>
    /// 获取帧的预期长度
    /// </summary>
    /// <param name="data">帧数据的开始部分</param>
    /// <returns>帧的预期总长度，如果无法确定返回 -1</returns>
    int GetExpectedFrameLength(byte[] data);

    /// <summary>
    /// 重置帧处理器状态
    /// </summary>
    void Reset();

    /// <summary>
    /// 完整帧提取事件
    /// </summary>
    event EventHandler<FrameExtractedEventArgs>? FrameExtracted;

    /// <summary>
    /// 帧处理错误事件
    /// </summary>
    event EventHandler<FramingErrorEventArgs>? FramingError;
}

/// <summary>
/// 帧模式枚举
/// </summary>
public enum FramingMode
{
    /// <summary>
    /// 无帧处理
    /// </summary>
    None,

    /// <summary>
    /// 固定长度帧
    /// </summary>
    FixedLength,

    /// <summary>
    /// 分隔符帧
    /// </summary>
    Delimiter,

    /// <summary>
    /// 长度前缀帧
    /// </summary>
    LengthPrefix,

    /// <summary>
    /// 起始和结束分隔符帧
    /// </summary>
    StartEndDelimiter,

    /// <summary>
    /// 自定义帧格式
    /// </summary>
    Custom
}
