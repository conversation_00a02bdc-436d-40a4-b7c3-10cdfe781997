﻿<?xml version="1.0" encoding="utf-8"?>
<TestRun id="bf04eca3-7fb6-4e07-8536-2163e0df797b" name="wa<PERSON><PERSON><PERSON><PERSON>@JD-ITA028088-PC 2025-06-12 13:37:26" runUser="AUXGROUP\wangzhuhui" xmlns="http://microsoft.com/schemas/VisualStudio/TeamTest/2010">
  <Times creation="2025-06-12T13:37:26.7819697+08:00" queuing="2025-06-12T13:37:26.7819699+08:00" start="2025-06-12T13:37:24.7663610+08:00" finish="2025-06-12T13:37:28.7682976+08:00" />
  <TestSettings name="default" id="4752d619-75cf-4c8d-8908-e929dafcd32b">
    <Deployment runDeploymentRoot="wangzhuhui_JD-ITA028088-PC_2025-06-12_13_37_26" />
  </TestSettings>
  <Results>
    <UnitTestResult executionId="8ad8ff1e-7453-43a0-8fde-d6ec5e47d769" testId="a17ba197-677f-433a-d98b-90b6dba91665" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: None)" computerName="JD-ITA028088-PC" duration="00:00:00.0003348" startTime="2025-06-12T13:37:26.8148287+08:00" endTime="2025-06-12T13:37:26.8148288+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8ad8ff1e-7453-43a0-8fde-d6ec5e47d769" />
    <UnitTestResult executionId="8c31e9ab-4957-4893-9316-5e68cd5e0ed6" testId="d2dd8c1b-a46c-c63e-2bb9-e11f3f0fe385" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Constructor_WithNullLogger_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0004917" startTime="2025-06-12T13:37:26.8259656+08:00" endTime="2025-06-12T13:37:26.8259657+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8c31e9ab-4957-4893-9316-5e68cd5e0ed6" />
    <UnitTestResult executionId="e3079799-39a1-418d-ac27-ebb5bef8edd3" testId="acca298d-8d66-e074-22dd-fdf90888635f" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ClearSendBuffer_WhenNotConnected_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0005470" startTime="2025-06-12T13:37:26.8244883+08:00" endTime="2025-06-12T13:37:26.8244883+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e3079799-39a1-418d-ac27-ebb5bef8edd3" />
    <UnitTestResult executionId="f6ad7a10-f070-4ce5-ab5b-82a9ca0b875b" testId="023e96e9-d51b-a358-64ce-ba11d0feba6d" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0002699" startTime="2025-06-12T13:37:26.7839365+08:00" endTime="2025-06-12T13:37:26.7839366+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f6ad7a10-f070-4ce5-ab5b-82a9ca0b875b" />
    <UnitTestResult executionId="9b71647f-9881-457a-bb7c-0c1856991431" testId="0e388063-0d1d-fee8-e535-f6ea426ca6a0" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithInvalidHex_ShouldThrowException(invalidHex: &quot;GG&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0003951" startTime="2025-06-12T13:37:26.7727609+08:00" endTime="2025-06-12T13:37:26.7727610+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9b71647f-9881-457a-bb7c-0c1856991431" />
    <UnitTestResult executionId="bb0984bb-f693-4bea-97d9-39fa9a767c02" testId="2a2a8bb1-f3b5-0d08-191a-eec8cd12af85" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_TinyDataPackets_ShouldHandleCorrectly(dataSize: 1)" computerName="JD-ITA028088-PC" duration="00:00:00.0009370" startTime="2025-06-12T13:37:26.9504122+08:00" endTime="2025-06-12T13:37:26.9504124+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="bb0984bb-f693-4bea-97d9-39fa9a767c02" />
    <UnitTestResult executionId="c022c2e3-9dde-45aa-897e-07b502b9b107" testId="dee417d5-57ab-c1ce-a824-beeb5380368e" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_ContinuousOverflow_ShouldMaintainStability" computerName="JD-ITA028088-PC" duration="00:00:00.0011441" startTime="2025-06-12T13:37:26.8164608+08:00" endTime="2025-06-12T13:37:26.8164608+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c022c2e3-9dde-45aa-897e-07b502b9b107" />
    <UnitTestResult executionId="2a99b7ff-36b0-4f74-89f6-f6fbbc53f425" testId="08289273-2527-c675-4a6a-0bcbc2ae148c" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_TinyDataPackets_ShouldHandleCorrectly(dataSize: 2)" computerName="JD-ITA028088-PC" duration="00:00:00.0011455" startTime="2025-06-12T13:37:26.9493853+08:00" endTime="2025-06-12T13:37:26.9493854+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2a99b7ff-36b0-4f74-89f6-f6fbbc53f425" />
    <UnitTestResult executionId="0e821853-5a2d-447b-a432-a4dfdbf2f6eb" testId="beab0b90-bded-b34e-a1b0-0889a62c7bc7" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithNullBuffer_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0009175" startTime="2025-06-12T13:37:26.8293472+08:00" endTime="2025-06-12T13:37:26.8293473+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0e821853-5a2d-447b-a432-a4dfdbf2f6eb" />
    <UnitTestResult executionId="2dab2e59-a053-4d4f-a9e6-ae50b35eee0e" testId="f60c8033-5a87-0f3a-3088-48468fd9301b" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0002050" startTime="2025-06-12T13:37:26.7909748+08:00" endTime="2025-06-12T13:37:26.7909748+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2dab2e59-a053-4d4f-a9e6-ae50b35eee0e" />
    <UnitTestResult executionId="7624ebe5-daa1-41a9-bdc5-479922f746f5" testId="38e1a48a-8126-3cc7-e8ac-60f91e67cb3d" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 9600, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0001993" startTime="2025-06-12T13:37:26.7814000+08:00" endTime="2025-06-12T13:37:26.7814001+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7624ebe5-daa1-41a9-bdc5-479922f746f5" />
    <UnitTestResult executionId="4ccac0a7-4801-498e-9763-eca6b81876ec" testId="82d8705e-825e-feba-4da5-acda164c6f53" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0005458" startTime="2025-06-12T13:37:26.7812379+08:00" endTime="2025-06-12T13:37:26.7812380+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4ccac0a7-4801-498e-9763-eca6b81876ec" />
    <UnitTestResult executionId="c6f681e4-218a-4edc-a95f-2e10c5a3472e" testId="aa267d8a-b773-ddc4-6cfe-031c9347f3d1" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0016461" startTime="2025-06-12T13:37:26.7806522+08:00" endTime="2025-06-12T13:37:26.7806523+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c6f681e4-218a-4edc-a95f-2e10c5a3472e" />
    <UnitTestResult executionId="c2e0789f-125e-4c24-bc5d-83a8875bc774" testId="c029ac10-2236-0f3e-f243-4fb9689c404b" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 4, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000080" startTime="2025-06-12T13:37:26.7720197+08:00" endTime="2025-06-12T13:37:26.7720198+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c2e0789f-125e-4c24-bc5d-83a8875bc774" />
    <UnitTestResult executionId="785951b6-c82b-4edf-98a2-7dc6ef60871b" testId="faacff99-6157-5e13-4b0f-4d4468903363" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48:65:6C:6C:6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" computerName="JD-ITA028088-PC" duration="00:00:00.0266500" startTime="2025-06-12T13:37:26.8008942+08:00" endTime="2025-06-12T13:37:26.8008942+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="785951b6-c82b-4edf-98a2-7dc6ef60871b" />
    <UnitTestResult executionId="696dd883-f8f7-4d27-9cb3-91e8a9a3fede" testId="ee05eb54-5290-18f9-2d55-e832c1a8888d" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_Default_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0004001" startTime="2025-06-12T13:37:26.7957965+08:00" endTime="2025-06-12T13:37:26.7957965+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="696dd883-f8f7-4d27-9cb3-91e8a9a3fede" />
    <UnitTestResult executionId="5e5dec9d-7f15-47dd-a5f6-18b65cb07788" testId="1b9f49aa-b9ff-fd05-f03b-43ccb23d82e6" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0005112" startTime="2025-06-12T13:37:26.7870147+08:00" endTime="2025-06-12T13:37:26.7870147+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5e5dec9d-7f15-47dd-a5f6-18b65cb07788" />
    <UnitTestResult executionId="6fcc4274-e979-4c64-9621-d44d4a445e87" testId="8b2e14a6-d10e-1c5b-c949-007826a28517" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.GetAvailablePorts_ShouldReturnPortArray" computerName="JD-ITA028088-PC" duration="00:00:00.0024623" startTime="2025-06-12T13:37:26.8205576+08:00" endTime="2025-06-12T13:37:26.8205576+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6fcc4274-e979-4c64-9621-d44d4a445e87" />
    <UnitTestResult executionId="2253ba2d-649a-44b3-8b47-a7e9c27a57cd" testId="c783c277-08d3-0e65-e123-287a21c8b95a" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 0, maxAttempts: -1, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001800" startTime="2025-06-12T13:37:26.7892658+08:00" endTime="2025-06-12T13:37:26.7892658+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2253ba2d-649a-44b3-8b47-a7e9c27a57cd" />
    <UnitTestResult executionId="13387a55-7862-4956-b42d-7f70a3e3070a" testId="7356f68e-ad7b-97c4-f64c-2536928bb3c9" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_TinyDataPackets_ShouldHandleCorrectly(dataSize: 0)" computerName="JD-ITA028088-PC" duration="00:00:00.0023766" startTime="2025-06-12T13:37:26.9481480+08:00" endTime="2025-06-12T13:37:26.9481481+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="13387a55-7862-4956-b42d-7f70a3e3070a" />
    <UnitTestResult executionId="2190249f-61f9-43c7-8229-862b3d3f10ed" testId="520ee81f-3aa8-ca94-4ff6-979e424be6fe" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(timeoutMs: 5000)" computerName="JD-ITA028088-PC" duration="00:00:00.0076971" startTime="2025-06-12T13:37:27.4497187+08:00" endTime="2025-06-12T13:37:27.4497188+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2190249f-61f9-43c7-8229-862b3d3f10ed" />
    <UnitTestResult executionId="c3d7760c-5ef0-4733-a20a-33a29fbcd7ba" testId="f9ae6db7-303a-9c26-b311-d84f4bd95208" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SendRateLimit_ExtremeValues_ShouldHandleCorrectly(rateLimit: 1000000)" computerName="JD-ITA028088-PC" duration="00:00:00.0003014" startTime="2025-06-12T13:37:26.8219239+08:00" endTime="2025-06-12T13:37:26.8219240+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c3d7760c-5ef0-4733-a20a-33a29fbcd7ba" />
    <UnitTestResult executionId="7972e2bd-d48e-4c9f-9f6c-c5de9c71038c" testId="ae04b4e0-cf08-4697-35c4-f48e7c5131c5" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;你好&quot;, expectedText: &quot;你好&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0000215" startTime="2025-06-12T13:37:26.8039988+08:00" endTime="2025-06-12T13:37:26.8039989+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7972e2bd-d48e-4c9f-9f6c-c5de9c71038c" />
    <UnitTestResult executionId="5854acf2-d9fb-4703-9042-1a95150375ad" testId="f4fbaad3-6781-c8aa-cfe1-bc48e5a2111b" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Dispose_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0003401" startTime="2025-06-12T13:37:26.8333758+08:00" endTime="2025-06-12T13:37:26.8333759+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5854acf2-d9fb-4703-9042-1a95150375ad" />
    <UnitTestResult executionId="80549065-30c1-43b4-afdd-7fec28755de1" testId="2cc2a28b-0583-c385-8580-90889ea0fa8d" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0002907" startTime="2025-06-12T13:37:26.7972623+08:00" endTime="2025-06-12T13:37:26.7972624+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="80549065-30c1-43b4-afdd-7fec28755de1" />
    <UnitTestResult executionId="c1719d34-f6c3-4b6a-b257-1b65f3776050" testId="0ba56fbe-43b1-2a63-5322-5cc9943a2bc2" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.RecordSend_WithValidData_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0010802" startTime="2025-06-12T13:37:26.8098968+08:00" endTime="2025-06-12T13:37:26.8098969+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c1719d34-f6c3-4b6a-b257-1b65f3776050" />
    <UnitTestResult executionId="7f878894-a4c3-4469-a4ea-f7eecdcb8d9c" testId="011894a4-ce24-7e54-f1d5-7f0ac286b44b" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.QueueUsagePercentage_ShouldCalculateCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0003497" startTime="2025-06-12T13:37:28.2375768+08:00" endTime="2025-06-12T13:37:28.2375769+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7f878894-a4c3-4469-a4ea-f7eecdcb8d9c" />
    <UnitTestResult executionId="6f360331-cdfd-482b-ba5a-62ba6e039e16" testId="bc61f3c1-ffcd-b602-c10b-5bb52b1c6806" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.SerialPortConnectionException_ShouldInheritFromSerialPortException" computerName="JD-ITA028088-PC" duration="00:00:00.0013463" startTime="2025-06-12T13:37:26.7775960+08:00" endTime="2025-06-12T13:37:26.7775961+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6f360331-cdfd-482b-ba5a-62ba6e039e16" />
    <UnitTestResult executionId="1368d0de-880c-462d-aec9-d4589f4badc6" testId="11e2ecbb-54ae-ecc5-771b-56b899b2be70" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 2147483647)" computerName="JD-ITA028088-PC" duration="00:00:00.0018415" startTime="2025-06-12T13:37:26.9433774+08:00" endTime="2025-06-12T13:37:26.9433775+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1368d0de-880c-462d-aec9-d4589f4badc6" />
    <UnitTestResult executionId="5372f721-3499-45a4-922c-9934aa9b3fdd" testId="ceb4348a-7f39-5209-f0c5-47a92f0ccf52" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithMultipleItems_ShouldReturnCorrectCount" computerName="JD-ITA028088-PC" duration="00:00:00.0004118" startTime="2025-06-12T13:37:26.8180778+08:00" endTime="2025-06-12T13:37:26.8180778+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5372f721-3499-45a4-922c-9934aa9b3fdd" />
    <UnitTestResult executionId="a3dd9230-e594-4498-ae26-98c1386c2864" testId="c199ae11-7eed-7c3a-2d66-da1a2b9b2453" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_ErrorHandling_ShouldHandleErrorsGracefully" computerName="JD-ITA028088-PC" duration="00:00:00.0039937" startTime="2025-06-12T13:37:26.9239984+08:00" endTime="2025-06-12T13:37:26.9239985+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a3dd9230-e594-4498-ae26-98c1386c2864" />
    <UnitTestResult executionId="f7ded333-2442-4535-beeb-5cad7a5115ca" testId="f5c5b75e-4e7c-c2d4-8d83-637641048e9e" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.GetStatistics_UnderHighLoad_ShouldProvideAccurateData" computerName="JD-ITA028088-PC" duration="00:00:00.0633764" startTime="2025-06-12T13:37:26.8077870+08:00" endTime="2025-06-12T13:37:26.8077871+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f7ded333-2442-4535-beeb-5cad7a5115ca" />
    <UnitTestResult executionId="c42b7672-37ec-427b-ac4d-d5b8df3653fc" testId="de18f2eb-3df5-540f-0a3a-47950916d770" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0007080" startTime="2025-06-12T13:37:26.7845608+08:00" endTime="2025-06-12T13:37:26.7845608+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c42b7672-37ec-427b-ac4d-d5b8df3653fc" />
    <UnitTestResult executionId="3ba032f4-7cd8-4267-8949-78e9f17ed13d" testId="fcf1ca95-bb0c-f451-5094-8b054adedf81" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithValidParameters_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001399" startTime="2025-06-12T13:37:26.8287373+08:00" endTime="2025-06-12T13:37:26.8287373+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3ba032f4-7cd8-4267-8949-78e9f17ed13d" />
    <UnitTestResult executionId="e70a19d8-619b-4956-be98-7ecf0ffa030c" testId="e39f649e-7c27-616e-c761-ed717edf3ede" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.RecordSend_WithNegativeLength_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0006516" startTime="2025-06-12T13:37:27.5187870+08:00" endTime="2025-06-12T13:37:27.5187871+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e70a19d8-619b-4956-be98-7ecf0ffa030c" />
    <UnitTestResult executionId="66367763-fca9-4335-87b2-39b184603e3b" testId="bac682fb-bc9a-5ee1-6f8f-e6c1b404c020" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.GetStatistics_ShouldReturnValidStatistics" computerName="JD-ITA028088-PC" duration="00:00:00.0006290" startTime="2025-06-12T13:37:26.8246994+08:00" endTime="2025-06-12T13:37:26.8246995+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="66367763-fca9-4335-87b2-39b184603e3b" />
    <UnitTestResult executionId="7534e18f-7bd0-4595-b4bd-6c43c719f5f3" testId="5772937f-dba3-ddd7-fc6d-303fd2ade221" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: null, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000048" startTime="2025-06-12T13:37:26.7950278+08:00" endTime="2025-06-12T13:37:26.7950279+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7534e18f-7bd0-4595-b4bd-6c43c719f5f3" />
    <UnitTestResult executionId="a2b19a6c-ea07-4deb-b9fc-c07d3b4e05b1" testId="2bba4be0-2bc3-6003-e0d8-d93e1c6880cb" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001625" startTime="2025-06-12T13:37:26.7684315+08:00" endTime="2025-06-12T13:37:26.7684316+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a2b19a6c-ea07-4deb-b9fc-c07d3b4e05b1" />
    <UnitTestResult executionId="ddac0606-4e68-405e-952e-c65730577bc2" testId="d99254fa-0691-194c-a6ad-0465dd755790" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 9600)" computerName="JD-ITA028088-PC" duration="00:00:00.0013349" startTime="2025-06-12T13:37:26.9383134+08:00" endTime="2025-06-12T13:37:26.9383137+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ddac0606-4e68-405e-952e-c65730577bc2" />
    <UnitTestResult executionId="155fb51c-0595-4c5c-985e-6091fa30cf5b" testId="b3f503b4-beb7-1162-dc16-c72eb6d05b08" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Enable_ShouldSetIsEnabledToTrue" computerName="JD-ITA028088-PC" duration="00:00:00.0002957" startTime="2025-06-12T13:37:26.8203536+08:00" endTime="2025-06-12T13:37:26.8203537+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="155fb51c-0595-4c5c-985e-6091fa30cf5b" />
    <UnitTestResult executionId="14b55c07-b5e0-4b82-8ac8-a5e9450b52a8" testId="4e851d5d-05a7-2eac-92f0-8bfc94b85dc6" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(dataSize: 4096)" computerName="JD-ITA028088-PC" duration="00:00:00.0757865" startTime="2025-06-12T13:37:26.8298067+08:00" endTime="2025-06-12T13:37:26.8298067+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="14b55c07-b5e0-4b82-8ac8-a5e9450b52a8" />
    <UnitTestResult executionId="a068ddfc-afe3-497f-9aa9-b40d4397376b" testId="0d5b3495-7932-d5af-331e-630ce28c78dd" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001122" startTime="2025-06-12T13:37:26.7913489+08:00" endTime="2025-06-12T13:37:26.7913490+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a068ddfc-afe3-497f-9aa9-b40d4397376b" />
    <UnitTestResult executionId="7297732d-ec6e-4446-99f0-e2507458bca6" testId="4797f9c7-926d-5ab1-d952-801179405c81" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_InvalidConfiguration_ShouldThrowException(portName: &quot;INVALID&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0030913" startTime="2025-06-12T13:37:27.4387720+08:00" endTime="2025-06-12T13:37:27.4387721+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7297732d-ec6e-4446-99f0-e2507458bca6" />
    <UnitTestResult executionId="3cbcaf9a-2d6e-40b3-af4b-a3f923a3d14f" testId="19c767b9-00f8-d409-00da-eeea99ad6661" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SendRateLimit_ExtremeValues_ShouldHandleCorrectly(rateLimit: 1)" computerName="JD-ITA028088-PC" duration="00:00:00.0000417" startTime="2025-06-12T13:37:26.8226393+08:00" endTime="2025-06-12T13:37:26.8226394+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3cbcaf9a-2d6e-40b3-af4b-a3f923a3d14f" />
    <UnitTestResult executionId="0784ceb0-4f14-44f8-9686-4d33786a3bfe" testId="2b64ca9d-2218-a1bd-b07d-cbcc8dde3d16" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(itemCount: 8, shouldTriggerWarning: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001020" startTime="2025-06-12T13:37:26.8235186+08:00" endTime="2025-06-12T13:37:26.8235187+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0784ceb0-4f14-44f8-9686-4d33786a3bfe" />
    <UnitTestResult executionId="d227e4f4-4801-47da-9f9a-0b4dd2bf1279" testId="8e0d6f2f-fb0e-4668-6fa9-8f21c6f9155a" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0004573" startTime="2025-06-12T13:37:26.7801652+08:00" endTime="2025-06-12T13:37:26.7801653+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d227e4f4-4801-47da-9f9a-0b4dd2bf1279" />
    <UnitTestResult executionId="d48d28f7-37ab-43b5-b403-b91626f4741c" testId="7498a7db-5111-4360-9445-2e4763e8cd4e" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001418" startTime="2025-06-12T13:37:26.7888877+08:00" endTime="2025-06-12T13:37:26.7888877+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d48d28f7-37ab-43b5-b403-b91626f4741c" />
    <UnitTestResult executionId="c504d023-5665-4657-a2a0-fb0a18367165" testId="ff8dcbb8-c854-51a3-cf7a-a1ca3b1fcc29" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithNullServices_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0004899" startTime="2025-06-12T13:37:26.8006799+08:00" endTime="2025-06-12T13:37:26.8006799+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c504d023-5665-4657-a2a0-fb0a18367165" />
    <UnitTestResult executionId="dabed08d-e088-4c12-bbbb-ca553d4c7410" testId="08d3fc81-b996-b7a6-e1cd-78cabc053881" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0121914" startTime="2025-06-12T13:37:26.7499812+08:00" endTime="2025-06-12T13:37:26.7499813+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="dabed08d-e088-4c12-bbbb-ca553d4c7410" />
    <UnitTestResult executionId="8ad9afca-579f-4402-b6c1-a1ec4723369d" testId="e7971ba4-b797-4f77-bdc8-05995b20ff35" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_LargeDataPackets_ShouldHandleCorrectly(dataSize: 262144)" computerName="JD-ITA028088-PC" duration="00:00:00.0015525" startTime="2025-06-12T13:37:26.9172086+08:00" endTime="2025-06-12T13:37:26.9172089+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8ad9afca-579f-4402-b6c1-a1ec4723369d" />
    <UnitTestResult executionId="c860e5f7-6339-4558-9b46-0a7151ad7395" testId="330fed7e-e885-a7aa-44dc-e8f169ebddaf" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 100)" computerName="JD-ITA028088-PC" duration="00:00:00.0029950" startTime="2025-06-12T13:37:26.9421055+08:00" endTime="2025-06-12T13:37:26.9421057+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c860e5f7-6339-4558-9b46-0a7151ad7395" />
    <UnitTestResult executionId="736e385b-4e59-4ffd-8b97-5e3efdc40e9e" testId="40f0c48a-0bc6-7691-1bcb-bf225bd03b25" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 3000, maxAttempts: 5, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000041" startTime="2025-06-12T13:37:26.7907408+08:00" endTime="2025-06-12T13:37:26.7907409+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="736e385b-4e59-4ffd-8b97-5e3efdc40e9e" />
    <UnitTestResult executionId="b8582972-e44a-48ba-b231-0e72b92dbff1" testId="68fa167d-968d-2375-d00a-3a724af909d8" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;:&quot;, uppercase: False, expectedHex: &quot;48:65:6c&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0001901" startTime="2025-06-12T13:37:26.8053303+08:00" endTime="2025-06-12T13:37:26.8053304+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b8582972-e44a-48ba-b231-0e72b92dbff1" />
    <UnitTestResult executionId="2b5cf430-0de1-402e-9c92-901549345197" testId="86618079-abf9-30c9-2492-2a4b2efcc170" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithNullText_ShouldInitializeWithEmptyData" computerName="JD-ITA028088-PC" duration="00:00:00.0003904" startTime="2025-06-12T13:37:26.8079821+08:00" endTime="2025-06-12T13:37:26.8079821+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2b5cf430-0de1-402e-9c92-901549345197" />
    <UnitTestResult executionId="27bd1231-10cf-43f4-ab29-3a21b6bda3d0" testId="dca00415-9a9b-5e9a-5d7c-36ae82b195dc" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.GlobalReconnectOptions_ShouldHaveCorrectDefaults" computerName="JD-ITA028088-PC" duration="00:00:00.0006912" startTime="2025-06-12T13:37:26.7986285+08:00" endTime="2025-06-12T13:37:26.7986285+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="27bd1231-10cf-43f4-ab29-3a21b6bda3d0" />
    <UnitTestResult executionId="4c98645f-8b70-473c-98f4-39dee8cd28ef" testId="7c790003-2d63-4b93-840e-28312682c36d" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;&quot;, expectedText: &quot;&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0004429" startTime="2025-06-12T13:37:26.8036139+08:00" endTime="2025-06-12T13:37:26.8036139+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4c98645f-8b70-473c-98f4-39dee8cd28ef" />
    <UnitTestResult executionId="e4ac4c14-e5dd-4b7a-be43-46f0809256d3" testId="b1ab4e5a-1abe-d4fc-3bce-7b8f40d0cefc" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.SerialPortDataException_ShouldInheritFromSerialPortException" computerName="JD-ITA028088-PC" duration="00:00:00.0012160" startTime="2025-06-12T13:37:26.7768857+08:00" endTime="2025-06-12T13:37:26.7768858+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e4ac4c14-e5dd-4b7a-be43-46f0809256d3" />
    <UnitTestResult executionId="49bcc3fb-cd36-4c15-8695-aa0dc6c5d995" testId="53690872-c918-73be-c68f-532030de60cd" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.SerialPortException_ShouldInheritFromException" computerName="JD-ITA028088-PC" duration="00:00:00.0003491" startTime="2025-06-12T13:37:26.7840965+08:00" endTime="2025-06-12T13:37:26.7840966+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="49bcc3fb-cd36-4c15-8695-aa0dc6c5d995" />
    <UnitTestResult executionId="873656a9-bd98-4f4b-ab2c-5994ddf50e2d" testId="de7a5bdb-03df-5bef-5375-0d9de7fb33cd" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48 65 6C 6C 6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" computerName="JD-ITA028088-PC" duration="00:00:00.0003278" startTime="2025-06-12T13:37:26.8026376+08:00" endTime="2025-06-12T13:37:26.8026376+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="873656a9-bd98-4f4b-ab2c-5994ddf50e2d" />
    <UnitTestResult executionId="e36db05d-10fa-4840-892e-e55f80910478" testId="2f29d2d9-c8a3-7a41-59a4-b408ec3f6dd9" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortManager_BasicOperations_ShouldWorkCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0094288" startTime="2025-06-12T13:37:26.9196889+08:00" endTime="2025-06-12T13:37:26.9196890+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e36db05d-10fa-4840-892e-e55f80910478" />
    <UnitTestResult executionId="aeb13019-0cdc-4ed2-9622-d586cb42060c" testId="3f1e1745-b426-f5fa-fc4d-7c36c8ed8987" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 4096, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000087" startTime="2025-06-12T13:37:26.7739548+08:00" endTime="2025-06-12T13:37:26.7739549+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="aeb13019-0cdc-4ed2-9622-d586cb42060c" />
    <UnitTestResult executionId="799da879-90ae-45bf-9711-17e323b7c423" testId="5bb814e9-4a25-51d9-60b7-6112270973bf" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.RemoveSerialPort_WithExistingPort_ShouldRemoveAndReturnTrue" computerName="JD-ITA028088-PC" duration="00:00:00.0003541" startTime="2025-06-12T13:37:26.8325746+08:00" endTime="2025-06-12T13:37:26.8325747+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="799da879-90ae-45bf-9711-17e323b7c423" />
    <UnitTestResult executionId="d04f8c47-04c8-4aa9-a706-1d6644f6c4ef" testId="5168b9bd-ef9a-5e12-e4e4-606e2d42d0c0" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_Default_ShouldInitializeWithEmptyData" computerName="JD-ITA028088-PC" duration="00:00:00.0003530" startTime="2025-06-12T13:37:26.8075833+08:00" endTime="2025-06-12T13:37:26.8075834+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d04f8c47-04c8-4aa9-a706-1d6644f6c4ef" />
    <UnitTestResult executionId="ca5adf9b-35bc-4e26-8c73-ed85297b8686" testId="eddea9f3-c4cb-08bd-7c5f-ec33540ba04f" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithLargeDataVolume_ShouldHandleMemoryPressure" computerName="JD-ITA028088-PC" duration="00:00:00.0006059" startTime="2025-06-12T13:37:28.6370106+08:00" endTime="2025-06-12T13:37:28.6370107+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ca5adf9b-35bc-4e26-8c73-ed85297b8686" />
    <UnitTestResult executionId="9ef69fd3-9585-4110-adfb-f912cc5a5921" testId="3d8895f0-2b0a-046b-ed9b-3a2696b478f8" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_ShouldRegisterRequiredServices" computerName="JD-ITA028088-PC" duration="00:00:00.0019447" startTime="2025-06-12T13:37:26.7980618+08:00" endTime="2025-06-12T13:37:26.7980619+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9ef69fd3-9585-4110-adfb-f912cc5a5921" />
    <UnitTestResult executionId="b8b83d23-c75a-4709-967d-d19ccb5a8bad" testId="62c90d57-0073-79e7-453d-174a3cf7a9c9" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.CreateSerialPort_WithValidConfiguration_ShouldCreateAndAddPort" computerName="JD-ITA028088-PC" duration="00:00:00.0003210" startTime="2025-06-12T13:37:26.8352415+08:00" endTime="2025-06-12T13:37:26.8352415+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b8b83d23-c75a-4709-967d-d19ccb5a8bad" />
    <UnitTestResult executionId="34f1ab25-38d9-41e7-9620-b85ab3a6d875" testId="74f91c1b-75f8-0352-e93b-da086f3f05ba" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(sequence: [17, 17, 19])" computerName="JD-ITA028088-PC" duration="00:00:00.0003345" startTime="2025-06-12T13:37:26.8187963+08:00" endTime="2025-06-12T13:37:26.8187963+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="34f1ab25-38d9-41e7-9620-b85ab3a6d875" />
    <UnitTestResult executionId="43344386-3121-441f-b384-a432f4251d52" testId="0110b712-a0ab-bd1c-e53f-1f5be5b0e0fa" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(dataSize: 10000)" computerName="JD-ITA028088-PC" duration="00:00:00.0003066" startTime="2025-06-12T13:37:26.8082835+08:00" endTime="2025-06-12T13:37:26.8082835+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="43344386-3121-441f-b384-a432f4251d52" />
    <UnitTestResult executionId="ab8e16c8-9c1e-4697-a216-f82ca14d7102" testId="03e7ae61-8ff6-c0f3-9702-96401e863ad1" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0009108" startTime="2025-06-12T13:37:26.7836256+08:00" endTime="2025-06-12T13:37:26.7836256+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ab8e16c8-9c1e-4697-a216-f82ca14d7102" />
    <UnitTestResult executionId="3d4166e6-e5a9-4a4d-b79c-fa665d4f868c" testId="3da8ccb5-36b2-5b31-1537-436bec0c05cc" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(dataSize: 1000)" computerName="JD-ITA028088-PC" duration="00:00:00.0000660" startTime="2025-06-12T13:37:26.8084749+08:00" endTime="2025-06-12T13:37:26.8084750+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3d4166e6-e5a9-4a4d-b79c-fa665d4f868c" />
    <UnitTestResult executionId="9960f9c4-a31b-43fa-ab87-770539b32eb8" testId="c4268a3e-dbd0-ce2a-3f14-43313d1251f3" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0003087" startTime="2025-06-12T13:37:26.7911020+08:00" endTime="2025-06-12T13:37:26.7911021+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9960f9c4-a31b-43fa-ab87-770539b32eb8" />
    <UnitTestResult executionId="b136ea5e-505e-44c7-a589-cef28ad03033" testId="d864081d-48a6-ba7d-caf4-2925abe987a3" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.GetBytesToWrite_WhenNotConnected_ShouldReturnZero" computerName="JD-ITA028088-PC" duration="00:00:00.0002554" startTime="2025-06-12T13:37:26.8311306+08:00" endTime="2025-06-12T13:37:26.8311306+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b136ea5e-505e-44c7-a589-cef28ad03033" />
    <UnitTestResult executionId="d37bbae8-6552-4777-901f-3f75541afd79" testId="4f700146-33f1-ec69-f82a-0bacec577d1e" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.BufferManager_LongRunningOperations_ShouldNotLeakMemory" computerName="JD-ITA028088-PC" duration="00:00:00.3149447" startTime="2025-06-12T13:37:28.6351540+08:00" endTime="2025-06-12T13:37:28.6351544+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d37bbae8-6552-4777-901f-3f75541afd79" />
    <UnitTestResult executionId="030b426f-a420-4545-bead-7090d2015f56" testId="10ecc65a-1d98-44c2-5793-97f0d54a583d" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: -1, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0003178" startTime="2025-06-12T13:37:26.7799676+08:00" endTime="2025-06-12T13:37:26.7799679+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="030b426f-a420-4545-bead-7090d2015f56" />
    <UnitTestResult executionId="57cd573f-663b-4b92-9d10-00fee997223c" testId="ad521c37-2834-6634-6bda-bfb366ff0094" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_Default_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001287" startTime="2025-06-12T13:37:26.7922792+08:00" endTime="2025-06-12T13:37:26.7922792+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="57cd573f-663b-4b92-9d10-00fee997223c" />
    <UnitTestResult executionId="b3585c23-e8af-4d34-8d59-00b546c6f44e" testId="8043c734-3566-ddb7-1d4a-07a44a746bbb" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;UTF-32&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.1233707" startTime="2025-06-12T13:37:26.9091051+08:00" endTime="2025-06-12T13:37:26.9091052+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b3585c23-e8af-4d34-8d59-00b546c6f44e" />
    <UnitTestResult executionId="b824207b-c8aa-44f5-8ce8-ee3c58951e84" testId="3d3291e3-98dd-e9e2-de72-914857c1022d" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.AddSerialPort_WithValidService_ShouldAddToCollection" computerName="JD-ITA028088-PC" duration="00:00:00.0100300" startTime="2025-06-12T13:37:26.8314748+08:00" endTime="2025-06-12T13:37:26.8314748+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b824207b-c8aa-44f5-8ce8-ee3c58951e84" />
    <UnitTestResult executionId="0da91cc3-9f5e-4fbf-8806-dd1eb018cb30" testId="d915a464-2e21-829b-f426-6772ddf55055" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ClearQueue_WithDataInQueue_ShouldEmptyQueue" computerName="JD-ITA028088-PC" duration="00:00:00.0001917" startTime="2025-06-12T13:37:28.3200356+08:00" endTime="2025-06-12T13:37:28.3200357+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0da91cc3-9f5e-4fbf-8806-dd1eb018cb30" />
    <UnitTestResult executionId="f9c0ea0c-6bff-4513-b974-c9da4c6dc3e5" testId="2e50dc24-0e76-792e-ccef-ba135fad6d20" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetAllStatus_WithMultiplePorts_ShouldReturnAllStatuses" computerName="JD-ITA028088-PC" duration="00:00:00.0018586" startTime="2025-06-12T13:37:26.8350430+08:00" endTime="2025-06-12T13:37:26.8350431+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f9c0ea0c-6bff-4513-b974-c9da4c6dc3e5" />
    <UnitTestResult executionId="dcce725a-3baa-4638-bb8a-b3098ad570a1" testId="6ff352e2-9dcf-35a2-90fb-231d5f1f5bba" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Dispose_CalledMultipleTimes_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0004295" startTime="2025-06-12T13:37:26.8209490+08:00" endTime="2025-06-12T13:37:26.8209491+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="dcce725a-3baa-4638-bb8a-b3098ad570a1" />
    <UnitTestResult executionId="716a1c32-8db4-4260-82ea-8ab38c12f8eb" testId="b777246c-f407-ff52-0009-26e9cffd3bc2" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0087889" startTime="2025-06-12T13:37:26.7511968+08:00" endTime="2025-06-12T13:37:26.7511969+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="716a1c32-8db4-4260-82ea-8ab38c12f8eb" />
    <UnitTestResult executionId="8cc8eedb-3a41-436d-ada0-7e5b99b1282d" testId="2b3d1d5d-a9e9-c463-03be-c93fc127b2b3" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.SendTextAsync_WithNullText_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0011745" startTime="2025-06-12T13:37:26.8307222+08:00" endTime="2025-06-12T13:37:26.8307223+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8cc8eedb-3a41-436d-ada0-7e5b99b1282d" />
    <UnitTestResult executionId="28288568-0a3b-4662-ad9f-55ccc3f43bbf" testId="3154c378-cb14-2e6f-72c2-17b34c4e8da5" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_ConcurrentAccess_ShouldBeThreadSafe" computerName="JD-ITA028088-PC" duration="00:00:00.0026009" startTime="2025-06-12T13:37:27.6230839+08:00" endTime="2025-06-12T13:37:27.6230840+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="28288568-0a3b-4662-ad9f-55ccc3f43bbf" />
    <UnitTestResult executionId="191e4592-2408-44f4-9d69-01917ddfd779" testId="abc2d729-2b39-bae1-e7d9-85d1bd974a73" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;COM1&quot;, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000050" startTime="2025-06-12T13:37:26.7953589+08:00" endTime="2025-06-12T13:37:26.7953590+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="191e4592-2408-44f4-9d69-01917ddfd779" />
    <UnitTestResult executionId="f0e46f22-a920-46ea-b5a6-5d677757007e" testId="acb3181c-ee8d-5c46-618c-09ac65f019a4" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Constructor_WithNullLogger_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0011125" startTime="2025-06-12T13:37:26.8091462+08:00" endTime="2025-06-12T13:37:26.8091463+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f0e46f22-a920-46ea-b5a6-5d677757007e" />
    <UnitTestResult executionId="77e56f5f-e5e4-43d5-9e24-9933631ba42c" testId="bed1de91-05bf-0085-34af-7bee2cd33ea7" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithNullConfigureOptions_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0007205" startTime="2025-06-12T13:37:26.7999309+08:00" endTime="2025-06-12T13:37:26.7999309+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="77e56f5f-e5e4-43d5-9e24-9933631ba42c" />
    <UnitTestResult executionId="1238aa8c-1977-492b-ad07-cfc11de65f0a" testId="147438f0-3f17-1c20-cfc2-e5a56643d7cc" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SetSendRateLimit_WithValidRate_ShouldUpdateLimit" computerName="JD-ITA028088-PC" duration="00:00:00.0001253" startTime="2025-06-12T13:37:26.8285245+08:00" endTime="2025-06-12T13:37:26.8285246+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1238aa8c-1977-492b-ad07-cfc11de65f0a" />
    <UnitTestResult executionId="2424791d-7fc2-40eb-8e3c-50e14d114627" testId="cf6e81ed-2d36-4cb5-7d19-3207ec4a1e6b" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 0, maxAttempts: 0, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0002879" startTime="2025-06-12T13:37:26.7882070+08:00" endTime="2025-06-12T13:37:26.7882072+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2424791d-7fc2-40eb-8e3c-50e14d114627" />
    <UnitTestResult executionId="3b46205f-e603-4332-ad62-b4afe7d18f05" testId="ee4c97f9-aba3-7fd3-4eff-c0ae32af980a" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToString_ShouldReturnFormattedString" computerName="JD-ITA028088-PC" duration="00:00:00.0016962" startTime="2025-06-12T13:37:26.8041963+08:00" endTime="2025-06-12T13:37:26.8041963+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3b46205f-e603-4332-ad62-b4afe7d18f05" />
    <UnitTestResult executionId="ffc722f7-02db-4d6f-a165-1592793e9b6e" testId="6c69255c-6ff2-34a3-9fdf-65012918c8be" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0003207" startTime="2025-06-12T13:37:26.7956513+08:00" endTime="2025-06-12T13:37:26.7956514+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ffc722f7-02db-4d6f-a165-1592793e9b6e" />
    <UnitTestResult executionId="8db0f0d5-9287-4456-9d73-bae0ba0b58ec" testId="5fcd1008-efe6-9593-2351-b4703f8be1f5" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WhenQueueFull_ShouldTriggerOverflowEvent" computerName="JD-ITA028088-PC" duration="00:00:00.0002961" startTime="2025-06-12T13:37:28.6379551+08:00" endTime="2025-06-12T13:37:28.6379552+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8db0f0d5-9287-4456-9d73-bae0ba0b58ec" />
    <UnitTestResult executionId="e1b102d1-6696-4fe7-b104-bd7cc959aa03" testId="456b7034-8cd0-175d-c38d-784450059613" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 115200, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000022" startTime="2025-06-12T13:37:26.7833141+08:00" endTime="2025-06-12T13:37:26.7833141+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e1b102d1-6696-4fe7-b104-bd7cc959aa03" />
    <UnitTestResult executionId="0867df74-143c-4c90-a1c2-4c3324e0636f" testId="df64e4db-ecba-2113-fb43-2182e90a55d5" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.TryDequeueData_WithEmptyQueue_ShouldReturnFalse" computerName="JD-ITA028088-PC" duration="00:00:00.0003017" startTime="2025-06-12T13:37:28.6367301+08:00" endTime="2025-06-12T13:37:28.6367302+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0867df74-143c-4c90-a1c2-4c3324e0636f" />
    <UnitTestResult executionId="e95bea0a-7761-4023-bcba-9f899e950772" testId="c53401be-8da1-5d73-868a-6420974db0e4" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;-&quot;, uppercase: True, expectedHex: &quot;48-65-6C&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0002369" startTime="2025-06-12T13:37:26.8055237+08:00" endTime="2025-06-12T13:37:26.8055237+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e95bea0a-7761-4023-bcba-9f899e950772" />
    <UnitTestResult executionId="8d55e145-caff-46c8-8a42-e8ecb923763d" testId="05265970-e28e-f5df-e7d7-9126a86d207f" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.BroadcastAsync_WithNullData_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0014880" startTime="2025-06-12T13:37:26.8335687+08:00" endTime="2025-06-12T13:37:26.8335688+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8d55e145-caff-46c8-8a42-e8ecb923763d" />
    <UnitTestResult executionId="f4e2ec1d-146b-4313-a2ca-892abac4174f" testId="700480ab-f9a3-c14c-3862-c09cc3b0f4a7" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WhenNearCapacity_ShouldTriggerWarningEvent" computerName="JD-ITA028088-PC" duration="00:00:00.0004938" startTime="2025-06-12T13:37:28.2783154+08:00" endTime="2025-06-12T13:37:28.2783155+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f4e2ec1d-146b-4313-a2ca-892abac4174f" />
    <UnitTestResult executionId="78ae6ec0-9e35-4b27-a4db-82d5dfbcb57b" testId="3b43f857-4c69-124a-f1e5-4526c8f72ed1" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 0, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000052" startTime="2025-06-12T13:37:26.7743546+08:00" endTime="2025-06-12T13:37:26.7743547+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="78ae6ec0-9e35-4b27-a4db-82d5dfbcb57b" />
    <UnitTestResult executionId="91e6ccf5-1140-4e7c-894b-b9c84cfd0891" testId="fc0f03d5-65c5-740b-47d9-a78e15ea1d9c" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48656C6C6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" computerName="JD-ITA028088-PC" duration="00:00:00.0000798" startTime="2025-06-12T13:37:26.8030729+08:00" endTime="2025-06-12T13:37:26.8030730+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="91e6ccf5-1140-4e7c-894b-b9c84cfd0891" />
    <UnitTestResult executionId="a9d03deb-9d98-4156-a354-d875be8d54e3" testId="b8418e21-906c-bab7-f26a-6263d8be1dd9" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;中文测试&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0009826" startTime="2025-06-12T13:37:26.9227832+08:00" endTime="2025-06-12T13:37:26.9227833+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a9d03deb-9d98-4156-a354-d875be8d54e3" />
    <UnitTestResult executionId="39a3a842-2ae5-452b-986f-a88765692495" testId="0acc92da-f9c6-47d9-c5a2-760ab204265e" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 6, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000101" startTime="2025-06-12T13:37:26.7713670+08:00" endTime="2025-06-12T13:37:26.7713670+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="39a3a842-2ae5-452b-986f-a88765692495" />
    <UnitTestResult executionId="70dbb3de-0c7a-4e87-8fc4-0f755732891b" testId="bfdab111-1736-9094-751d-b681895868a8" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;   &quot;, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000948" startTime="2025-06-12T13:37:26.7925771+08:00" endTime="2025-06-12T13:37:26.7925772+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="70dbb3de-0c7a-4e87-8fc4-0f755732891b" />
    <UnitTestResult executionId="fafde010-82f0-4604-9b00-c71b6ed5fe56" testId="f7c3f6f4-f082-0f10-2964-f125578d9325" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.CreateSerialPort_WithDuplicatePortName_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0065514" startTime="2025-06-12T13:37:26.8278498+08:00" endTime="2025-06-12T13:37:26.8278498+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fafde010-82f0-4604-9b00-c71b6ed5fe56" />
    <UnitTestResult executionId="844f9286-8d43-4a91-8827-d48cab7a8489" testId="edf58ff9-246f-e14c-f25f-39068185472a" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithOptions_ShouldRegisterServicesAndOptions" computerName="JD-ITA028088-PC" duration="00:00:00.0353020" startTime="2025-06-12T13:37:26.7978439+08:00" endTime="2025-06-12T13:37:26.7978440+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="844f9286-8d43-4a91-8827-d48cab7a8489" />
    <UnitTestResult executionId="38022a40-325b-459c-bb70-f9ccf9778e59" testId="ef67f86c-3dd1-1171-22b1-c007547c1494" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Configure_WithNullConfiguration_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0003354" startTime="2025-06-12T13:37:26.8313248+08:00" endTime="2025-06-12T13:37:26.8313249+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="38022a40-325b-459c-bb70-f9ccf9778e59" />
    <UnitTestResult executionId="8eb868c9-ba9d-4c5d-a26a-804050d3fa09" testId="e835e9bc-7afc-0744-d083-42c92aa262b2" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0000797" startTime="2025-06-12T13:37:26.7928340+08:00" endTime="2025-06-12T13:37:26.7928340+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8eb868c9-ba9d-4c5d-a26a-804050d3fa09" />
    <UnitTestResult executionId="19ccb6b8-2320-4fe3-b6fe-c928ec33e51b" testId="d42f65dc-b483-693d-547e-03998b4ce142" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;COM2&quot;, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000073" startTime="2025-06-12T13:37:26.7946739+08:00" endTime="2025-06-12T13:37:26.7946740+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="19ccb6b8-2320-4fe3-b6fe-c928ec33e51b" />
    <UnitTestResult executionId="f0215e41-f689-4e46-a3ac-c22a9fa7348a" testId="a33546b2-5014-1109-76f2-708037dcb465" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithText_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0006602" startTime="2025-06-12T13:37:26.8064116+08:00" endTime="2025-06-12T13:37:26.8064117+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f0215e41-f689-4e46-a3ac-c22a9fa7348a" />
    <UnitTestResult executionId="eada3be6-70e1-49e6-8ffd-afa93fa3dabf" testId="7255bb30-60f6-a3b4-e259-af25a7d0a5b7" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.GetBytesToRead_WhenNotConnected_ShouldReturnZero" computerName="JD-ITA028088-PC" duration="00:00:00.0003486" startTime="2025-06-12T13:37:26.8255688+08:00" endTime="2025-06-12T13:37:26.8255689+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="eada3be6-70e1-49e6-8ffd-afa93fa3dabf" />
    <UnitTestResult executionId="a0a2a64f-7332-444b-b96f-10b13d570e6f" testId="c6821c0d-34ef-41c3-52bd-004793cc5855" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(dataSize: 100)" computerName="JD-ITA028088-PC" duration="00:00:00.1233643" startTime="2025-06-12T13:37:26.9088913+08:00" endTime="2025-06-12T13:37:26.9088915+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a0a2a64f-7332-444b-b96f-10b13d570e6f" />
    <UnitTestResult executionId="ce7da6a9-6149-49cd-9094-58f20440b3ae" testId="03bac210-5121-4552-6b4a-8ad9c8a4430a" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.Constructor_WithNullConfiguration_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0008009" startTime="2025-06-12T13:37:28.2371084+08:00" endTime="2025-06-12T13:37:28.2371086+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ce7da6a9-6149-49cd-9094-58f20440b3ae" />
    <UnitTestResult executionId="146cfeca-66b8-4615-a07d-249d3f27b770" testId="f3202fa7-6b71-5d20-4401-3d8bc9668755" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0006311" startTime="2025-06-12T13:37:26.7967283+08:00" endTime="2025-06-12T13:37:26.7967284+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="146cfeca-66b8-4615-a07d-249d3f27b770" />
    <UnitTestResult executionId="d9cd0d0c-bc42-4436-b2bf-858398f1a93a" testId="cac85d36-7607-899b-f9db-0914e3049caf" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetAvailablePorts_ShouldReturnPortArray" computerName="JD-ITA028088-PC" duration="00:00:00.0003430" startTime="2025-06-12T13:37:26.8318676+08:00" endTime="2025-06-12T13:37:26.8318677+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d9cd0d0c-bc42-4436-b2bf-858398f1a93a" />
    <UnitTestResult executionId="f31ff85c-6c26-446c-8491-2eaa92da6a9b" testId="b5d0270e-cd4b-dce1-3f1c-951f7852f9a7" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_BasicLifecycle_ShouldWorkCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0042776" startTime="2025-06-12T13:37:26.9403437+08:00" endTime="2025-06-12T13:37:26.9403439+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f31ff85c-6c26-446c-8491-2eaa92da6a9b" />
    <UnitTestResult executionId="dcb1813b-2cbe-4689-a2fc-5ceb6b226c66" testId="ad145a9e-531c-61f2-55a9-e48b910a979e" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Constructor_WithNullConfiguration_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0008519" startTime="2025-06-12T13:37:26.8224280+08:00" endTime="2025-06-12T13:37:26.8224282+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="dcb1813b-2cbe-4689-a2fc-5ceb6b226c66" />
    <UnitTestResult executionId="bdee86d2-36da-461b-b3a8-a792499b495c" testId="bfffdd04-e7b6-b0a1-e537-a650f2f9df27" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 9, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0002864" startTime="2025-06-12T13:37:26.7681025+08:00" endTime="2025-06-12T13:37:26.7681026+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="bdee86d2-36da-461b-b3a8-a792499b495c" />
    <UnitTestResult executionId="a8d9e12b-811a-4eb0-a54b-dbd75e6edc4b" testId="ccb6e43b-ed87-41fb-aacf-8532bac21ec1" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(strategy: DropNewest)" computerName="JD-ITA028088-PC" duration="00:00:00.0003870" startTime="2025-06-12T13:37:26.8103592+08:00" endTime="2025-06-12T13:37:26.8103594+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a8d9e12b-811a-4eb0-a54b-dbd75e6edc4b" />
    <UnitTestResult executionId="869d4b0f-b542-4c5f-9045-cd39ab4c1a87" testId="28df04bd-39fc-1cce-6c87-14cb33ebc5ed" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SetRtsFlowControl_ConcurrentOperations_ShouldBeThreadSafe" computerName="JD-ITA028088-PC" duration="00:00:00.0198389" startTime="2025-06-12T13:37:27.5392567+08:00" endTime="2025-06-12T13:37:27.5392568+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="869d4b0f-b542-4c5f-9045-cd39ab4c1a87" />
    <UnitTestResult executionId="6997fa49-c83b-4f3c-badf-134b929fb38e" testId="9bd2b2f8-4184-1ac4-6817-df59f52f3eae" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Dispose_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0003438" startTime="2025-06-12T13:37:26.8276547+08:00" endTime="2025-06-12T13:37:26.8276548+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6997fa49-c83b-4f3c-badf-134b929fb38e" />
    <UnitTestResult executionId="27ad2e10-18db-46a7-9284-47b5e2a549af" testId="81eb87c9-cb57-f039-ec3e-433c7ad89d3b" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetSerialPort_WithExistingPort_ShouldReturnService" computerName="JD-ITA028088-PC" duration="00:00:00.0057954" startTime="2025-06-12T13:37:26.8207541+08:00" endTime="2025-06-12T13:37:26.8207542+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="27ad2e10-18db-46a7-9284-47b5e2a549af" />
    <UnitTestResult executionId="b71e87c9-3be0-48bf-884d-4ecc6e597201" testId="e33e1140-ec5a-c360-ef7c-351a69dc550b" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_DeviceRemoved_ShouldDetectAndRecover" computerName="JD-ITA028088-PC" duration="00:00:00.1127481" startTime="2025-06-12T13:37:27.6203204+08:00" endTime="2025-06-12T13:37:27.6203207+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b71e87c9-3be0-48bf-884d-4ecc6e597201" />
    <UnitTestResult executionId="3f2bdda8-daf3-4a67-8b84-c9e43c6b13c3" testId="15548509-32f5-1f17-61af-b5d469801c2e" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Constructor_WithValidParameters_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001905" startTime="2025-06-12T13:37:26.8339445+08:00" endTime="2025-06-12T13:37:26.8339445+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3f2bdda8-daf3-4a67-8b84-c9e43c6b13c3" />
    <UnitTestResult executionId="19fcb5ab-1040-4277-9a68-f2178d0c5adc" testId="969900f1-f9c4-1336-3ebc-d975003306b5" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.CreateDefault_ShouldReturnValidConfiguration" computerName="JD-ITA028088-PC" duration="00:00:00.0005294" startTime="2025-06-12T13:37:26.7857954+08:00" endTime="2025-06-12T13:37:26.7857955+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="19fcb5ab-1040-4277-9a68-f2178d0c5adc" />
    <UnitTestResult executionId="50e91c56-4526-47d1-b04d-aeff40edbebf" testId="eec8e6e9-5b71-12c7-7998-04c7b1e88c20" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001528" startTime="2025-06-12T13:37:26.7864306+08:00" endTime="2025-06-12T13:37:26.7864306+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="50e91c56-4526-47d1-b04d-aeff40edbebf" />
    <UnitTestResult executionId="86c379aa-92c3-482c-ba9b-4113d13c30dc" testId="f26d999c-5247-a0ac-b5ab-848e393d4022" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 300)" computerName="JD-ITA028088-PC" duration="00:00:00.0030670" startTime="2025-06-12T13:37:26.9317633+08:00" endTime="2025-06-12T13:37:26.9317634+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="86c379aa-92c3-482c-ba9b-4113d13c30dc" />
    <UnitTestResult executionId="66dc07dc-f62a-4d08-bbbd-d32329c29cc3" testId="d8f02cf7-433b-61ec-c449-566982f978ff" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithNullByteArray_ShouldInitializeWithEmptyData" computerName="JD-ITA028088-PC" duration="00:00:00.0002375" startTime="2025-06-12T13:37:26.8059071+08:00" endTime="2025-06-12T13:37:26.8059072+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="66dc07dc-f62a-4d08-bbbd-d32329c29cc3" />
    <UnitTestResult executionId="be204ca5-5cab-49fc-b5f3-1baee1cf962f" testId="2759dc48-2d09-a5a8-b7c3-335908e8d07b" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_LargeDataPackets_ShouldHandleCorrectly(dataSize: 131072)" computerName="JD-ITA028088-PC" duration="00:00:00.0029680" startTime="2025-06-12T13:37:26.9155434+08:00" endTime="2025-06-12T13:37:26.9155436+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="be204ca5-5cab-49fc-b5f3-1baee1cf962f" />
    <UnitTestResult executionId="bb501047-38cc-4bd0-adc2-198494ac977f" testId="4fb7a94f-b5ad-a202-33ad-c14f2afe94a5" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48-65-6C-6C-6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" computerName="JD-ITA028088-PC" duration="00:00:00.0000921" startTime="2025-06-12T13:37:26.8028504+08:00" endTime="2025-06-12T13:37:26.8028504+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="bb501047-38cc-4bd0-adc2-198494ac977f" />
    <UnitTestResult executionId="ffd1d515-da22-44d7-b116-ea5e145602e5" testId="40dc612e-fcad-d21a-6d8f-c2c13cc86897" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 921600)" computerName="JD-ITA028088-PC" duration="00:00:00.0010628" startTime="2025-06-12T13:37:26.9341578+08:00" endTime="2025-06-12T13:37:26.9341581+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ffd1d515-da22-44d7-b116-ea5e145602e5" />
    <UnitTestResult executionId="9c9235e5-eab6-4581-abc8-924cdfe89d79" testId="fc11fc4c-e912-c68a-17af-aad770dde13c" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithNullData_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0003564" startTime="2025-06-12T13:37:26.8237458+08:00" endTime="2025-06-12T13:37:26.8237458+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9c9235e5-eab6-4581-abc8-924cdfe89d79" />
    <UnitTestResult executionId="4fe6a727-0604-4700-8ae1-ee83ca7272e7" testId="48f760e6-1514-1162-1f47-005a4a4b8f9d" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 115200)" computerName="JD-ITA028088-PC" duration="00:00:00.0012354" startTime="2025-06-12T13:37:26.9329940+08:00" endTime="2025-06-12T13:37:26.9329942+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4fe6a727-0604-4700-8ae1-ee83ca7272e7" />
    <UnitTestResult executionId="a40b518c-756c-492e-8760-9d2e6b8f6a1c" testId="2b557e92-a648-4516-f450-d8ea7736c72f" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;UTF-8&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0013588" startTime="2025-06-12T13:37:26.9126250+08:00" endTime="2025-06-12T13:37:26.9126252+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a40b518c-756c-492e-8760-9d2e6b8f6a1c" />
    <UnitTestResult executionId="b46c3c57-b953-4b30-9e3b-5f136879c6cd" testId="8b38b793-1bda-2cf1-fc52-ca85a8e00e41" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WhenResolved_ShouldCreateValidInstance" computerName="JD-ITA028088-PC" duration="00:00:00.0020960" startTime="2025-06-12T13:37:26.8001925+08:00" endTime="2025-06-12T13:37:26.8001926+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b46c3c57-b953-4b30-9e3b-5f136879c6cd" />
    <UnitTestResult executionId="854495bb-1d00-426f-a63e-9566a23f564d" testId="2f010a59-e386-1c48-7a96-6eaf300b4a00" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.GetDataCopy_ShouldReturnIndependentCopy" computerName="JD-ITA028088-PC" duration="00:00:00.0018577" startTime="2025-06-12T13:37:26.8051075+08:00" endTime="2025-06-12T13:37:26.8051076+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="854495bb-1d00-426f-a63e-9566a23f564d" />
    <UnitTestResult executionId="20598506-185c-4dfb-8491-f22d64930d67" testId="3fede858-ef69-906c-1781-83551e8174e8" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Dispose_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0003528" startTime="2025-06-12T13:37:26.8289281+08:00" endTime="2025-06-12T13:37:26.8289281+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="20598506-185c-4dfb-8491-f22d64930d67" />
    <UnitTestResult executionId="112e482d-9e22-4afd-aceb-21fdab287505" testId="a81de412-4838-6623-61fa-67ddc3188238" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;Hello&quot;, expectedText: &quot;Hello&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0004967" startTime="2025-06-12T13:37:26.8038145+08:00" endTime="2025-06-12T13:37:26.8038145+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="112e482d-9e22-4afd-aceb-21fdab287505" />
    <UnitTestResult executionId="0ab58745-bff7-4175-a771-69249b3486f3" testId="fcb4374f-81fa-4153-7c7a-49168b68b53b" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;&quot;, uppercase: True, expectedHex: &quot;48656C&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0000132" startTime="2025-06-12T13:37:26.8057147+08:00" endTime="2025-06-12T13:37:26.8057147+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0ab58745-bff7-4175-a771-69249b3486f3" />
    <UnitTestResult executionId="beeb5424-4920-46e2-9298-c666abbcb191" testId="02b715c1-0002-be47-2b1c-d64152d8f12d" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_LargeDataPackets_ShouldHandleCorrectly(dataSize: 65536)" computerName="JD-ITA028088-PC" duration="00:00:00.0010328" startTime="2025-06-12T13:37:26.9183156+08:00" endTime="2025-06-12T13:37:26.9183158+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="beeb5424-4920-46e2-9298-c666abbcb191" />
    <UnitTestResult executionId="45a627ae-d748-483f-9744-f235b6058196" testId="ea26ec81-d8c7-5fae-e953-a047af719c17" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.BroadcastTextAsync_WithNullText_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0010920" startTime="2025-06-12T13:37:26.8341339+08:00" endTime="2025-06-12T13:37:26.8341340+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="45a627ae-d748-483f-9744-f235b6058196" />
    <UnitTestResult executionId="3e4cf76d-13c8-464d-a507-02378947bb40" testId="8c638f55-8f9a-3b1d-dff8-8169a30d369f" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 5, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0090025" startTime="2025-06-12T13:37:26.7419167+08:00" endTime="2025-06-12T13:37:26.7420058+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3e4cf76d-13c8-464d-a507-02378947bb40" />
    <UnitTestResult executionId="6578f54f-10cc-41ce-8b31-b4967ad30071" testId="b25ea050-dc5b-ed4b-30ec-a1476f14ecc6" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_RepeatedOperations_ShouldNotLeakMemory" computerName="JD-ITA028088-PC" duration="00:00:00.0578556" startTime="2025-06-12T13:37:27.5074124+08:00" endTime="2025-06-12T13:37:27.5074126+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6578f54f-10cc-41ce-8b31-b4967ad30071" />
    <UnitTestResult executionId="9df96433-583c-4b4b-a2ef-035c9562768a" testId="09ce9de4-34a5-edec-6b47-7870a0ac8152" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: -1, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001459" startTime="2025-06-12T13:37:26.7764985+08:00" endTime="2025-06-12T13:37:26.7764986+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9df96433-583c-4b4b-a2ef-035c9562768a" />
    <UnitTestResult executionId="0cec6c94-d8ad-4ff3-838c-4d78b5320243" testId="8caa8b5b-b84c-d1d7-e2b2-05bff9553bb9" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.GetStatistics_FrequentCalls_ShouldNotImpactPerformance" computerName="JD-ITA028088-PC" duration="00:00:00.0032543" startTime="2025-06-12T13:37:28.2777434+08:00" endTime="2025-06-12T13:37:28.2777435+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0cec6c94-d8ad-4ff3-838c-4d78b5320243" />
    <UnitTestResult executionId="34ffe3fd-7d92-4698-910f-ddfa65e65e88" testId="d3314a3a-784e-50f7-5f7e-7145efc09018" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithBufferOverflow_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0013200" startTime="2025-06-12T13:37:26.8257682+08:00" endTime="2025-06-12T13:37:26.8257682+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="34ffe3fd-7d92-4698-910f-ddfa65e65e88" />
    <UnitTestResult executionId="265895e2-2d09-432c-9174-6123040e99ab" testId="fa202986-794c-673e-db28-2db703cc0fc9" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0004646" startTime="2025-06-12T13:37:26.7867237+08:00" endTime="2025-06-12T13:37:26.7867238+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="265895e2-2d09-432c-9174-6123040e99ab" />
    <UnitTestResult executionId="1db23d47-09ac-4d1d-aefd-cf4656b9001b" testId="53a79683-6e18-bdcc-2942-0ae631602802" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 7, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000105" startTime="2025-06-12T13:37:26.7723891+08:00" endTime="2025-06-12T13:37:26.7723892+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1db23d47-09ac-4d1d-aefd-cf4656b9001b" />
    <UnitTestResult executionId="92c950d1-df38-406b-9bea-558f11eb118f" testId="4f9d435a-f0d2-5d93-e815-ec19ec4acd79" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ConcurrentBufferOperations_WithEventHandlers_ShouldBeThreadSafe" computerName="JD-ITA028088-PC" duration="00:00:00.0406840" startTime="2025-06-12T13:37:28.3193754+08:00" endTime="2025-06-12T13:37:28.3193756+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="92c950d1-df38-406b-9bea-558f11eb118f" />
    <UnitTestResult executionId="8638ed3a-1212-4d72-a349-986ccab9f8b7" testId="ca5223c1-4903-c112-ec50-72cd8a818e1b" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 4096)" computerName="JD-ITA028088-PC" duration="00:00:00.0017084" startTime="2025-06-12T13:37:26.9522122+08:00" endTime="2025-06-12T13:37:26.9522123+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8638ed3a-1212-4d72-a349-986ccab9f8b7" />
    <UnitTestResult executionId="ea664d28-6937-42f9-9d02-403bf4e51635" testId="a70006d6-2422-b7b2-466f-65fda27fd805" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.TryDequeueData_WithDataInQueue_ShouldReturnData" computerName="JD-ITA028088-PC" duration="00:00:00.0003730" startTime="2025-06-12T13:37:27.0306969+08:00" endTime="2025-06-12T13:37:27.0306970+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ea664d28-6937-42f9-9d02-403bf4e51635" />
    <UnitTestResult executionId="4894f2b0-9694-49b6-8266-9b0f6bddef57" testId="eb395603-826c-2aec-bdbc-d35ee9d4654e" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_NullAndEmptyParameters_ShouldHandleGracefully" computerName="JD-ITA028088-PC" duration="00:00:00.0047482" startTime="2025-06-12T13:37:27.1796153+08:00" endTime="2025-06-12T13:37:27.1796154+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4894f2b0-9694-49b6-8266-9b0f6bddef57" />
    <UnitTestResult executionId="a66f3d49-174b-4b2b-bce7-be130ec928ad" testId="7f56e2f6-e85d-2d67-fab2-f0a6cfe57e1f" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithEmptyData_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0003544" startTime="2025-06-12T13:37:26.8252875+08:00" endTime="2025-06-12T13:37:26.8252876+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a66f3d49-174b-4b2b-bce7-be130ec928ad" />
    <UnitTestResult executionId="5d977581-ac72-48ab-91a7-ee38458d07cd" testId="b8844c7c-ca09-4104-11ef-326b5bcd433f" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001299" startTime="2025-06-12T13:37:26.7942591+08:00" endTime="2025-06-12T13:37:26.7942592+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5d977581-ac72-48ab-91a7-ee38458d07cd" />
    <UnitTestResult executionId="07f6ed4f-fe14-4ef2-8376-89e0d57c4b22" testId="e95c7136-7996-a786-e59e-522991193040" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_ShouldRegisterServicesWithCorrectLifetime" computerName="JD-ITA028088-PC" duration="00:00:00.0009275" startTime="2025-06-12T13:37:26.8011418+08:00" endTime="2025-06-12T13:37:26.8011419+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="07f6ed4f-fe14-4ef2-8376-89e0d57c4b22" />
    <UnitTestResult executionId="581699ab-34f2-4c77-a523-d0150c846c31" testId="31b4aa6a-561a-ec27-c6d2-f1426e253b35" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithValidData_ShouldAddToQueue" computerName="JD-ITA028088-PC" duration="00:00:00.0004838" startTime="2025-06-12T13:37:26.8177308+08:00" endTime="2025-06-12T13:37:26.8177309+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="581699ab-34f2-4c77-a523-d0150c846c31" />
    <UnitTestResult executionId="44efd3a7-438a-4c7c-9ce2-7f7de7206f10" testId="902ef6cf-b62e-58ea-9022-da49556e41c5" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 1)" computerName="JD-ITA028088-PC" duration="00:00:00.0010912" startTime="2025-06-12T13:37:26.9456831+08:00" endTime="2025-06-12T13:37:26.9456833+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="44efd3a7-438a-4c7c-9ce2-7f7de7206f10" />
    <UnitTestResult executionId="f72ed453-828a-43b1-852f-b1bbc4c2815c" testId="4d4c87b9-bd77-56b0-acd7-7c0c49cbc08f" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_WithRateLimit_ShouldEnforceTimingAccurately" computerName="JD-ITA028088-PC" duration="00:00:00.6005545" startTime="2025-06-12T13:37:27.5180298+08:00" endTime="2025-06-12T13:37:27.5180300+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f72ed453-828a-43b1-852f-b1bbc4c2815c" />
    <UnitTestResult executionId="19e7e54f-5ed6-4d45-9133-1123b65625fb" testId="ac6b9c02-26c7-4e3d-e30f-fe45601d8bad" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(strategy: ThrowException)" computerName="JD-ITA028088-PC" duration="00:00:00.0017156" startTime="2025-06-12T13:37:26.8100968+08:00" endTime="2025-06-12T13:37:26.8100969+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="19e7e54f-5ed6-4d45-9133-1123b65625fb" />
    <UnitTestResult executionId="7c4295e4-6c19-4e83-b65f-ba243850d603" testId="db95ccd0-c126-7f48-a28b-a633c62602b2" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(timeoutMs: 100)" computerName="JD-ITA028088-PC" duration="00:00:00.0016637" startTime="2025-06-12T13:37:27.4405327+08:00" endTime="2025-06-12T13:37:27.4405328+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7c4295e4-6c19-4e83-b65f-ba243850d603" />
    <UnitTestResult executionId="bd5d58f9-3c1f-4071-9a0b-23f4143abee6" testId="a0d6040b-1605-8c04-788d-3eef62fbd2d3" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(requestCount: 1000)" computerName="JD-ITA028088-PC" duration="00:00:00.0002000" startTime="2025-06-12T13:37:26.8193848+08:00" endTime="2025-06-12T13:37:26.8193848+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="bd5d58f9-3c1f-4071-9a0b-23f4143abee6" />
    <UnitTestResult executionId="61fd99a4-a2cc-43de-85f1-4ed762beac7c" testId="e3460a9b-1799-f6b6-2f2c-a356970be8b9" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_WhenEnabled_ShouldReturnTrue" computerName="JD-ITA028088-PC" duration="00:00:00.0001441" startTime="2025-06-12T13:37:26.8291411+08:00" endTime="2025-06-12T13:37:26.8291411+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="61fd99a4-a2cc-43de-85f1-4ed762beac7c" />
    <UnitTestResult executionId="d7e25405-f868-4da0-a8e6-c678e0c3e92e" testId="2e5792b5-5830-a1a1-6907-f9e6eeb32c23" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.SendAsync_WithNullData_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0016762" startTime="2025-06-12T13:37:26.8274632+08:00" endTime="2025-06-12T13:37:26.8274632+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d7e25405-f868-4da0-a8e6-c678e0c3e92e" />
    <UnitTestResult executionId="f650b668-8906-49a5-8828-fe880bf328cc" testId="33597ab1-9d34-65c5-b0a1-f1a694190e93" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: -1, maxAttempts: 0, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000078" startTime="2025-06-12T13:37:26.7904871+08:00" endTime="2025-06-12T13:37:26.7904871+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f650b668-8906-49a5-8828-fe880bf328cc" />
    <UnitTestResult executionId="69933988-6dd8-45c6-b700-03788db86fb3" testId="54caa2f7-9288-7523-076d-6fecd3569986" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_ConfigurationUpdate_ShouldApplyCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0035881" startTime="2025-06-12T13:37:26.9441315+08:00" endTime="2025-06-12T13:37:26.9441316+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="69933988-6dd8-45c6-b700-03788db86fb3" />
    <UnitTestResult executionId="d2b68d6c-a74b-471b-ad55-73b4b9cdf457" testId="14c24c6f-1bd1-8a0b-0fdb-8dccb26c718e" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.GetStatistics_ShouldReturnCorrectInformation" computerName="JD-ITA028088-PC" duration="00:00:00.0007677" startTime="2025-06-12T13:37:28.6362987+08:00" endTime="2025-06-12T13:37:28.6362988+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d2b68d6c-a74b-471b-ad55-73b4b9cdf457" />
    <UnitTestResult executionId="878edbeb-000e-4a73-b4bb-50833218d20b" testId="92f29400-6caf-fff2-71d7-c6b032343516" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 65536)" computerName="JD-ITA028088-PC" duration="00:00:00.0040759" startTime="2025-06-12T13:37:26.9577791+08:00" endTime="2025-06-12T13:37:26.9577793+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="878edbeb-000e-4a73-b4bb-50833218d20b" />
    <UnitTestResult executionId="0345589f-e0a8-44f5-ae94-44d6ba955409" testId="43a8f9d1-3c2f-114d-d7c3-2fa82d3ccdfd" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.CreateSerialPort_WithNullConfiguration_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0004158" startTime="2025-06-12T13:37:26.8337546+08:00" endTime="2025-06-12T13:37:26.8337546+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0345589f-e0a8-44f5-ae94-44d6ba955409" />
    <UnitTestResult executionId="85b3e4a5-f106-440d-bb75-e2b80e8f461b" testId="befe512a-b39c-f7de-f215-29ab09232596" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0003565" startTime="2025-06-12T13:37:26.7884432+08:00" endTime="2025-06-12T13:37:26.7884432+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="85b3e4a5-f106-440d-bb75-e2b80e8f461b" />
    <UnitTestResult executionId="30ce4ab7-24f9-49cb-b5fd-f6705165ffea" testId="186e3f0a-894f-97e9-d204-455dc212b0c8" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Configure_WithValidConfiguration_ShouldUpdateConfiguration" computerName="JD-ITA028088-PC" duration="00:00:00.0003923" startTime="2025-06-12T13:37:26.8295388+08:00" endTime="2025-06-12T13:37:26.8295388+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="30ce4ab7-24f9-49cb-b5fd-f6705165ffea" />
    <UnitTestResult executionId="781191c0-d9ff-4074-986a-c60a4d64abed" testId="54e12c3f-6b43-5687-a842-a3d35e6a7a19" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\t&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0019206" startTime="2025-06-12T13:37:26.9248277+08:00" endTime="2025-06-12T13:37:26.9248279+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="781191c0-d9ff-4074-986a-c60a4d64abed" />
    <UnitTestResult executionId="33cf1a5a-ffd3-4f22-8996-348021aec466" testId="1ec54028-be89-592f-c08c-8fb797e62772" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: RtsCts)" computerName="JD-ITA028088-PC" duration="00:00:00.0009936" startTime="2025-06-12T13:37:26.8140889+08:00" endTime="2025-06-12T13:37:26.8140891+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="33cf1a5a-ffd3-4f22-8996-348021aec466" />
    <UnitTestResult executionId="824bb285-e496-41a9-a92d-e3176d8738e6" testId="259912bc-7987-fbbd-0222-56d9037315ca" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.BufferManagement_WhenAdvancedBufferingDisabled_ShouldReturnDefaults" computerName="JD-ITA028088-PC" duration="00:00:00.0014506" startTime="2025-06-12T13:37:26.8144723+08:00" endTime="2025-06-12T13:37:26.8144724+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="824bb285-e496-41a9-a92d-e3176d8738e6" />
    <UnitTestResult executionId="427c574c-68a5-4fa0-8ef9-a464b1e91c4c" testId="cb5b6170-9791-0b05-807b-fb298b58c5d8" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WhenDisabled_ShouldNotProcess" computerName="JD-ITA028088-PC" duration="00:00:00.0004632" startTime="2025-06-12T13:37:26.8272657+08:00" endTime="2025-06-12T13:37:26.8272657+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="427c574c-68a5-4fa0-8ef9-a464b1e91c4c" />
    <UnitTestResult executionId="adc937b7-b14a-4f2b-bccc-34d004ef6655" testId="78f63ed6-a82b-1dc7-8333-4f5a85338c27" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0001706" startTime="2025-06-12T13:37:26.7889761+08:00" endTime="2025-06-12T13:37:26.7889762+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="adc937b7-b14a-4f2b-bccc-34d004ef6655" />
    <UnitTestResult executionId="cf8eb3f2-89e2-4de1-9155-a05ee18abb15" testId="14978f86-a584-09a6-4ff2-659b440bf384" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_ConcurrentOperations_ShouldHandleParallelRequests" computerName="JD-ITA028088-PC" duration="00:00:00.0078268" startTime="2025-06-12T13:37:26.9320274+08:00" endTime="2025-06-12T13:37:26.9320274+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cf8eb3f2-89e2-4de1-9155-a05ee18abb15" />
    <UnitTestResult executionId="1ef20ea1-d8fd-4e2f-a91e-62157f39efb7" testId="1e4bdd15-076b-9af7-45c3-530c1fecf022" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.Constructor_ShouldInitializeWithDefaultValues" computerName="JD-ITA028088-PC" duration="00:00:00.0058376" startTime="2025-06-12T13:37:26.7974601+08:00" endTime="2025-06-12T13:37:26.7974601+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1ef20ea1-d8fd-4e2f-a91e-62157f39efb7" />
    <UnitTestResult executionId="360c5d24-39f1-4afe-8bba-f0b0dcf349c5" testId="06b280c7-f3e9-b8e7-377e-97f90aa891c1" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetSerialPort_WithNonExistentPort_ShouldReturnNull" computerName="JD-ITA028088-PC" duration="00:00:00.0001457" startTime="2025-06-12T13:37:26.8329842+08:00" endTime="2025-06-12T13:37:26.8329842+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="360c5d24-39f1-4afe-8bba-f0b0dcf349c5" />
    <UnitTestResult executionId="4cc16902-2296-41a3-9c83-9b2c5ebded0d" testId="c92fb4d1-98fd-ec97-499d-551eebba0847" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.ContainsPort_WithExistingPort_ShouldReturnTrue" computerName="JD-ITA028088-PC" duration="00:00:00.0003099" startTime="2025-06-12T13:37:26.8316781+08:00" endTime="2025-06-12T13:37:26.8316782+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4cc16902-2296-41a3-9c83-9b2c5ebded0d" />
    <UnitTestResult executionId="b8fad011-f80c-49de-b352-ea6d38e5626c" testId="be06675f-3c25-5bc8-de68-febd1ec325bb" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.RemoveSerialPort_WithNullOrEmptyPortName_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0630195" startTime="2025-06-12T13:37:26.8066743+08:00" endTime="2025-06-12T13:37:26.8066743+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b8fad011-f80c-49de-b352-ea6d38e5626c" />
    <UnitTestResult executionId="115c6a11-a88f-43d7-a07c-968f091b4c4e" testId="e0e718ff-4c78-ae1f-949d-1594d0430302" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Constructor_WithNullServiceProvider_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0004200" startTime="2025-06-12T13:37:26.8348357+08:00" endTime="2025-06-12T13:37:26.8348358+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="115c6a11-a88f-43d7-a07c-968f091b4c4e" />
    <UnitTestResult executionId="0eab09e4-0364-4a33-93dd-ed348c68475d" testId="4e85762c-ae91-2b9e-76be-257c688fb9fa" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtMaxCapacity_ShouldHandleCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0004240" startTime="2025-06-12T13:37:27.1324890+08:00" endTime="2025-06-12T13:37:27.1324891+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0eab09e4-0364-4a33-93dd-ed348c68475d" />
    <UnitTestResult executionId="047d9dca-fd24-4b85-b786-edbf3655ba5c" testId="f7c5e95f-be71-08d0-bb43-352b994d7d2c" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueDequeue_ConcurrentOperations_ShouldBeThreadSafe" computerName="JD-ITA028088-PC" duration="00:00:00.1011641" startTime="2025-06-12T13:37:27.1319833+08:00" endTime="2025-06-12T13:37:27.1319834+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="047d9dca-fd24-4b85-b786-edbf3655ba5c" />
    <UnitTestResult executionId="f5da3f38-4d4b-4e29-91f2-b4995da3f097" testId="b1444560-e97c-3484-c5b5-7415dcdaf44b" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: XonXoff)" computerName="JD-ITA028088-PC" duration="00:00:00.0000484" startTime="2025-06-12T13:37:26.8151541+08:00" endTime="2025-06-12T13:37:26.8151542+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f5da3f38-4d4b-4e29-91f2-b4995da3f097" />
    <UnitTestResult executionId="2139bd88-44e5-43bd-b55c-9f7a89cd520f" testId="35ae527b-65f4-e761-c5e9-42b376ef79c1" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_MixedWithRegularData_ShouldExtractControlCharsCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001570" startTime="2025-06-12T13:37:27.5194509+08:00" endTime="2025-06-12T13:37:27.5194509+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2139bd88-44e5-43bd-b55c-9f7a89cd520f" />
    <UnitTestResult executionId="0f171567-98f2-4f22-a163-23feff577da3" testId="ed1c132d-d00f-bdaf-6b94-61ad7a3557e9" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(dataSize: 100000)" computerName="JD-ITA028088-PC" duration="00:00:00.0637722" startTime="2025-06-12T13:37:26.8080660+08:00" endTime="2025-06-12T13:37:26.8080661+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0f171567-98f2-4f22-a163-23feff577da3" />
    <UnitTestResult executionId="edc708a9-3094-4b78-9cea-bb6e75eba8be" testId="5dbf9991-d128-405c-e119-2c5c8fb571d8" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithInvalidHex_ShouldThrowException(invalidHex: &quot;48 65 6C 6C 6&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0140201" startTime="2025-06-12T13:37:26.7676526+08:00" endTime="2025-06-12T13:37:26.7676527+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="edc708a9-3094-4b78-9cea-bb6e75eba8be" />
    <UnitTestResult executionId="7feea879-aeea-4b5d-a8e7-56eb1a35f073" testId="55695b9c-8fa7-75b9-c4de-aa77143fb3c0" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.RemoveSerialPort_WithNonExistentPort_ShouldReturnFalse" computerName="JD-ITA028088-PC" duration="00:00:00.0001539" startTime="2025-06-12T13:37:26.8354884+08:00" endTime="2025-06-12T13:37:26.8354885+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7feea879-aeea-4b5d-a8e7-56eb1a35f073" />
    <UnitTestResult executionId="79607f67-ba92-4860-bb7c-6443dff7da36" testId="66354075-61e8-8db6-93a7-d9e160dce397" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ConcurrentOperations_WithCancellation_ShouldHandleGracefully" computerName="JD-ITA028088-PC" duration="00:00:00.1013585" startTime="2025-06-12T13:37:27.2339418+08:00" endTime="2025-06-12T13:37:27.2339419+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="79607f67-ba92-4860-bb7c-6443dff7da36" />
    <UnitTestResult executionId="d8df03a1-86fc-4fbc-8e31-ccb120fd07e1" testId="89904fc7-f945-148c-427f-28dd272d907e" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithXoffData_ShouldPauseFlow" computerName="JD-ITA028088-PC" duration="00:00:00.0001579" startTime="2025-06-12T13:37:26.8161304+08:00" endTime="2025-06-12T13:37:26.8161304+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d8df03a1-86fc-4fbc-8e31-ccb120fd07e1" />
    <UnitTestResult executionId="4353ba2a-d0ab-4929-b927-828a7dbbb3ad" testId="35f2d5bf-cf12-732f-0809-1a84cafae57a" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;&quot;, expectedBytes: [])" computerName="JD-ITA028088-PC" duration="00:00:00.0002436" startTime="2025-06-12T13:37:26.8014801+08:00" endTime="2025-06-12T13:37:26.8014802+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4353ba2a-d0ab-4929-b927-828a7dbbb3ad" />
    <UnitTestResult executionId="76baeee0-569a-4ade-96c2-1e5a40f06f56" testId="2a46be50-f303-bb70-1536-236543d44d59" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_HighFrequencyCalls_ShouldMaintainPerformance" computerName="JD-ITA028088-PC" duration="00:00:00.0026646" startTime="2025-06-12T13:37:26.8266728+08:00" endTime="2025-06-12T13:37:26.8266729+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="76baeee0-569a-4ade-96c2-1e5a40f06f56" />
    <UnitTestResult executionId="d5005e0e-b80d-428a-8773-bc66b2fa41ed" testId="ac78eccc-7e0e-a33e-3154-a1d2a9e455e3" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.SerialPortConfigurationException_ShouldInheritFromSerialPortException" computerName="JD-ITA028088-PC" duration="00:00:00.0012073" startTime="2025-06-12T13:37:26.7771064+08:00" endTime="2025-06-12T13:37:26.7771065+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d5005e0e-b80d-428a-8773-bc66b2fa41ed" />
    <UnitTestResult executionId="260757b9-59ce-494a-908d-c1bc96bd42c7" testId="8d9dd9a9-c289-1130-7c61-b67125a5dbe3" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\ud83d\ude80\ud83d\udd25\ud83d\udcbb&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0017502" startTime="2025-06-12T13:37:26.9266924+08:00" endTime="2025-06-12T13:37:26.9266930+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="260757b9-59ce-494a-908d-c1bc96bd42c7" />
    <UnitTestResult executionId="89d58db3-bf60-4099-8a8c-ada2225ad5f6" testId="3a22456d-b134-6108-c30d-835c62639107" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithZeroLengthData_ShouldHandleCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0003257" startTime="2025-06-12T13:37:28.3197851+08:00" endTime="2025-06-12T13:37:28.3197852+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="89d58db3-bf60-4099-8a8c-ada2225ad5f6" />
    <UnitTestResult executionId="aa409082-b757-40d0-b6f7-e3588bc2670e" testId="db250e33-9c97-4756-436a-16d6e8cf414f" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: 0, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0001392" startTime="2025-06-12T13:37:26.7777724+08:00" endTime="2025-06-12T13:37:26.7777725+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="aa409082-b757-40d0-b6f7-e3588bc2670e" />
    <UnitTestResult executionId="f5dc9014-6480-4954-a528-8a47ed09d889" testId="65132280-9071-90ad-f1d3-b2bec385e31a" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(timeoutMs: 1000)" computerName="JD-ITA028088-PC" duration="00:00:00.0010475" startTime="2025-06-12T13:37:27.4416413+08:00" endTime="2025-06-12T13:37:27.4416414+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f5dc9014-6480-4954-a528-8a47ed09d889" />
    <UnitTestResult executionId="7f3d0102-fcd1-4cb0-81ba-4b680b9acd9c" testId="57a78de4-cb96-8d86-813e-ab82acf74480" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(itemCount: 9, shouldTriggerWarning: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0007002" startTime="2025-06-12T13:37:26.8211476+08:00" endTime="2025-06-12T13:37:26.8211476+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7f3d0102-fcd1-4cb0-81ba-4b680b9acd9c" />
    <UnitTestResult executionId="ad6ef08a-4f7a-4a28-b8c8-30b65cc7bbdc" testId="5a625eaf-ce33-5cc0-168a-ac8f3f113fa3" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001285" startTime="2025-06-12T13:37:26.7886836+08:00" endTime="2025-06-12T13:37:26.7886837+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ad6ef08a-4f7a-4a28-b8c8-30b65cc7bbdc" />
    <UnitTestResult executionId="45e27199-b61c-487d-ba1f-0c3d0fcf178f" testId="11d85114-8736-9f44-b948-699605b5a326" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 1)" computerName="JD-ITA028088-PC" duration="00:00:00.0008581" startTime="2025-06-12T13:37:26.9587300+08:00" endTime="2025-06-12T13:37:26.9587301+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="45e27199-b61c-487d-ba1f-0c3d0fcf178f" />
    <UnitTestResult executionId="c809a4fc-7fec-447c-8827-6505fb5a3d7a" testId="c46d3460-b4d6-d480-98b9-89cb5e5d84d1" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_Default_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0004438" startTime="2025-06-12T13:37:26.7861246+08:00" endTime="2025-06-12T13:37:26.7861247+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c809a4fc-7fec-447c-8827-6505fb5a3d7a" />
    <UnitTestResult executionId="90d9d8a7-0a13-4ac0-86de-f1c8e640f994" testId="0a76b3a1-8066-016c-7b64-ba09c55edde9" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithoutLogging_ShouldStillWork" computerName="JD-ITA028088-PC" duration="00:00:00.0003958" startTime="2025-06-12T13:37:26.8025001+08:00" endTime="2025-06-12T13:37:26.8025002+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="90d9d8a7-0a13-4ac0-86de-f1c8e640f994" />
    <UnitTestResult executionId="53082dbc-74e1-4cc2-a677-f7950464b2bc" testId="cffe2f0d-7498-e2dd-fb74-c52cfbb78304" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: Both)" computerName="JD-ITA028088-PC" duration="00:00:00.0000327" startTime="2025-06-12T13:37:26.8154827+08:00" endTime="2025-06-12T13:37:26.8154828+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="53082dbc-74e1-4cc2-a677-f7950464b2bc" />
    <UnitTestResult executionId="41233f07-5353-4f56-978c-37305331dfad" testId="1fc3a25a-3320-5e0f-9e3e-b9761c8f4549" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithNullOrWhiteSpace_ShouldReturnEmptyData" computerName="JD-ITA028088-PC" duration="00:00:00.0010269" startTime="2025-06-12T13:37:26.8032873+08:00" endTime="2025-06-12T13:37:26.8032874+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="41233f07-5353-4f56-978c-37305331dfad" />
    <UnitTestResult executionId="235382cc-a47b-445e-b6ba-c5b14ef0796c" testId="5cd769ba-e210-24e0-902c-6f839ac6a5e1" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.BufferManager_HighLoadOperations_ShouldMaintainReasonableCpuUsage" computerName="JD-ITA028088-PC" duration="00:00:01.0007313" startTime="2025-06-12T13:37:28.2349292+08:00" endTime="2025-06-12T13:37:28.2349294+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="235382cc-a47b-445e-b6ba-c5b14ef0796c" />
    <UnitTestResult executionId="fc9c58be-78a9-44e2-954f-f9b5108baaa1" testId="fc3b21c3-0f8b-a760-763b-ba136c1f3355" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0001097" startTime="2025-06-12T13:37:26.7809509+08:00" endTime="2025-06-12T13:37:26.7809510+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fc9c58be-78a9-44e2-954f-f9b5108baaa1" />
    <UnitTestResult executionId="87c1a5e9-9d0a-4475-a7bb-de94005a590d" testId="1110dce6-cdc2-edbd-83e1-76acc885583d" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.Constructor_WithValidConfiguration_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0002908" startTime="2025-06-12T13:37:26.8183971+08:00" endTime="2025-06-12T13:37:26.8183971+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="87c1a5e9-9d0a-4475-a7bb-de94005a590d" />
    <UnitTestResult executionId="65c3ad0d-2574-4d74-8901-8c6d080792c6" testId="c34da281-7d78-d48e-fd36-b17e7d8c79e7" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [], expectedHex: &quot;&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0002028" startTime="2025-06-12T13:37:26.7934667+08:00" endTime="2025-06-12T13:37:26.7934668+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="65c3ad0d-2574-4d74-8901-8c6d080792c6" />
    <UnitTestResult executionId="6dfe7602-8ba3-409c-aef2-41f6f765fb07" testId="4a656b7f-67e3-8f8d-6a98-8264a3231c8f" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(sequence: [17, 19, 17])" computerName="JD-ITA028088-PC" duration="00:00:00.0014393" startTime="2025-06-12T13:37:26.8185968+08:00" endTime="2025-06-12T13:37:26.8185969+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6dfe7602-8ba3-409c-aef2-41f6f765fb07" />
    <UnitTestResult executionId="d4b4cd30-dd68-46d4-990c-51172ccb5955" testId="f9be5f74-0f42-c1ca-06ce-4ff54de6dea3" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_WhenDisabled_ShouldReturnTrue" computerName="JD-ITA028088-PC" duration="00:00:00.0001387" startTime="2025-06-12T13:37:26.8268747+08:00" endTime="2025-06-12T13:37:26.8268748+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d4b4cd30-dd68-46d4-990c-51172ccb5955" />
    <UnitTestResult executionId="af705834-4ea4-4cb3-a893-09ba5416b371" testId="9c726bc3-a469-3d93-f1bf-fce1b2658615" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ConcurrentStatusChanges_ShouldBeThreadSafe" computerName="JD-ITA028088-PC" duration="00:00:00.1336387" startTime="2025-06-12T13:37:26.9175759+08:00" endTime="2025-06-12T13:37:26.9175760+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="af705834-4ea4-4cb3-a893-09ba5416b371" />
    <UnitTestResult executionId="bc4fe66c-e692-4924-b447-01bba7076734" testId="ed73d661-31f4-564a-3bca-e7bd1da12bc7" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [72, 101, 108, 108, 111], expectedHex: &quot;48 65 6C 6C 6F&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0000394" startTime="2025-06-12T13:37:26.7947953+08:00" endTime="2025-06-12T13:37:26.7947954+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="bc4fe66c-e692-4924-b447-01bba7076734" />
    <UnitTestResult executionId="fee3bd86-4716-4cc1-a83d-00af277a730a" testId="94919b74-043c-ac5d-87f1-0587b8f46ef7" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.AddSerialPort_WithNullService_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0003912" startTime="2025-06-12T13:37:26.8327902+08:00" endTime="2025-06-12T13:37:26.8327903+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fee3bd86-4716-4cc1-a83d-00af277a730a" />
    <UnitTestResult executionId="97609364-6292-4b3b-a74e-f8a037cdb599" testId="f0dd822e-110a-9423-89f0-2a6bc0510f84" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 1024, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0003886" startTime="2025-06-12T13:37:26.7731582+08:00" endTime="2025-06-12T13:37:26.7731582+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="97609364-6292-4b3b-a74e-f8a037cdb599" />
    <UnitTestResult executionId="9e7e2dad-9c0d-4f34-a021-d8aa4a0b7d0e" testId="f543d2e9-4ced-9d03-4307-68823ad67ec5" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithByteArray_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0010384" startTime="2025-06-12T13:37:26.8061166+08:00" endTime="2025-06-12T13:37:26.8061167+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9e7e2dad-9c0d-4f34-a021-d8aa4a0b7d0e" />
    <UnitTestResult executionId="9c5b6496-637b-4108-9012-ce25baaa7be4" testId="97ad4d87-64e8-3d1f-2000-33d34d4e461d" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_RapidConnectDisconnect_ShouldHandleStably" computerName="JD-ITA028088-PC" duration="00:00:00.2158186" startTime="2025-06-12T13:37:27.1746757+08:00" endTime="2025-06-12T13:37:27.1746758+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9c5b6496-637b-4108-9012-ce25baaa7be4" />
    <UnitTestResult executionId="00d1e193-5c71-472f-823b-b604e66296de" testId="65c4cae6-02b9-cdd9-6a8b-d3eb8e0242f1" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Constructor_WithValidConfiguration_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0021894" startTime="2025-06-12T13:37:26.8096866+08:00" endTime="2025-06-12T13:37:26.8096867+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="00d1e193-5c71-472f-823b-b604e66296de" />
    <UnitTestResult executionId="7f884700-60c8-4cf8-95bb-622a7a8c645d" testId="29cc443b-5843-75d0-329d-fdd89478ba0f" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithNullData_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0005625" startTime="2025-06-12T13:37:28.6376154+08:00" endTime="2025-06-12T13:37:28.6376163+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7f884700-60c8-4cf8-95bb-622a7a8c645d" />
    <UnitTestResult executionId="f2ce9005-8196-4d7c-8ca3-f474a179af06" testId="d095da48-d2a1-b869-306b-68ec74fb6a86" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 8, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000133" startTime="2025-06-12T13:37:26.7686449+08:00" endTime="2025-06-12T13:37:26.7686450+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f2ce9005-8196-4d7c-8ca3-f474a179af06" />
    <UnitTestResult executionId="de433dcd-76fa-456e-bd56-57452700faa5" testId="a094d57c-350b-c7d9-7ef4-ab145f0776e7" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 30000)" computerName="JD-ITA028088-PC" duration="00:00:00.0010297" startTime="2025-06-12T13:37:26.9444878+08:00" endTime="2025-06-12T13:37:26.9444879+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="de433dcd-76fa-456e-bd56-57452700faa5" />
    <UnitTestResult executionId="d0aff8ce-c47d-4fff-8fe7-c8508af4f8e0" testId="8927fc38-5352-5a92-ac17-c0b53db402c5" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;&quot;, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001347" startTime="2025-06-12T13:37:26.7944271+08:00" endTime="2025-06-12T13:37:26.7944272+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d0aff8ce-c47d-4fff-8fe7-c8508af4f8e0" />
    <UnitTestResult executionId="9461cb61-7b4e-4252-8b9c-7f228984a49d" testId="54219804-c561-b035-0838-e2a41e644606" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [255, 0, 171], expectedHex: &quot;FF 00 AB&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0027044" startTime="2025-06-12T13:37:26.7901648+08:00" endTime="2025-06-12T13:37:26.7901649+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9461cb61-7b4e-4252-8b9c-7f228984a49d" />
    <UnitTestResult executionId="f3db0982-ac00-4527-997c-3218340b3a96" testId="f4bd691f-6e1a-eb6a-8014-5eb6cc5c57a1" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithRtsCtsType_ShouldNotProcessXonXoff" computerName="JD-ITA028088-PC" duration="00:00:00.0002015" startTime="2025-06-12T13:37:26.8270715+08:00" endTime="2025-06-12T13:37:26.8270715+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f3db0982-ac00-4527-997c-3218340b3a96" />
    <UnitTestResult executionId="8ad02b69-2acb-4bb9-9d4b-029400de496e" testId="c4b6fa98-2242-c1ba-efc6-ec174c1be4e2" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\0&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0021017" startTime="2025-06-12T13:37:26.9205383+08:00" endTime="2025-06-12T13:37:26.9205385+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8ad02b69-2acb-4bb9-9d4b-029400de496e" />
    <UnitTestResult executionId="75c8fa63-4855-4020-9cb0-61e1208b5e71" testId="c8b6c16e-78a6-4e53-1410-df1171a01492" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(requestCount: -1)" computerName="JD-ITA028088-PC" duration="00:00:00.0000487" startTime="2025-06-12T13:37:26.8201360+08:00" endTime="2025-06-12T13:37:26.8201361+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="75c8fa63-4855-4020-9cb0-61e1208b5e71" />
    <UnitTestResult executionId="69fd9dc6-fe48-41cc-bdf1-ba14ddbb3ab4" testId="26c01f72-2f55-6301-3660-bbcccfc220bf" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(itemCount: 7, shouldTriggerWarning: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0004355" startTime="2025-06-12T13:37:26.8231222+08:00" endTime="2025-06-12T13:37:26.8231222+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="69fd9dc6-fe48-41cc-bdf1-ba14ddbb3ab4" />
    <UnitTestResult executionId="7d4c6c9f-117c-4303-890a-e2a50da69a57" testId="d205cea9-26a0-cb1a-deb8-bd301be91a7a" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(requestCount: 0)" computerName="JD-ITA028088-PC" duration="00:00:00.0004438" startTime="2025-06-12T13:37:26.8189861+08:00" endTime="2025-06-12T13:37:26.8189861+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7d4c6c9f-117c-4303-890a-e2a50da69a57" />
    <UnitTestResult executionId="633947d2-46e3-4688-b6a7-73eddaae7a63" testId="d554c12b-5ed2-23ff-fe49-9271ab3c4030" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0120370" startTime="2025-06-12T13:37:26.7508068+08:00" endTime="2025-06-12T13:37:26.7508069+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="633947d2-46e3-4688-b6a7-73eddaae7a63" />
    <UnitTestResult executionId="d969b723-3ac6-4818-84e4-82c7a2c98252" testId="d0e94aff-761a-b752-8ea1-b05e263e6baa" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithMoreRequestedThanAvailable_ShouldReturnAllAvailable" computerName="JD-ITA028088-PC" duration="00:00:00.0002145" startTime="2025-06-12T13:37:28.2785848+08:00" endTime="2025-06-12T13:37:28.2785849+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d969b723-3ac6-4818-84e4-82c7a2c98252" />
    <UnitTestResult executionId="abf3202c-0ac5-4350-96ed-fb34e2a9d4fc" testId="d72a782f-3fee-0055-ced6-0e57470253f9" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ClearDataQueue_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0004659" startTime="2025-06-12T13:37:26.8158115+08:00" endTime="2025-06-12T13:37:26.8158115+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="abf3202c-0ac5-4350-96ed-fb34e2a9d4fc" />
    <UnitTestResult executionId="f7bb6d1f-b688-4638-a823-7a43f067dfef" testId="90a5f1aa-f90c-1c7c-8cfb-a59b5defb741" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(dataSize: 1024)" computerName="JD-ITA028088-PC" duration="00:00:00.0011285" startTime="2025-06-12T13:37:26.9120052+08:00" endTime="2025-06-12T13:37:26.9120053+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f7bb6d1f-b688-4638-a823-7a43f067dfef" />
    <UnitTestResult executionId="19cc99e3-1ae7-4bd9-b6e8-e4bfaef0151d" testId="374deec9-2627-2caa-434b-b1767297fddb" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Disable_ShouldSetIsEnabledToFalse" computerName="JD-ITA028088-PC" duration="00:00:00.0011859" startTime="2025-06-12T13:37:26.8094377+08:00" endTime="2025-06-12T13:37:26.8094377+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="19cc99e3-1ae7-4bd9-b6e8-e4bfaef0151d" />
    <UnitTestResult executionId="bdc4ed34-3a31-4608-9948-b56c9848e631" testId="2660b25c-0ab3-7226-1818-3de3d93f7e8b" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: -1, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001867" startTime="2025-06-12T13:37:26.7735795+08:00" endTime="2025-06-12T13:37:26.7735796+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="bdc4ed34-3a31-4608-9948-b56c9848e631" />
    <UnitTestResult executionId="c8fe6d4e-00f1-4538-b4a9-9f88f4a05583" testId="e5f24540-0924-e65b-6f16-4ab793a71012" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.SerialPortServiceOptions_ShouldHaveCorrectDefaults" computerName="JD-ITA028088-PC" duration="00:00:00.0003696" startTime="2025-06-12T13:37:26.8004412+08:00" endTime="2025-06-12T13:37:26.8004413+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c8fe6d4e-00f1-4538-b4a9-9f88f4a05583" />
    <UnitTestResult executionId="ab609544-db7d-4ac8-8d0a-1bea29de6800" testId="cf59dfeb-fc64-c423-a638-29288238b6c4" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithXonAfterXoff_ShouldResumeFlow" computerName="JD-ITA028088-PC" duration="00:00:00.0002561" startTime="2025-06-12T13:37:26.8248989+08:00" endTime="2025-06-12T13:37:26.8248989+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ab609544-db7d-4ac8-8d0a-1bea29de6800" />
    <UnitTestResult executionId="2e9652e0-70c1-4385-891b-ea03cf69e362" testId="bdd00514-c674-7fbe-6276-f883dc5e0af9" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ConcurrentOperations_ResourceContention_ShouldNotDeadlock" computerName="JD-ITA028088-PC" duration="00:00:00.2524849" startTime="2025-06-12T13:37:27.0302450+08:00" endTime="2025-06-12T13:37:27.0302452+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2e9652e0-70c1-4385-891b-ea03cf69e362" />
    <UnitTestResult executionId="564651da-3d9f-44b9-8a42-61622853b193" testId="4910faa5-5592-c319-7394-2de698b2c0c7" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0000916" startTime="2025-06-12T13:37:26.7929723+08:00" endTime="2025-06-12T13:37:26.7929724+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="564651da-3d9f-44b9-8a42-61622853b193" />
    <UnitTestResult executionId="cafda20a-444f-4357-8268-2dc3ada236ab" testId="5bacd699-c1c3-a30f-c688-788c48f5fa12" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_AdvancedBuffering_ShouldConfigureCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0040028" startTime="2025-06-12T13:37:26.9358714+08:00" endTime="2025-06-12T13:37:26.9358715+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cafda20a-444f-4357-8268-2dc3ada236ab" />
    <UnitTestResult executionId="07468682-eaaa-4e99-bb81-b57d43ab62b8" testId="4593fd1a-0307-349e-dd30-e0a7cd2c3a3b" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_ConnectionLost_ShouldHandleGracefully" computerName="JD-ITA028088-PC" duration="00:00:00.2023695" startTime="2025-06-12T13:37:26.9094470+08:00" endTime="2025-06-12T13:37:26.9094470+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="07468682-eaaa-4e99-bb81-b57d43ab62b8" />
    <UnitTestResult executionId="4745fcd7-09a8-41eb-93da-33fb819d36d7" testId="37b241c9-a28c-3630-63d5-22833c81cd74" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.CreateDefault_WithEmptyPortName_ShouldReturnInvalidConfiguration" computerName="JD-ITA028088-PC" duration="00:00:00.0001651" startTime="2025-06-12T13:37:26.7976993+08:00" endTime="2025-06-12T13:37:26.7976993+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4745fcd7-09a8-41eb-93da-33fb819d36d7" />
    <UnitTestResult executionId="04fc1006-3b5f-4835-9687-a8fe256b8f29" testId="66753aa0-12f4-9a94-0af2-e1d64e8dae4a" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_HighFrequencyOperations_ShouldMaintainPerformance" computerName="JD-ITA028088-PC" duration="00:00:00.0380739" startTime="2025-06-12T13:37:28.2744346+08:00" endTime="2025-06-12T13:37:28.2744347+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="04fc1006-3b5f-4835-9687-a8fe256b8f29" />
    <UnitTestResult executionId="69ca79bf-cb37-41a1-a1fc-25cd47290b3e" testId="5de9e3f2-f5fe-88a8-b3fb-d72657ad04d9" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\&quot;'\\&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0015397" startTime="2025-06-12T13:37:26.9284327+08:00" endTime="2025-06-12T13:37:26.9284329+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="69ca79bf-cb37-41a1-a1fc-25cd47290b3e" />
    <UnitTestResult executionId="c60870e9-6add-488a-aa11-e5cbd33e4614" testId="4ba3dac7-6a62-cb49-022a-20a7d4d37087" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\r\n&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0010956" startTime="2025-06-12T13:37:26.9217175+08:00" endTime="2025-06-12T13:37:26.9217176+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c60870e9-6add-488a-aa11-e5cbd33e4614" />
    <UnitTestResult executionId="ad925acd-d884-45a7-b573-b51f3f925dd3" testId="586a2248-3398-cdf2-2aaf-e284666f9fc1" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0088398" startTime="2025-06-12T13:37:26.7510437+08:00" endTime="2025-06-12T13:37:26.7510438+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ad925acd-d884-45a7-b573-b51f3f925dd3" />
    <UnitTestResult executionId="c47bf3f4-5bc7-48b3-b091-5763827bd1a6" testId="f758c623-141e-3413-11d2-f683cfe5a47c" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithInvalidCount_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0010424" startTime="2025-06-12T13:37:26.8309371+08:00" endTime="2025-06-12T13:37:26.8309372+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c47bf3f4-5bc7-48b3-b091-5763827bd1a6" />
    <UnitTestResult executionId="c8f1629c-da0d-402f-8559-6ac0661e286d" testId="387aec6f-b8f3-0719-a44f-14efd8e3f6b0" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_CalledMultipleTimes_ShouldNotDuplicateServices" computerName="JD-ITA028088-PC" duration="00:00:00.0006430" startTime="2025-06-12T13:37:26.7996643+08:00" endTime="2025-06-12T13:37:26.7996644+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c8f1629c-da0d-402f-8559-6ac0661e286d" />
    <UnitTestResult executionId="2b018ead-2d18-4cde-aee1-f2497f0113ed" testId="80209ebf-9964-6643-698c-8cc7c39137a7" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 512)" computerName="JD-ITA028088-PC" duration="00:00:00.0012809" startTime="2025-06-12T13:37:26.9562693+08:00" endTime="2025-06-12T13:37:26.9562695+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2b018ead-2d18-4cde-aee1-f2497f0113ed" />
    <UnitTestResult executionId="8e5b263e-1864-4058-996d-f986d4c253f9" testId="1a863009-7253-b831-45ba-4599991a1e23" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;ASCII&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0757702" startTime="2025-06-12T13:37:26.8297257+08:00" endTime="2025-06-12T13:37:26.8297258+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8e5b263e-1864-4058-996d-f986d4c253f9" />
    <UnitTestResult executionId="6dd4e648-2966-41af-a3a3-db37cde3f8f5" testId="d796aa36-66a9-9e1e-199e-e7f8579e7312" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 0, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000079" startTime="2025-06-12T13:37:26.7829670+08:00" endTime="2025-06-12T13:37:26.7829671+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6dd4e648-2966-41af-a3a3-db37cde3f8f5" />
    <UnitTestResult executionId="5ef0d7be-d0e5-44b0-8050-63b4542daed9" testId="c18a85de-caa6-f92b-dd42-c491eddd8d5a" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_BufferOverflow_ShouldHandleGracefully" computerName="JD-ITA028088-PC" duration="00:00:00.5222125" startTime="2025-06-12T13:37:27.4323321+08:00" endTime="2025-06-12T13:37:27.4323322+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5ef0d7be-d0e5-44b0-8050-63b4542daed9" />
    <UnitTestResult executionId="117930f2-8516-4ca5-8460-3c76c470a3a3" testId="cd2d846a-4253-c669-9172-ae3f3e9c95ea" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(sequence: [19, 19, 17])" computerName="JD-ITA028088-PC" duration="00:00:00.0000499" startTime="2025-06-12T13:37:26.8191638+08:00" endTime="2025-06-12T13:37:26.8191639+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="117930f2-8516-4ca5-8460-3c76c470a3a3" />
    <UnitTestResult executionId="f01c165e-a7d9-4565-9fc0-075062972101" testId="44298843-f215-2386-e136-f2a25ffedf0e" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: 1000, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000073" startTime="2025-06-12T13:37:26.7782468+08:00" endTime="2025-06-12T13:37:26.7782469+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f01c165e-a7d9-4565-9fc0-075062972101" />
    <UnitTestResult executionId="ecd4c796-ce68-409f-b9f7-9ee437ba0e94" testId="b531b8c7-1b6c-8c36-b996-64028f4f0324" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 1200)" computerName="JD-ITA028088-PC" duration="00:00:00.0026139" startTime="2025-06-12T13:37:26.9369758+08:00" endTime="2025-06-12T13:37:26.9369761+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ecd4c796-ce68-409f-b9f7-9ee437ba0e94" />
    <UnitTestResult executionId="db67cd9a-2c8f-4377-a242-0f82dc416dd7" testId="746694ab-c99f-c4df-9347-d40ac323bdff" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithInvalidOffset_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0629899" startTime="2025-06-12T13:37:26.8067721+08:00" endTime="2025-06-12T13:37:26.8067722+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="db67cd9a-2c8f-4377-a242-0f82dc416dd7" />
    <UnitTestResult executionId="91121044-df15-4780-b551-52488a255331" testId="d030f382-42f0-6302-7045-2cfd01c44112" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SetSendRateLimit_WithNegativeRate_ShouldSetToZero" computerName="JD-ITA028088-PC" duration="00:00:00.0002966" startTime="2025-06-12T13:37:27.5191463+08:00" endTime="2025-06-12T13:37:27.5191464+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="91121044-df15-4780-b551-52488a255331" />
    <UnitTestResult executionId="224ed723-bbab-4960-91f9-b496d43d0b11" testId="a2862178-237f-01ca-e3a5-f47ceb7b9484" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ClearReceiveBuffer_WhenNotConnected_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0010529" startTime="2025-06-12T13:37:26.8250930+08:00" endTime="2025-06-12T13:37:26.8250931+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="224ed723-bbab-4960-91f9-b496d43d0b11" />
    <UnitTestResult executionId="beea40c7-8e6e-4901-bbc6-51908b27b734" testId="c828ecfa-33bf-6434-f923-b9cfd77032bc" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_InvalidConfiguration_ShouldThrowException(portName: &quot;COM999&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0018263" startTime="2025-06-12T13:37:27.4342633+08:00" endTime="2025-06-12T13:37:27.4342635+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="beea40c7-8e6e-4901-bbc6-51908b27b734" />
    <UnitTestResult executionId="3708c74d-0813-4d22-ab37-7e7151a009ea" testId="c4f03241-cdba-e70d-96b7-1a09d3cf8bfb" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SendRateLimit_ExtremeValues_ShouldHandleCorrectly(rateLimit: 2147483647)" computerName="JD-ITA028088-PC" duration="00:00:00.0002172" startTime="2025-06-12T13:37:26.8221325+08:00" endTime="2025-06-12T13:37:26.8221325+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3708c74d-0813-4d22-ab37-7e7151a009ea" />
    <UnitTestResult executionId="9060b876-36ec-4786-90f1-3e41f5e29b13" testId="69868bb1-a9df-9b47-1d6a-43a9bb280b16" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.AddSerialPort_WithDuplicatePortName_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0012819" startTime="2025-06-12T13:37:26.8331734+08:00" endTime="2025-06-12T13:37:26.8331735+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9060b876-36ec-4786-90f1-3e41f5e29b13" />
    <UnitTestResult executionId="51b6e7cb-f0e4-44e3-a338-66017e7d0033" testId="9510bd6c-2b16-38c6-12a7-a2ae238d0e20" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_ShouldReturnServiceCollection" computerName="JD-ITA028088-PC" duration="00:00:00.0004803" startTime="2025-06-12T13:37:26.7983295+08:00" endTime="2025-06-12T13:37:26.7983296+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="51b6e7cb-f0e4-44e3-a338-66017e7d0033" />
    <UnitTestResult executionId="5a95d898-f888-487f-827f-0e0f78395c21" testId="06f82529-1ce7-16b1-604c-9f0392f7d160" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_InvalidConfiguration_ShouldThrowException(portName: &quot;&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0012538" startTime="2025-06-12T13:37:27.4355764+08:00" endTime="2025-06-12T13:37:27.4355765+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5a95d898-f888-487f-827f-0e0f78395c21" />
    <UnitTestResult executionId="e11f5824-6b84-44d0-a5f0-4ab5316d014b" testId="57f89734-de5a-0489-86e3-f364df300ca2" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_Default_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001377" startTime="2025-06-12T13:37:26.7970128+08:00" endTime="2025-06-12T13:37:26.7970129+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e11f5824-6b84-44d0-a5f0-4ab5316d014b" />
    <UnitTestResult executionId="5ad729f5-378c-4426-bee7-2df899f6b4ae" testId="99219d16-4665-2168-0835-d11459e2febf" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;UTF-16&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0020387" startTime="2025-06-12T13:37:26.9132293+08:00" endTime="2025-06-12T13:37:26.9132295+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5ad729f5-378c-4426-bee7-2df899f6b4ae" />
  </Results>
  <TestDefinitions>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WhenDisabled_ShouldNotProcess" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cb5b6170-9791-0b05-807b-fb298b58c5d8">
      <Execution id="427c574c-68a5-4fa0-8ef9-a464b1e91c4c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_WhenDisabled_ShouldNotProcess" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;-&quot;, uppercase: True, expectedHex: &quot;48-65-6C&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c53401be-8da1-5d73-868a-6420974db0e4">
      <Execution id="e95bea0a-7761-4023-bcba-9f899e950772" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_ConfigurationUpdate_ShouldApplyCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="54caa2f7-9288-7523-076d-6fecd3569986">
      <Execution id="69933988-6dd8-45c6-b700-03788db86fb3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="SerialPortService_ConfigurationUpdate_ShouldApplyCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;COM1&quot;, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="abc2d729-2b39-bae1-e7d9-85d1bd974a73">
      <Execution id="191e4592-2408-44f4-9d69-01917ddfd779" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;Hello&quot;, expectedText: &quot;Hello&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a81de412-4838-6623-61fa-67ddc3188238">
      <Execution id="112e482d-9e22-4afd-aceb-21fdab287505" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToText_WithUTF8Encoding_ShouldReturnCorrectText" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 3000, maxAttempts: 5, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="40f0c48a-0bc6-7691-1bcb-bf225bd03b25">
      <Execution id="736e385b-4e59-4ffd-8b97-5e3efdc40e9e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithReconnectSettings_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8e0d6f2f-fb0e-4668-6fa9-8f21c6f9155a">
      <Execution id="d227e4f4-4801-47da-9f9a-0b4dd2bf1279" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;&quot;, expectedBytes: [])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="35f2d5bf-cf12-732f-0809-1a84cafae57a">
      <Execution id="4353ba2a-d0ab-4929-b927-828a7dbbb3ad" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="586a2248-3398-cdf2-2aaf-e284666f9fc1">
      <Execution id="ad925acd-d884-45a7-b573-b51f3f925dd3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithNullPortName_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48656C6C6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fc0f03d5-65c5-740b-47d9-a78e15ea1d9c">
      <Execution id="91e6ccf5-1140-4e7c-894b-b9c84cfd0891" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fa202986-794c-673e-db28-2db703cc0fc9">
      <Execution id="265895e2-2d09-432c-9174-6123040e99ab" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.SendTextAsync_WithNullText_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2b3d1d5d-a9e9-c463-03be-c93fc127b2b3">
      <Execution id="8cc8eedb-3a41-436d-ada0-7e5b99b1282d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="SendTextAsync_WithNullText_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 9, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bfffdd04-e7b6-b0a1-e537-a650f2f9df27">
      <Execution id="bdee86d2-36da-461b-b3a8-a792499b495c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 65536)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="92f29400-6caf-fff2-71d7-c6b032343516">
      <Execution id="878edbeb-000e-4a73-b4bb-50833218d20b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ConcurrentStatusChanges_ShouldBeThreadSafe" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="9c726bc3-a469-3d93-f1bf-fce1b2658615">
      <Execution id="af705834-4ea4-4cb3-a893-09ba5416b371" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_ConcurrentStatusChanges_ShouldBeThreadSafe" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 4, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c029ac10-2236-0f3e-f243-4fb9689c404b">
      <Execution id="c2e0789f-125e-4c24-bc5d-83a8875bc774" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48 65 6C 6C 6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="de7a5bdb-03df-5bef-5375-0d9de7fb33cd">
      <Execution id="873656a9-bd98-4f4b-ab2c-5994ddf50e2d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_LargeDataPackets_ShouldHandleCorrectly(dataSize: 262144)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e7971ba4-b797-4f77-bdc8-05995b20ff35">
      <Execution id="8ad9afca-579f-4402-b6c1-a1ec4723369d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_LargeDataPackets_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f3202fa7-6b71-5d20-4401-3d8bc9668755">
      <Execution id="146cfeca-66b8-4615-a07d-249d3f27b770" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.Constructor_ShouldInitializeWithDefaultValues" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1e4bdd15-076b-9af7-45c3-530c1fecf022">
      <Execution id="1ef20ea1-d8fd-4e2f-a91e-62157f39efb7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="Constructor_ShouldInitializeWithDefaultValues" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithInvalidHex_ShouldThrowException(invalidHex: &quot;48 65 6C 6C 6&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5dbf9991-d128-405c-e119-2c5c8fb571d8">
      <Execution id="edc708a9-3094-4b78-9cea-bb6e75eba8be" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithInvalidHex_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.SerialPortConnectionException_ShouldInheritFromSerialPortException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bc61f3c1-ffcd-b602-c10b-5bb52b1c6806">
      <Execution id="6f360331-cdfd-482b-ba5a-62ba6e039e16" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="SerialPortConnectionException_ShouldInheritFromSerialPortException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="82d8705e-825e-feba-4da5-acda164c6f53">
      <Execution id="4ccac0a7-4801-498e-9763-eca6b81876ec" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_ErrorHandling_ShouldHandleErrorsGracefully" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c199ae11-7eed-7c3a-2d66-da1a2b9b2453">
      <Execution id="a3dd9230-e594-4498-ae26-98c1386c2864" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="SerialPortService_ErrorHandling_ShouldHandleErrorsGracefully" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;:&quot;, uppercase: False, expectedHex: &quot;48:65:6c&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="68fa167d-968d-2375-d00a-3a724af909d8">
      <Execution id="b8582972-e44a-48ba-b231-0e72b92dbff1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ClearSendBuffer_WhenNotConnected_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="acca298d-8d66-e074-22dd-fdf90888635f">
      <Execution id="e3079799-39a1-418d-ac27-ebb5bef8edd3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ClearSendBuffer_WhenNotConnected_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.BroadcastAsync_WithNullData_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="05265970-e28e-f5df-e7d7-9126a86d207f">
      <Execution id="8d55e145-caff-46c8-8a42-e8ecb923763d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="BroadcastAsync_WithNullData_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;   &quot;, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bfdab111-1736-9094-751d-b681895868a8">
      <Execution id="70dbb3de-0c7a-4e87-8fc4-0f755732891b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(timeoutMs: 1000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="65132280-9071-90ad-f1d3-b2bec385e31a">
      <Execution id="f5dc9014-6480-4954-a528-8a47ed09d889" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 7, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="53a79683-6e18-bdcc-2942-0ae631602802">
      <Execution id="1db23d47-09ac-4d1d-aefd-cf4656b9001b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Constructor_WithValidParameters_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="15548509-32f5-1f17-61af-b5d469801c2e">
      <Execution id="3f2bdda8-daf3-4a67-8b84-c9e43c6b13c3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="Constructor_WithValidParameters_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="08d3fc81-b996-b7a6-e1cd-78cabc053881">
      <Execution id="dabed08d-e088-4c12-bbbb-ca553d4c7410" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.SendAsync_WithNullData_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2e5792b5-5830-a1a1-6907-f9e6eeb32c23">
      <Execution id="d7e25405-f868-4da0-a8e6-c678e0c3e92e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="SendAsync_WithNullData_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Constructor_WithNullLogger_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="acb3181c-ee8d-5c46-618c-09ac65f019a4">
      <Execution id="f0e46f22-a920-46ea-b5a6-5d677757007e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="Constructor_WithNullLogger_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="03e7ae61-8ff6-c0f3-9702-96401e863ad1">
      <Execution id="ab8e16c8-9c1e-4697-a216-f82ca14d7102" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithNullMessage_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithNullBuffer_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="beab0b90-bded-b34e-a1b0-0889a62c7bc7">
      <Execution id="0e821853-5a2d-447b-a432-a4dfdbf2f6eb" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ReadAsync_WithNullBuffer_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 1024, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f0dd822e-110a-9423-89f0-2a6bc0510f84">
      <Execution id="97609364-6292-4b3b-a74e-f8a037cdb599" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBufferSizes_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\r\n&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4ba3dac7-6a62-cb49-022a-20a7d4d37087">
      <Execution id="c60870e9-6add-488a-aa11-e5cbd33e4614" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_SpecialCharacters_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_Default_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ad521c37-2834-6634-6bda-bfb366ff0094">
      <Execution id="57cd573f-663b-4b92-9d10-00fee997223c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_Default_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.CreateDefault_WithEmptyPortName_ShouldReturnInvalidConfiguration" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="37b241c9-a28c-3630-63d5-22833c81cd74">
      <Execution id="4745fcd7-09a8-41eb-93da-33fb819d36d7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="CreateDefault_WithEmptyPortName_ShouldReturnInvalidConfiguration" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(itemCount: 7, shouldTriggerWarning: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="26c01f72-2f55-6301-3660-bbcccfc220bf">
      <Execution id="69fd9dc6-fe48-41cc-bdf1-ba14ddbb3ab4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SetSendRateLimit_WithValidRate_ShouldUpdateLimit" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="147438f0-3f17-1c20-cfc2-e5a56643d7cc">
      <Execution id="1238aa8c-1977-492b-ad07-cfc11de65f0a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="SetSendRateLimit_WithValidRate_ShouldUpdateLimit" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_HighFrequencyCalls_ShouldMaintainPerformance" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2a46be50-f303-bb70-1536-236543d44d59">
      <Execution id="76baeee0-569a-4ade-96c2-1e5a40f06f56" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="CanSend_HighFrequencyCalls_ShouldMaintainPerformance" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithOptions_ShouldRegisterServicesAndOptions" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="edf58ff9-246f-e14c-f25f-39068185472a">
      <Execution id="844f9286-8d43-4a91-8827-d48cab7a8489" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_WithOptions_ShouldRegisterServicesAndOptions" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ClearQueue_WithDataInQueue_ShouldEmptyQueue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d915a464-2e21-829b-f426-6772ddf55055">
      <Execution id="0da91cc3-9f5e-4fbf-8806-dd1eb018cb30" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="ClearQueue_WithDataInQueue_ShouldEmptyQueue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithValidData_ShouldAddToQueue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="31b4aa6a-561a-ec27-c6d2-f1426e253b35">
      <Execution id="581699ab-34f2-4c77-a523-d0150c846c31" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithValidData_ShouldAddToQueue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2bba4be0-2bc3-6003-e0d8-d93e1c6880cb">
      <Execution id="a2b19a6c-ea07-4deb-b9fc-c07d3b4e05b1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.SerialPortDataException_ShouldInheritFromSerialPortException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b1ab4e5a-1abe-d4fc-3bce-7b8f40d0cefc">
      <Execution id="e4ac4c14-e5dd-4b7a-be43-46f0809256d3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="SerialPortDataException_ShouldInheritFromSerialPortException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SetSendRateLimit_WithNegativeRate_ShouldSetToZero" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d030f382-42f0-6302-7045-2cfd01c44112">
      <Execution id="91121044-df15-4780-b551-52488a255331" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="SetSendRateLimit_WithNegativeRate_ShouldSetToZero" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\0&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c4b6fa98-2242-c1ba-efc6-ec174c1be4e2">
      <Execution id="8ad02b69-2acb-4bb9-9d4b-029400de496e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_SpecialCharacters_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.GetStatistics_FrequentCalls_ShouldNotImpactPerformance" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8caa8b5b-b84c-d1d7-e2b2-05bff9553bb9">
      <Execution id="0cec6c94-d8ad-4ff3-838c-4d78b5320243" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="GetStatistics_FrequentCalls_ShouldNotImpactPerformance" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;UTF-16&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="99219d16-4665-2168-0835-d11459e2febf">
      <Execution id="5ad729f5-378c-4426-bee7-2df899f6b4ae" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_DifferentEncodings_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.CreateDefault_ShouldReturnValidConfiguration" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="969900f1-f9c4-1336-3ebc-d975003306b5">
      <Execution id="19fcb5ab-1040-4277-9a68-f2178d0c5adc" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="CreateDefault_ShouldReturnValidConfiguration" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.ContainsPort_WithExistingPort_ShouldReturnTrue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c92fb4d1-98fd-ec97-499d-551eebba0847">
      <Execution id="4cc16902-2296-41a3-9c83-9b2c5ebded0d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="ContainsPort_WithExistingPort_ShouldReturnTrue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 0, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3b43f857-4c69-124a-f1e5-4526c8f72ed1">
      <Execution id="78ae6ec0-9e35-4b27-a4db-82d5dfbcb57b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBufferSizes_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;&quot;, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8927fc38-5352-5a92-ac17-c0b53db402c5">
      <Execution id="d0aff8ce-c47d-4fff-8fe7-c8508af4f8e0" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_LargeDataPackets_ShouldHandleCorrectly(dataSize: 131072)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2759dc48-2d09-a5a8-b7c3-335908e8d07b">
      <Execution id="be204ca5-5cab-49fc-b5f3-1baee1cf962f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_LargeDataPackets_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\t&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="54e12c3f-6b43-5687-a842-a3d35e6a7a19">
      <Execution id="781191c0-d9ff-4074-986a-c60a4d64abed" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_SpecialCharacters_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WhenResolved_ShouldCreateValidInstance" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8b38b793-1bda-2cf1-fc52-ca85a8e00e41">
      <Execution id="b46c3c57-b953-4b30-9e3b-5f136879c6cd" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_WhenResolved_ShouldCreateValidInstance" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(strategy: ThrowException)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ac6b9c02-26c7-4e3d-e30f-fe45601d8bad">
      <Execution id="19e7e54f-5ed6-4d45-9133-1123b65625fb" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 0, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d796aa36-66a9-9e1e-199e-e7f8579e7312">
      <Execution id="6dd4e648-2966-41af-a3a3-db37cde3f8f5" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBaudRate_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [], expectedHex: &quot;&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c34da281-7d78-d48e-fd36-b17e7d8c79e7">
      <Execution id="65c3ad0d-2574-4d74-8901-8c6d080792c6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ClearReceiveBuffer_WhenNotConnected_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a2862178-237f-01ca-e3a5-f47ceb7b9484">
      <Execution id="224ed723-bbab-4960-91f9-b496d43d0b11" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ClearReceiveBuffer_WhenNotConnected_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 0, maxAttempts: -1, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c783c277-08d3-0e65-e123-287a21c8b95a">
      <Execution id="2253ba2d-649a-44b3-8b47-a7e9c27a57cd" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithReconnectSettings_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 300)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f26d999c-5247-a0ac-b5ab-848e393d4022">
      <Execution id="86c379aa-92c3-482c-ba9b-4113d13c30dc" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1b9f49aa-b9ff-fd05-f03b-43ccb23d82e6">
      <Execution id="5e5dec9d-7f15-47dd-a5f6-18b65cb07788" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithPortName_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: RtsCts)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1ec54028-be89-592f-c08c-8fb797e62772">
      <Execution id="33cf1a5a-ffd3-4f22-8996-348021aec466" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: 0, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="db250e33-9c97-4756-436a-16d6e8cf414f">
      <Execution id="aa409082-b757-40d0-b6f7-e3588bc2670e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithTimeouts_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ClearDataQueue_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d72a782f-3fee-0055-ced6-0e57470253f9">
      <Execution id="abf3202c-0ac5-4350-96ed-fb34e2a9d4fc" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ClearDataQueue_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_WhenDisabled_ShouldReturnTrue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f9be5f74-0f42-c1ca-06ce-4ff54de6dea3">
      <Execution id="d4b4cd30-dd68-46d4-990c-51172ccb5955" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="CanSend_WhenDisabled_ShouldReturnTrue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithInvalidOffset_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="746694ab-c99f-c4df-9347-d40ac323bdff">
      <Execution id="db67cd9a-2c8f-4377-a242-0f82dc416dd7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ReadAsync_WithInvalidOffset_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.TryDequeueData_WithEmptyQueue_ShouldReturnFalse" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="df64e4db-ecba-2113-fb43-2182e90a55d5">
      <Execution id="0867df74-143c-4c90-a1c2-4c3324e0636f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="TryDequeueData_WithEmptyQueue_ShouldReturnFalse" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Configure_WithNullConfiguration_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ef67f86c-3dd1-1171-22b1-c007547c1494">
      <Execution id="38022a40-325b-459c-bb70-f9ccf9778e59" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="Configure_WithNullConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithNullServices_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ff8dcbb8-c854-51a3-cf7a-a1ca3b1fcc29">
      <Execution id="c504d023-5665-4657-a2a0-fb0a18367165" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_WithNullServices_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e835e9bc-7afc-0744-d083-42c92aa262b2">
      <Execution id="8eb868c9-ba9d-4c5d-a26a-804050d3fa09" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithNullMessage_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: None)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a17ba197-677f-433a-d98b-90b6dba91665">
      <Execution id="8ad8ff1e-7453-43a0-8fde-d6ec5e47d769" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.GetDataCopy_ShouldReturnIndependentCopy" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2f010a59-e386-1c48-7a96-6eaf300b4a00">
      <Execution id="854495bb-1d00-426f-a63e-9566a23f564d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="GetDataCopy_ShouldReturnIndependentCopy" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5a625eaf-ce33-5cc0-168a-ac8f3f113fa3">
      <Execution id="ad6ef08a-4f7a-4a28-b8c8-30b65cc7bbdc" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(sequence: [19, 19, 17])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cd2d846a-4253-c669-9172-ae3f3e9c95ea">
      <Execution id="117930f2-8516-4ca5-8460-3c76c470a3a3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="78f63ed6-a82b-1dc7-8333-4f5a85338c27">
      <Execution id="adc937b7-b14a-4f2b-bccc-34d004ef6655" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithNullMessage_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.BroadcastTextAsync_WithNullText_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ea26ec81-d8c7-5fae-e953-a047af719c17">
      <Execution id="45a627ae-d748-483f-9744-f235b6058196" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="BroadcastTextAsync_WithNullText_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.GetBytesToWrite_WhenNotConnected_ShouldReturnZero" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d864081d-48a6-ba7d-caf4-2925abe987a3">
      <Execution id="b136ea5e-505e-44c7-a589-cef28ad03033" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="GetBytesToWrite_WhenNotConnected_ShouldReturnZero" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: -1, maxAttempts: 0, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="33597ab1-9d34-65c5-b0a1-f1a694190e93">
      <Execution id="f650b668-8906-49a5-8828-fe880bf328cc" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithReconnectSettings_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtMaxCapacity_ShouldHandleCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4e85762c-ae91-2b9e-76be-257c688fb9fa">
      <Execution id="0eab09e4-0364-4a33-93dd-ed348c68475d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_AtMaxCapacity_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithXonAfterXoff_ShouldResumeFlow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cf59dfeb-fc64-c423-a638-29288238b6c4">
      <Execution id="ab609544-db7d-4ac8-8d0a-1bea29de6800" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_WithXonAfterXoff_ShouldResumeFlow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithoutLogging_ShouldStillWork" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0a76b3a1-8066-016c-7b64-ba09c55edde9">
      <Execution id="90d9d8a7-0a13-4ac0-86de-f1c8e640f994" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_WithoutLogging_ShouldStillWork" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 9600, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="38e1a48a-8126-3cc7-e8ac-60f91e67cb3d">
      <Execution id="7624ebe5-daa1-41a9-bdc5-479922f746f5" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBaudRate_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(requestCount: 1000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a0d6040b-1605-8c04-788d-3eef62fbd2d3">
      <Execution id="bd5d58f9-3c1f-4071-9a0b-23f4143abee6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="6c69255c-6ff2-34a3-9fdf-65012918c8be">
      <Execution id="ffc722f7-02db-4d6f-a165-1592793e9b6e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.GetStatistics_UnderHighLoad_ShouldProvideAccurateData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f5c5b75e-4e7c-c2d4-8d83-637641048e9e">
      <Execution id="f7ded333-2442-4535-beeb-5cad7a5115ca" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="GetStatistics_UnderHighLoad_ShouldProvideAccurateData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(sequence: [17, 17, 19])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="74f91c1b-75f8-0352-e93b-da086f3f05ba">
      <Execution id="34f1ab25-38d9-41e7-9620-b85ab3a6d875" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_Default_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c46d3460-b4d6-d480-98b9-89cb5e5d84d1">
      <Execution id="c809a4fc-7fec-447c-8827-6505fb5a3d7a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_Default_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c4268a3e-dbd0-ce2a-3f14-43313d1251f3">
      <Execution id="9960f9c4-a31b-43fa-ab87-770539b32eb8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithNullMessage_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WhenNearCapacity_ShouldTriggerWarningEvent" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="700480ab-f9a3-c14c-3862-c09cc3b0f4a7">
      <Execution id="f4e2ec1d-146b-4313-a2ca-892abac4174f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WhenNearCapacity_ShouldTriggerWarningEvent" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0d5b3495-7932-d5af-331e-630ce28c78dd">
      <Execution id="a068ddfc-afe3-497f-9aa9-b40d4397376b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithPortName_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.AddSerialPort_WithNullService_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="94919b74-043c-ac5d-87f1-0587b8f46ef7">
      <Execution id="fee3bd86-4716-4cc1-a83d-00af277a730a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="AddSerialPort_WithNullService_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="023e96e9-d51b-a358-64ce-ba11d0feba6d">
      <Execution id="f6ad7a10-f070-4ce5-ab5b-82a9ca0b875b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithPortName_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_InvalidConfiguration_ShouldThrowException(portName: &quot;INVALID&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4797f9c7-926d-5ab1-d952-801179405c81">
      <Execution id="7297732d-ec6e-4446-99f0-e2507458bca6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_InvalidConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithNullByteArray_ShouldInitializeWithEmptyData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d8f02cf7-433b-61ec-c449-566982f978ff">
      <Execution id="66dc07dc-f62a-4d08-bbbd-d32329c29cc3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_WithNullByteArray_ShouldInitializeWithEmptyData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_NullAndEmptyParameters_ShouldHandleGracefully" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="eb395603-826c-2aec-bdbc-d35ee9d4654e">
      <Execution id="4894f2b0-9694-49b6-8266-9b0f6bddef57" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_NullAndEmptyParameters_ShouldHandleGracefully" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 4096)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ca5223c1-4903-c112-ec50-72cd8a818e1b">
      <Execution id="8638ed3a-1212-4d72-a349-986ccab9f8b7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 921600)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="40dc612e-fcad-d21a-6d8f-c2c13cc86897">
      <Execution id="ffd1d515-da22-44d7-b116-ea5e145602e5" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_TinyDataPackets_ShouldHandleCorrectly(dataSize: 0)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="7356f68e-ad7b-97c4-f64c-2536928bb3c9">
      <Execution id="13387a55-7862-4956-b42d-7f70a3e3070a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_TinyDataPackets_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithEmptyData_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="7f56e2f6-e85d-2d67-fab2-f0a6cfe57e1f">
      <Execution id="a66f3d49-174b-4b2b-bce7-be130ec928ad" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_WithEmptyData_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.GlobalReconnectOptions_ShouldHaveCorrectDefaults" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="dca00415-9a9b-5e9a-5d7c-36ae82b195dc">
      <Execution id="27bd1231-10cf-43f4-ab29-3a21b6bda3d0" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="GlobalReconnectOptions_ShouldHaveCorrectDefaults" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_InvalidConfiguration_ShouldThrowException(portName: &quot;&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="06f82529-1ce7-16b1-604c-9f0392f7d160">
      <Execution id="5a95d898-f888-487f-827f-0e0f78395c21" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_InvalidConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2cc2a28b-0583-c385-8580-90889ea0fa8d">
      <Execution id="80549065-30c1-43b4-afdd-7fec28755de1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.GetStatistics_ShouldReturnCorrectInformation" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="14c24c6f-1bd1-8a0b-0fdb-8dccb26c718e">
      <Execution id="d2b68d6c-a74b-471b-ad55-73b4b9cdf457" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="GetStatistics_ShouldReturnCorrectInformation" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 6, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0acc92da-f9c6-47d9-c5a2-760ab204265e">
      <Execution id="39a3a842-2ae5-452b-986f-a88765692495" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithValidParameters_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fcf1ca95-bb0c-f451-5094-8b054adedf81">
      <Execution id="3ba032f4-7cd8-4267-8949-78e9f17ed13d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Constructor_WithValidParameters_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\ud83d\ude80\ud83d\udd25\ud83d\udcbb&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8d9dd9a9-c289-1130-7c61-b67125a5dbe3">
      <Execution id="260757b9-59ce-494a-908d-c1bc96bd42c7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_SpecialCharacters_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\&quot;'\\&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5de9e3f2-f5fe-88a8-b3fb-d72657ad04d9">
      <Execution id="69ca79bf-cb37-41a1-a1fc-25cd47290b3e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_SpecialCharacters_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SendRateLimit_ExtremeValues_ShouldHandleCorrectly(rateLimit: 1000000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f9ae6db7-303a-9c26-b311-d84f4bd95208">
      <Execution id="c3d7760c-5ef0-4733-a20a-33a29fbcd7ba" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="SendRateLimit_ExtremeValues_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_ShouldRegisterServicesWithCorrectLifetime" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e95c7136-7996-a786-e59e-522991193040">
      <Execution id="07f6ed4f-fe14-4ef2-8376-89e0d57c4b22" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_ShouldRegisterServicesWithCorrectLifetime" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(dataSize: 4096)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4e851d5d-05a7-2eac-92f0-8bfc94b85dc6">
      <Execution id="14b55c07-b5e0-4b82-8ac8-a5e9450b52a8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 1)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="902ef6cf-b62e-58ea-9022-da49556e41c5">
      <Execution id="44efd3a7-438a-4c7c-9ce2-7f7de7206f10" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeTimeouts_ShouldRespectSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.Constructor_WithNullConfiguration_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="03bac210-5121-4552-6b4a-8ad9c8a4430a">
      <Execution id="ce7da6a9-6149-49cd-9094-58f20440b3ae" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="Constructor_WithNullConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithMultipleItems_ShouldReturnCorrectCount" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ceb4348a-7f39-5209-f0c5-47a92f0ccf52">
      <Execution id="5372f721-3499-45a4-922c-9934aa9b3fdd" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="DequeueBatch_WithMultipleItems_ShouldReturnCorrectCount" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToString_ShouldReturnFormattedString" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ee4c97f9-aba3-7fd3-4eff-c0ae32af980a">
      <Execution id="3b46205f-e603-4332-ad62-b4afe7d18f05" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToString_ShouldReturnFormattedString" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.RecordSend_WithNegativeLength_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e39f649e-7c27-616e-c761-ed717edf3ede">
      <Execution id="e70a19d8-619b-4956-be98-7ecf0ffa030c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="RecordSend_WithNegativeLength_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.TryDequeueData_WithDataInQueue_ShouldReturnData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a70006d6-2422-b7b2-466f-65fda27fd805">
      <Execution id="ea664d28-6937-42f9-9d02-403bf4e51635" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="TryDequeueData_WithDataInQueue_ShouldReturnData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 1200)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b531b8c7-1b6c-8c36-b996-64028f4f0324">
      <Execution id="ecd4c796-ce68-409f-b9f7-9ee437ba0e94" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(sequence: [17, 19, 17])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4a656b7f-67e3-8f8d-6a98-8264a3231c8f">
      <Execution id="6dfe7602-8ba3-409c-aef2-41f6f765fb07" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_Default_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="57f89734-de5a-0489-86e3-f364df300ca2">
      <Execution id="e11f5824-6b84-44d0-a5f0-4ab5316d014b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_Default_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b777246c-f407-ff52-0009-26e9cffd3bc2">
      <Execution id="716a1c32-8db4-4260-82ea-8ab38c12f8eb" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithNullPortName_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f60c8033-5a87-0f3a-3088-48468fd9301b">
      <Execution id="2dab2e59-a053-4d4f-a9e6-ae50b35eee0e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortManager_BasicOperations_ShouldWorkCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2f29d2d9-c8a3-7a41-59a4-b408ec3f6dd9">
      <Execution id="e36db05d-10fa-4840-892e-e55f80910478" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="SerialPortManager_BasicOperations_ShouldWorkCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithByteArray_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f543d2e9-4ced-9d03-4307-68823ad67ec5">
      <Execution id="9e7e2dad-9c0d-4f34-a021-d8aa4a0b7d0e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_WithByteArray_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;ASCII&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1a863009-7253-b831-45ba-4599991a1e23">
      <Execution id="8e5b263e-1864-4058-996d-f986d4c253f9" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_DifferentEncodings_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithInvalidHex_ShouldThrowException(invalidHex: &quot;GG&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0e388063-0d1d-fee8-e535-f6ea426ca6a0">
      <Execution id="9b71647f-9881-457a-bb7c-0c1856991431" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithInvalidHex_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(strategy: DropNewest)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ccb6e43b-ed87-41fb-aacf-8532bac21ec1">
      <Execution id="a8d9e12b-811a-4eb0-a54b-dbd75e6edc4b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;中文测试&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b8418e21-906c-bab7-f26a-6263d8be1dd9">
      <Execution id="a9d03deb-9d98-4156-a354-d875be8d54e3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_SpecialCharacters_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_MixedWithRegularData_ShouldExtractControlCharsCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="35ae527b-65f4-e761-c5e9-42b376ef79c1">
      <Execution id="2139bd88-44e5-43bd-b55c-9f7a89cd520f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_MixedWithRegularData_ShouldExtractControlCharsCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(timeoutMs: 5000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="520ee81f-3aa8-ca94-4ff6-979e424be6fe">
      <Execution id="2190249f-61f9-43c7-8229-862b3d3f10ed" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.CreateSerialPort_WithValidConfiguration_ShouldCreateAndAddPort" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="62c90d57-0073-79e7-453d-174a3cf7a9c9">
      <Execution id="b8b83d23-c75a-4709-967d-d19ccb5a8bad" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="CreateSerialPort_WithValidConfiguration_ShouldCreateAndAddPort" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_HighFrequencyOperations_ShouldMaintainPerformance" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="66753aa0-12f4-9a94-0af2-e1d64e8dae4a">
      <Execution id="04fc1006-3b5f-4835-9687-a8fe256b8f29" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_HighFrequencyOperations_ShouldMaintainPerformance" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 100)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="330fed7e-e885-a7aa-44dc-e8f169ebddaf">
      <Execution id="c860e5f7-6339-4558-9b46-0a7151ad7395" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeTimeouts_ShouldRespectSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: null, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5772937f-dba3-ddd7-fc6d-303fd2ade221">
      <Execution id="7534e18f-7bd0-4595-b4bd-6c43c719f5f3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithLargeDataVolume_ShouldHandleMemoryPressure" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="eddea9f3-c4cb-08bd-7c5f-ec33540ba04f">
      <Execution id="ca5adf9b-35bc-4e26-8c73-ed85297b8686" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithLargeDataVolume_ShouldHandleMemoryPressure" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(timeoutMs: 100)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="db95ccd0-c126-7f48-a28b-a633c62602b2">
      <Execution id="7c4295e4-6c19-4e83-b65f-ba243850d603" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: -1, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="09ce9de4-34a5-edec-6b47-7870a0ac8152">
      <Execution id="9df96433-583c-4b4b-a2ef-035c9562768a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithTimeouts_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Dispose_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f4fbaad3-6781-c8aa-cfe1-bc48e5a2111b">
      <Execution id="5854acf2-d9fb-4703-9042-1a95150375ad" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="Dispose_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(itemCount: 8, shouldTriggerWarning: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2b64ca9d-2218-a1bd-b07d-cbcc8dde3d16">
      <Execution id="0784ceb0-4f14-44f8-9686-4d33786a3bfe" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithNullOrWhiteSpace_ShouldReturnEmptyData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1fc3a25a-3320-5e0f-9e3e-b9761c8f4549">
      <Execution id="41233f07-5353-4f56-978c-37305331dfad" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithNullOrWhiteSpace_ShouldReturnEmptyData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_CalledMultipleTimes_ShouldNotDuplicateServices" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="387aec6f-b8f3-0719-a44f-14efd8e3f6b0">
      <Execution id="c8f1629c-da0d-402f-8559-6ac0661e286d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_CalledMultipleTimes_ShouldNotDuplicateServices" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(dataSize: 1000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3da8ccb5-36b2-5b31-1537-436bec0c05cc">
      <Execution id="3d4166e6-e5a9-4a4d-b79c-fa665d4f868c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d554c12b-5ed2-23ff-fe49-9271ab3c4030">
      <Execution id="633947d2-46e3-4688-b6a7-73eddaae7a63" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="7498a7db-5111-4360-9445-2e4763e8cd4e">
      <Execution id="d48d28f7-37ab-43b5-b403-b91626f4741c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ConcurrentBufferOperations_WithEventHandlers_ShouldBeThreadSafe" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4f9d435a-f0d2-5d93-e815-ec19ec4acd79">
      <Execution id="92c950d1-df38-406b-9bea-558f11eb118f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="ConcurrentBufferOperations_WithEventHandlers_ShouldBeThreadSafe" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SetRtsFlowControl_ConcurrentOperations_ShouldBeThreadSafe" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="28df04bd-39fc-1cce-6c87-14cb33ebc5ed">
      <Execution id="869d4b0f-b542-4c5f-9045-cd39ab4c1a87" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="SetRtsFlowControl_ConcurrentOperations_ShouldBeThreadSafe" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_RapidConnectDisconnect_ShouldHandleStably" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="97ad4d87-64e8-3d1f-2000-33d34d4e461d">
      <Execution id="9c5b6496-637b-4108-9012-ce25baaa7be4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_RapidConnectDisconnect_ShouldHandleStably" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.QueueUsagePercentage_ShouldCalculateCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="011894a4-ce24-7e54-f1d5-7f0ac286b44b">
      <Execution id="7f878894-a4c3-4469-a4ea-f7eecdcb8d9c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="QueueUsagePercentage_ShouldCalculateCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_InvalidConfiguration_ShouldThrowException(portName: &quot;COM999&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c828ecfa-33bf-6434-f923-b9cfd77032bc">
      <Execution id="beea40c7-8e6e-4901-bbc6-51908b27b734" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_InvalidConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SendRateLimit_ExtremeValues_ShouldHandleCorrectly(rateLimit: 2147483647)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c4f03241-cdba-e70d-96b7-1a09d3cf8bfb">
      <Execution id="3708c74d-0813-4d22-ab37-7e7151a009ea" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="SendRateLimit_ExtremeValues_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [72, 101, 108, 108, 111], expectedHex: &quot;48 65 6C 6C 6F&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ed73d661-31f4-564a-3bca-e7bd1da12bc7">
      <Execution id="bc4fe66c-e692-4924-b447-01bba7076734" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 30000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a094d57c-350b-c7d9-7ef4-ab145f0776e7">
      <Execution id="de433dcd-76fa-456e-bd56-57452700faa5" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeTimeouts_ShouldRespectSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="de18f2eb-3df5-540f-0a3a-47950916d770">
      <Execution id="c42b7672-37ec-427b-ac4d-d5b8df3653fc" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Constructor_WithNullConfiguration_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ad145a9e-531c-61f2-55a9-e48b910a979e">
      <Execution id="dcb1813b-2cbe-4689-a2fc-5ceb6b226c66" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="Constructor_WithNullConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 115200, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="456b7034-8cd0-175d-c38d-784450059613">
      <Execution id="e1b102d1-6696-4fe7-b104-bd7cc959aa03" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBaudRate_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithZeroLengthData_ShouldHandleCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3a22456d-b134-6108-c30d-835c62639107">
      <Execution id="89d58db3-bf60-4099-8a8c-ada2225ad5f6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithZeroLengthData_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;&quot;, uppercase: True, expectedHex: &quot;48656C&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fcb4374f-81fa-4153-7c7a-49168b68b53b">
      <Execution id="0ab58745-bff7-4175-a771-69249b3486f3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_ConcurrentAccess_ShouldBeThreadSafe" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3154c378-cb14-2e6f-72c2-17b34c4e8da5">
      <Execution id="28288568-0a3b-4662-ad9f-55ccc3f43bbf" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_ConcurrentAccess_ShouldBeThreadSafe" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.GetStatistics_ShouldReturnValidStatistics" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bac682fb-bc9a-5ee1-6f8f-e6c1b404c020">
      <Execution id="66367763-fca9-4335-87b2-39b184603e3b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="GetStatistics_ShouldReturnValidStatistics" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4910faa5-5592-c319-7394-2de698b2c0c7">
      <Execution id="564651da-3d9f-44b9-8a42-61622853b193" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithNullPortName_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.BufferManagement_WhenAdvancedBufferingDisabled_ShouldReturnDefaults" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="259912bc-7987-fbbd-0222-56d9037315ca">
      <Execution id="824bb285-e496-41a9-a92d-e3176d8738e6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="BufferManagement_WhenAdvancedBufferingDisabled_ShouldReturnDefaults" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_WithRateLimit_ShouldEnforceTimingAccurately" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4d4c87b9-bd77-56b0-acd7-7c0c49cbc08f">
      <Execution id="f72ed453-828a-43b1-852f-b1bbc4c2815c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="CanSend_WithRateLimit_ShouldEnforceTimingAccurately" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.AddSerialPort_WithValidService_ShouldAddToCollection" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3d3291e3-98dd-e9e2-de72-914857c1022d">
      <Execution id="b824207b-c8aa-44f5-8ce8-ee3c58951e84" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="AddSerialPort_WithValidService_ShouldAddToCollection" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Constructor_WithNullServiceProvider_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e0e718ff-4c78-ae1f-949d-1594d0430302">
      <Execution id="115c6a11-a88f-43d7-a07c-968f091b4c4e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="Constructor_WithNullServiceProvider_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_WhenEnabled_ShouldReturnTrue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e3460a9b-1799-f6b6-2f2c-a356970be8b9">
      <Execution id="61fd99a4-a2cc-43de-85f1-4ed762beac7c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="CanSend_WhenEnabled_ShouldReturnTrue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_LargeDataPackets_ShouldHandleCorrectly(dataSize: 65536)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="02b715c1-0002-be47-2b1c-d64152d8f12d">
      <Execution id="beeb5424-4920-46e2-9298-c666abbcb191" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_LargeDataPackets_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Disable_ShouldSetIsEnabledToFalse" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="374deec9-2627-2caa-434b-b1767297fddb">
      <Execution id="19cc99e3-1ae7-4bd9-b6e8-e4bfaef0151d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Disable_ShouldSetIsEnabledToFalse" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithInvalidCount_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f758c623-141e-3413-11d2-f683cfe5a47c">
      <Execution id="c47bf3f4-5bc7-48b3-b091-5763827bd1a6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ReadAsync_WithInvalidCount_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [255, 0, 171], expectedHex: &quot;FF 00 AB&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="54219804-c561-b035-0838-e2a41e644606">
      <Execution id="9461cb61-7b4e-4252-8b9c-7f228984a49d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.AddSerialPort_WithDuplicatePortName_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="69868bb1-a9df-9b47-1d6a-43a9bb280b16">
      <Execution id="9060b876-36ec-4786-90f1-3e41f5e29b13" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="AddSerialPort_WithDuplicatePortName_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_ShouldReturnServiceCollection" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="9510bd6c-2b16-38c6-12a7-a2ae238d0e20">
      <Execution id="51b6e7cb-f0e4-44e3-a338-66017e7d0033" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_ShouldReturnServiceCollection" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 9600)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d99254fa-0691-194c-a6ad-0465dd755790">
      <Execution id="ddac0606-4e68-405e-952e-c65730577bc2" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_TinyDataPackets_ShouldHandleCorrectly(dataSize: 2)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="08289273-2527-c675-4a6a-0bcbc2ae148c">
      <Execution id="2a99b7ff-36b0-4f74-89f6-f6fbbc53f425" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_TinyDataPackets_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_ConnectionLost_ShouldHandleGracefully" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4593fd1a-0307-349e-dd30-e0a7cd2c3a3b">
      <Execution id="07468682-eaaa-4e99-bb81-b57d43ab62b8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_ConnectionLost_ShouldHandleGracefully" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Constructor_WithValidConfiguration_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="65c4cae6-02b9-cdd9-6a8b-d3eb8e0242f1">
      <Execution id="00d1e193-5c71-472f-823b-b604e66296de" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="Constructor_WithValidConfiguration_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_AdvancedBuffering_ShouldConfigureCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5bacd699-c1c3-a30f-c688-788c48f5fa12">
      <Execution id="cafda20a-444f-4357-8268-2dc3ada236ab" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="SerialPortService_AdvancedBuffering_ShouldConfigureCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.RemoveSerialPort_WithExistingPort_ShouldRemoveAndReturnTrue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5bb814e9-4a25-51d9-60b7-6112270973bf">
      <Execution id="799da879-90ae-45bf-9711-17e323b7c423" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="RemoveSerialPort_WithExistingPort_ShouldRemoveAndReturnTrue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithXoffData_ShouldPauseFlow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="89904fc7-f945-148c-427f-28dd272d907e">
      <Execution id="d8df03a1-86fc-4fbc-8e31-ccb120fd07e1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_WithXoffData_ShouldPauseFlow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_ShouldRegisterRequiredServices" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3d8895f0-2b0a-046b-ed9b-3a2696b478f8">
      <Execution id="9ef69fd3-9585-4110-adfb-f912cc5a5921" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_ShouldRegisterRequiredServices" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_RepeatedOperations_ShouldNotLeakMemory" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b25ea050-dc5b-ed4b-30ec-a1476f14ecc6">
      <Execution id="6578f54f-10cc-41ce-8b31-b4967ad30071" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_RepeatedOperations_ShouldNotLeakMemory" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 8, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d095da48-d2a1-b869-306b-68ec74fb6a86">
      <Execution id="f2ce9005-8196-4d7c-8ca3-f474a179af06" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 115200)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="48f760e6-1514-1162-1f47-005a4a4b8f9d">
      <Execution id="4fe6a727-0604-4700-8ae1-ee83ca7272e7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithText_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a33546b2-5014-1109-76f2-708037dcb465">
      <Execution id="f0215e41-f689-4e46-a3ac-c22a9fa7348a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_WithText_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.RemoveSerialPort_WithNullOrEmptyPortName_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="be06675f-3c25-5bc8-de68-febd1ec325bb">
      <Execution id="b8fad011-f80c-49de-b352-ea6d38e5626c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="RemoveSerialPort_WithNullOrEmptyPortName_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithNullData_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="29cc443b-5843-75d0-329d-fdd89478ba0f">
      <Execution id="7f884700-60c8-4cf8-95bb-622a7a8c645d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithNullData_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ConcurrentOperations_WithCancellation_ShouldHandleGracefully" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="66354075-61e8-8db6-93a7-d9e160dce397">
      <Execution id="79607f67-ba92-4860-bb7c-6443dff7da36" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="ConcurrentOperations_WithCancellation_ShouldHandleGracefully" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Dispose_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="9bd2b2f8-4184-1ac4-6817-df59f52f3eae">
      <Execution id="6997fa49-c83b-4f3c-badf-134b929fb38e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Dispose_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 0, maxAttempts: 0, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cf6e81ed-2d36-4cb5-7d19-3207ec4a1e6b">
      <Execution id="2424791d-7fc2-40eb-8e3c-50e14d114627" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithReconnectSettings_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 1)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="11d85114-8736-9f44-b948-699605b5a326">
      <Execution id="45e27199-b61c-487d-ba1f-0c3d0fcf178f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: -1, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2660b25c-0ab3-7226-1818-3de3d93f7e8b">
      <Execution id="bdc4ed34-3a31-4608-9948-b56c9848e631" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBufferSizes_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.SerialPortException_ShouldInheritFromException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="53690872-c918-73be-c68f-532030de60cd">
      <Execution id="49bcc3fb-cd36-4c15-8695-aa0dc6c5d995" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="SerialPortException_ShouldInheritFromException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_BufferOverflow_ShouldHandleGracefully" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c18a85de-caa6-f92b-dd42-c491eddd8d5a">
      <Execution id="5ef0d7be-d0e5-44b0-8050-63b4542daed9" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_BufferOverflow_ShouldHandleGracefully" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;&quot;, expectedText: &quot;&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="7c790003-2d63-4b93-840e-28312682c36d">
      <Execution id="4c98645f-8b70-473c-98f4-39dee8cd28ef" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToText_WithUTF8Encoding_ShouldReturnCorrectText" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: Both)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cffe2f0d-7498-e2dd-fb74-c52cfbb78304">
      <Execution id="53082dbc-74e1-4cc2-a677-f7950464b2bc" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.RecordSend_WithValidData_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0ba56fbe-43b1-2a63-5322-5cc9943a2bc2">
      <Execution id="c1719d34-f6c3-4b6a-b257-1b65f3776050" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="RecordSend_WithValidData_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_Default_ShouldInitializeWithEmptyData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5168b9bd-ef9a-5e12-e4e4-606e2d42d0c0">
      <Execution id="d04f8c47-04c8-4aa9-a706-1d6644f6c4ef" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_Default_ShouldInitializeWithEmptyData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithMoreRequestedThanAvailable_ShouldReturnAllAvailable" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d0e94aff-761a-b752-8ea1-b05e263e6baa">
      <Execution id="d969b723-3ac6-4818-84e4-82c7a2c98252" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="DequeueBatch_WithMoreRequestedThanAvailable_ShouldReturnAllAvailable" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithNullData_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fc11fc4c-e912-c68a-17af-aad770dde13c">
      <Execution id="9c9235e5-eab6-4581-abc8-924cdfe89d79" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_WithNullData_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithBufferOverflow_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d3314a3a-784e-50f7-5f7e-7145efc09018">
      <Execution id="34ffe3fd-7d92-4698-910f-ddfa65e65e88" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ReadAsync_WithBufferOverflow_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(requestCount: -1)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c8b6c16e-78a6-4e53-1410-df1171a01492">
      <Execution id="75c8fa63-4855-4020-9cb0-61e1208b5e71" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_TinyDataPackets_ShouldHandleCorrectly(dataSize: 1)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2a2a8bb1-f3b5-0d08-191a-eec8cd12af85">
      <Execution id="bb0984bb-f693-4bea-97d9-39fa9a767c02" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_TinyDataPackets_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ConcurrentOperations_ResourceContention_ShouldNotDeadlock" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bdd00514-c674-7fbe-6276-f883dc5e0af9">
      <Execution id="2e9652e0-70c1-4385-891b-ea03cf69e362" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="ConcurrentOperations_ResourceContention_ShouldNotDeadlock" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_BasicLifecycle_ShouldWorkCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b5d0270e-cd4b-dce1-3f1c-951f7852f9a7">
      <Execution id="f31ff85c-6c26-446c-8491-2eaa92da6a9b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="SerialPortService_BasicLifecycle_ShouldWorkCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Dispose_CalledMultipleTimes_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="6ff352e2-9dcf-35a2-90fb-231d5f1f5bba">
      <Execution id="dcce725a-3baa-4638-bb8a-b3098ad570a1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Dispose_CalledMultipleTimes_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 4096, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3f1e1745-b426-f5fa-fc4d-7c36c8ed8987">
      <Execution id="aeb13019-0cdc-4ed2-9622-d586cb42060c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBufferSizes_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;你好&quot;, expectedText: &quot;你好&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ae04b4e0-cf08-4697-35c4-f48e7c5131c5">
      <Execution id="7972e2bd-d48e-4c9f-9f6c-c5de9c71038c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToText_WithUTF8Encoding_ShouldReturnCorrectText" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 5, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8c638f55-8f9a-3b1d-dff8-8169a30d369f">
      <Execution id="3e4cf76d-13c8-464d-a507-02378947bb40" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="befe512a-b39c-f7de-f215-29ab09232596">
      <Execution id="85b3e4a5-f106-440d-bb75-e2b80e8f461b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 512)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="80209ebf-9964-6643-698c-8cc7c39137a7">
      <Execution id="2b018ead-2d18-4cde-aee1-f2497f0113ed" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;UTF-8&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2b557e92-a648-4516-f450-d8ea7736c72f">
      <Execution id="a40b518c-756c-492e-8760-9d2e6b8f6a1c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_DifferentEncodings_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: 1000, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="44298843-f215-2386-e136-f2a25ffedf0e">
      <Execution id="f01c165e-a7d9-4565-9fc0-075062972101" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithTimeouts_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(dataSize: 1024)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="90a5f1aa-f90c-1c7c-8cfb-a59b5defb741">
      <Execution id="f7bb6d1f-b688-4638-a823-7a43f067dfef" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_ConcurrentOperations_ShouldHandleParallelRequests" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="14978f86-a584-09a6-4ff2-659b440bf384">
      <Execution id="cf8eb3f2-89e2-4de1-9155-a05ee18abb15" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="SerialPortService_ConcurrentOperations_ShouldHandleParallelRequests" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(dataSize: 100000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ed1c132d-d00f-bdaf-6b94-61ad7a3557e9">
      <Execution id="0f171567-98f2-4f22-a163-23feff577da3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.BufferManager_HighLoadOperations_ShouldMaintainReasonableCpuUsage" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5cd769ba-e210-24e0-902c-6f839ac6a5e1">
      <Execution id="235382cc-a47b-445e-b6ba-c5b14ef0796c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="BufferManager_HighLoadOperations_ShouldMaintainReasonableCpuUsage" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;UTF-32&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8043c734-3566-ddb7-1d4a-07a44a746bbb">
      <Execution id="b3585c23-e8af-4d34-8d59-00b546c6f44e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_DifferentEncodings_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(dataSize: 100)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c6821c0d-34ef-41c3-52bd-004793cc5855">
      <Execution id="a0a2a64f-7332-444b-b96f-10b13d570e6f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.SerialPortServiceOptions_ShouldHaveCorrectDefaults" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e5f24540-0924-e65b-6f16-4ab793a71012">
      <Execution id="c8fe6d4e-00f1-4538-b4a9-9f88f4a05583" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="SerialPortServiceOptions_ShouldHaveCorrectDefaults" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48-65-6C-6C-6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4fb7a94f-b5ad-a202-33ad-c14f2afe94a5">
      <Execution id="bb501047-38cc-4bd0-adc2-198494ac977f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: XonXoff)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b1444560-e97c-3484-c5b5-7415dcdaf44b">
      <Execution id="f5da3f38-4d4b-4e29-91f2-b4995da3f097" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.GetBytesToRead_WhenNotConnected_ShouldReturnZero" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="7255bb30-60f6-a3b4-e259-af25a7d0a5b7">
      <Execution id="eada3be6-70e1-49e6-8ffd-afa93fa3dabf" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="GetBytesToRead_WhenNotConnected_ShouldReturnZero" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_ContinuousOverflow_ShouldMaintainStability" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="dee417d5-57ab-c1ce-a824-beeb5380368e">
      <Execution id="c022c2e3-9dde-45aa-897e-07b502b9b107" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_ContinuousOverflow_ShouldMaintainStability" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetAllStatus_WithMultiplePorts_ShouldReturnAllStatuses" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2e50dc24-0e76-792e-ccef-ba135fad6d20">
      <Execution id="f9c0ea0c-6bff-4513-b974-c9da4c6dc3e5" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="GetAllStatus_WithMultiplePorts_ShouldReturnAllStatuses" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WhenQueueFull_ShouldTriggerOverflowEvent" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5fcd1008-efe6-9593-2351-b4703f8be1f5">
      <Execution id="8db0f0d5-9287-4456-9d73-bae0ba0b58ec" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WhenQueueFull_ShouldTriggerOverflowEvent" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_DeviceRemoved_ShouldDetectAndRecover" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e33e1140-ec5a-c360-ef7c-351a69dc550b">
      <Execution id="b71e87c9-3be0-48bf-884d-4ecc6e597201" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_DeviceRemoved_ShouldDetectAndRecover" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(requestCount: 0)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d205cea9-26a0-cb1a-deb8-bd301be91a7a">
      <Execution id="7d4c6c9f-117c-4303-890a-e2a50da69a57" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 2147483647)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="11e2ecbb-54ae-ecc5-771b-56b899b2be70">
      <Execution id="1368d0de-880c-462d-aec9-d4589f4badc6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeTimeouts_ShouldRespectSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.GetAvailablePorts_ShouldReturnPortArray" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8b2e14a6-d10e-1c5b-c949-007826a28517">
      <Execution id="6fcc4274-e979-4c64-9621-d44d4a445e87" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="GetAvailablePorts_ShouldReturnPortArray" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_Default_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ee05eb54-5290-18f9-2d55-e832c1a8888d">
      <Execution id="696dd883-f8f7-4d27-9cb3-91e8a9a3fede" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_Default_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueDequeue_ConcurrentOperations_ShouldBeThreadSafe" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f7c5e95f-be71-08d0-bb43-352b994d7d2c">
      <Execution id="047d9dca-fd24-4b85-b786-edbf3655ba5c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueDequeue_ConcurrentOperations_ShouldBeThreadSafe" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SendRateLimit_ExtremeValues_ShouldHandleCorrectly(rateLimit: 1)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="19c767b9-00f8-d409-00da-eeea99ad6661">
      <Execution id="3cbcaf9a-2d6e-40b3-af4b-a3f923a3d14f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="SendRateLimit_ExtremeValues_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetAvailablePorts_ShouldReturnPortArray" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cac85d36-7607-899b-f9db-0914e3049caf">
      <Execution id="d9cd0d0c-bc42-4436-b2bf-858398f1a93a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="GetAvailablePorts_ShouldReturnPortArray" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: -1, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="10ecc65a-1d98-44c2-5793-97f0d54a583d">
      <Execution id="030b426f-a420-4545-bead-7090d2015f56" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBaudRate_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.RemoveSerialPort_WithNonExistentPort_ShouldReturnFalse" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="55695b9c-8fa7-75b9-c4de-aa77143fb3c0">
      <Execution id="7feea879-aeea-4b5d-a8e7-56eb1a35f073" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="RemoveSerialPort_WithNonExistentPort_ShouldReturnFalse" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetSerialPort_WithNonExistentPort_ShouldReturnNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="06b280c7-f3e9-b8e7-377e-97f90aa891c1">
      <Execution id="360c5d24-39f1-4afe-8bba-f0b0dcf349c5" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="GetSerialPort_WithNonExistentPort_ShouldReturnNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Configure_WithValidConfiguration_ShouldUpdateConfiguration" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="186e3f0a-894f-97e9-d204-455dc212b0c8">
      <Execution id="30ce4ab7-24f9-49cb-b5fd-f6705165ffea" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="Configure_WithValidConfiguration_ShouldUpdateConfiguration" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.CreateSerialPort_WithDuplicatePortName_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f7c3f6f4-f082-0f10-2964-f125578d9325">
      <Execution id="fafde010-82f0-4604-9b00-c71b6ed5fe56" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="CreateSerialPort_WithDuplicatePortName_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(dataSize: 10000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0110b712-a0ab-bd1c-e53f-1f5be5b0e0fa">
      <Execution id="43344386-3121-441f-b384-a432f4251d52" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Enable_ShouldSetIsEnabledToTrue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b3f503b4-beb7-1162-dc16-c72eb6d05b08">
      <Execution id="155fb51c-0595-4c5c-985e-6091fa30cf5b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Enable_ShouldSetIsEnabledToTrue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.SerialPortConfigurationException_ShouldInheritFromSerialPortException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ac78eccc-7e0e-a33e-3154-a1d2a9e455e3">
      <Execution id="d5005e0e-b80d-428a-8773-bc66b2fa41ed" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="SerialPortConfigurationException_ShouldInheritFromSerialPortException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Dispose_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3fede858-ef69-906c-1781-83551e8174e8">
      <Execution id="20598506-185c-4dfb-8491-f22d64930d67" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="Dispose_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.Constructor_WithValidConfiguration_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1110dce6-cdc2-edbd-83e1-76acc885583d">
      <Execution id="87c1a5e9-9d0a-4475-a7bb-de94005a590d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="Constructor_WithValidConfiguration_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithNullText_ShouldInitializeWithEmptyData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="86618079-abf9-30c9-2492-2a4b2efcc170">
      <Execution id="2b5cf430-0de1-402e-9c92-901549345197" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_WithNullText_ShouldInitializeWithEmptyData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="eec8e6e9-5b71-12c7-7998-04c7b1e88c20">
      <Execution id="50e91c56-4526-47d1-b04d-aeff40edbebf" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithPortName_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fc3b21c3-0f8b-a760-763b-ba136c1f3355">
      <Execution id="fc9c58be-78a9-44e2-954f-f9b5108baaa1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithNullPortName_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="aa267d8a-b773-ddc4-6cfe-031c9347f3d1">
      <Execution id="c6f681e4-218a-4edc-a95f-2e10c5a3472e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetSerialPort_WithExistingPort_ShouldReturnService" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="81eb87c9-cb57-f039-ec3e-433c7ad89d3b">
      <Execution id="27ad2e10-18db-46a7-9284-47b5e2a549af" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="GetSerialPort_WithExistingPort_ShouldReturnService" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.BufferManager_LongRunningOperations_ShouldNotLeakMemory" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4f700146-33f1-ec69-f82a-0bacec577d1e">
      <Execution id="d37bbae8-6552-4777-901f-3f75541afd79" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="BufferManager_LongRunningOperations_ShouldNotLeakMemory" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48:65:6C:6C:6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="faacff99-6157-5e13-4b0f-4d4468903363">
      <Execution id="785951b6-c82b-4edf-98a2-7dc6ef60871b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Constructor_WithNullLogger_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d2dd8c1b-a46c-c63e-2bb9-e11f3f0fe385">
      <Execution id="8c31e9ab-4957-4893-9316-5e68cd5e0ed6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="Constructor_WithNullLogger_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b8844c7c-ca09-4104-11ef-326b5bcd433f">
      <Execution id="5d977581-ac72-48ab-91a7-ee38458d07cd" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.CreateSerialPort_WithNullConfiguration_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="43a8f9d1-3c2f-114d-d7c3-2fa82d3ccdfd">
      <Execution id="0345589f-e0a8-44f5-ae94-44d6ba955409" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="CreateSerialPort_WithNullConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithNullConfigureOptions_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bed1de91-05bf-0085-34af-7bee2cd33ea7">
      <Execution id="77e56f5f-e5e4-43d5-9e24-9933631ba42c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_WithNullConfigureOptions_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(itemCount: 9, shouldTriggerWarning: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="57a78de4-cb96-8d86-813e-ab82acf74480">
      <Execution id="7f3d0102-fcd1-4cb0-81ba-4b680b9acd9c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;COM2&quot;, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d42f65dc-b483-693d-547e-03998b4ce142">
      <Execution id="19ccb6b8-2320-4fe3-b6fe-c928ec33e51b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithRtsCtsType_ShouldNotProcessXonXoff" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f4bd691f-6e1a-eb6a-8014-5eb6cc5c57a1">
      <Execution id="f3db0982-ac00-4527-997c-3218340b3a96" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_WithRtsCtsType_ShouldNotProcessXonXoff" />
    </UnitTest>
  </TestDefinitions>
  <TestEntries>
    <TestEntry testId="a17ba197-677f-433a-d98b-90b6dba91665" executionId="8ad8ff1e-7453-43a0-8fde-d6ec5e47d769" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d2dd8c1b-a46c-c63e-2bb9-e11f3f0fe385" executionId="8c31e9ab-4957-4893-9316-5e68cd5e0ed6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="acca298d-8d66-e074-22dd-fdf90888635f" executionId="e3079799-39a1-418d-ac27-ebb5bef8edd3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="023e96e9-d51b-a358-64ce-ba11d0feba6d" executionId="f6ad7a10-f070-4ce5-ab5b-82a9ca0b875b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0e388063-0d1d-fee8-e535-f6ea426ca6a0" executionId="9b71647f-9881-457a-bb7c-0c1856991431" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2a2a8bb1-f3b5-0d08-191a-eec8cd12af85" executionId="bb0984bb-f693-4bea-97d9-39fa9a767c02" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="dee417d5-57ab-c1ce-a824-beeb5380368e" executionId="c022c2e3-9dde-45aa-897e-07b502b9b107" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="08289273-2527-c675-4a6a-0bcbc2ae148c" executionId="2a99b7ff-36b0-4f74-89f6-f6fbbc53f425" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="beab0b90-bded-b34e-a1b0-0889a62c7bc7" executionId="0e821853-5a2d-447b-a432-a4dfdbf2f6eb" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f60c8033-5a87-0f3a-3088-48468fd9301b" executionId="2dab2e59-a053-4d4f-a9e6-ae50b35eee0e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="38e1a48a-8126-3cc7-e8ac-60f91e67cb3d" executionId="7624ebe5-daa1-41a9-bdc5-479922f746f5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="82d8705e-825e-feba-4da5-acda164c6f53" executionId="4ccac0a7-4801-498e-9763-eca6b81876ec" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="aa267d8a-b773-ddc4-6cfe-031c9347f3d1" executionId="c6f681e4-218a-4edc-a95f-2e10c5a3472e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c029ac10-2236-0f3e-f243-4fb9689c404b" executionId="c2e0789f-125e-4c24-bc5d-83a8875bc774" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="faacff99-6157-5e13-4b0f-4d4468903363" executionId="785951b6-c82b-4edf-98a2-7dc6ef60871b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ee05eb54-5290-18f9-2d55-e832c1a8888d" executionId="696dd883-f8f7-4d27-9cb3-91e8a9a3fede" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1b9f49aa-b9ff-fd05-f03b-43ccb23d82e6" executionId="5e5dec9d-7f15-47dd-a5f6-18b65cb07788" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8b2e14a6-d10e-1c5b-c949-007826a28517" executionId="6fcc4274-e979-4c64-9621-d44d4a445e87" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c783c277-08d3-0e65-e123-287a21c8b95a" executionId="2253ba2d-649a-44b3-8b47-a7e9c27a57cd" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7356f68e-ad7b-97c4-f64c-2536928bb3c9" executionId="13387a55-7862-4956-b42d-7f70a3e3070a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="520ee81f-3aa8-ca94-4ff6-979e424be6fe" executionId="2190249f-61f9-43c7-8229-862b3d3f10ed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f9ae6db7-303a-9c26-b311-d84f4bd95208" executionId="c3d7760c-5ef0-4733-a20a-33a29fbcd7ba" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ae04b4e0-cf08-4697-35c4-f48e7c5131c5" executionId="7972e2bd-d48e-4c9f-9f6c-c5de9c71038c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f4fbaad3-6781-c8aa-cfe1-bc48e5a2111b" executionId="5854acf2-d9fb-4703-9042-1a95150375ad" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2cc2a28b-0583-c385-8580-90889ea0fa8d" executionId="80549065-30c1-43b4-afdd-7fec28755de1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0ba56fbe-43b1-2a63-5322-5cc9943a2bc2" executionId="c1719d34-f6c3-4b6a-b257-1b65f3776050" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="011894a4-ce24-7e54-f1d5-7f0ac286b44b" executionId="7f878894-a4c3-4469-a4ea-f7eecdcb8d9c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bc61f3c1-ffcd-b602-c10b-5bb52b1c6806" executionId="6f360331-cdfd-482b-ba5a-62ba6e039e16" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="11e2ecbb-54ae-ecc5-771b-56b899b2be70" executionId="1368d0de-880c-462d-aec9-d4589f4badc6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ceb4348a-7f39-5209-f0c5-47a92f0ccf52" executionId="5372f721-3499-45a4-922c-9934aa9b3fdd" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c199ae11-7eed-7c3a-2d66-da1a2b9b2453" executionId="a3dd9230-e594-4498-ae26-98c1386c2864" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f5c5b75e-4e7c-c2d4-8d83-637641048e9e" executionId="f7ded333-2442-4535-beeb-5cad7a5115ca" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="de18f2eb-3df5-540f-0a3a-47950916d770" executionId="c42b7672-37ec-427b-ac4d-d5b8df3653fc" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fcf1ca95-bb0c-f451-5094-8b054adedf81" executionId="3ba032f4-7cd8-4267-8949-78e9f17ed13d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e39f649e-7c27-616e-c761-ed717edf3ede" executionId="e70a19d8-619b-4956-be98-7ecf0ffa030c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bac682fb-bc9a-5ee1-6f8f-e6c1b404c020" executionId="66367763-fca9-4335-87b2-39b184603e3b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5772937f-dba3-ddd7-fc6d-303fd2ade221" executionId="7534e18f-7bd0-4595-b4bd-6c43c719f5f3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2bba4be0-2bc3-6003-e0d8-d93e1c6880cb" executionId="a2b19a6c-ea07-4deb-b9fc-c07d3b4e05b1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d99254fa-0691-194c-a6ad-0465dd755790" executionId="ddac0606-4e68-405e-952e-c65730577bc2" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b3f503b4-beb7-1162-dc16-c72eb6d05b08" executionId="155fb51c-0595-4c5c-985e-6091fa30cf5b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4e851d5d-05a7-2eac-92f0-8bfc94b85dc6" executionId="14b55c07-b5e0-4b82-8ac8-a5e9450b52a8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0d5b3495-7932-d5af-331e-630ce28c78dd" executionId="a068ddfc-afe3-497f-9aa9-b40d4397376b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4797f9c7-926d-5ab1-d952-801179405c81" executionId="7297732d-ec6e-4446-99f0-e2507458bca6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="19c767b9-00f8-d409-00da-eeea99ad6661" executionId="3cbcaf9a-2d6e-40b3-af4b-a3f923a3d14f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2b64ca9d-2218-a1bd-b07d-cbcc8dde3d16" executionId="0784ceb0-4f14-44f8-9686-4d33786a3bfe" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8e0d6f2f-fb0e-4668-6fa9-8f21c6f9155a" executionId="d227e4f4-4801-47da-9f9a-0b4dd2bf1279" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7498a7db-5111-4360-9445-2e4763e8cd4e" executionId="d48d28f7-37ab-43b5-b403-b91626f4741c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ff8dcbb8-c854-51a3-cf7a-a1ca3b1fcc29" executionId="c504d023-5665-4657-a2a0-fb0a18367165" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="08d3fc81-b996-b7a6-e1cd-78cabc053881" executionId="dabed08d-e088-4c12-bbbb-ca553d4c7410" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e7971ba4-b797-4f77-bdc8-05995b20ff35" executionId="8ad9afca-579f-4402-b6c1-a1ec4723369d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="330fed7e-e885-a7aa-44dc-e8f169ebddaf" executionId="c860e5f7-6339-4558-9b46-0a7151ad7395" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="40f0c48a-0bc6-7691-1bcb-bf225bd03b25" executionId="736e385b-4e59-4ffd-8b97-5e3efdc40e9e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="68fa167d-968d-2375-d00a-3a724af909d8" executionId="b8582972-e44a-48ba-b231-0e72b92dbff1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="86618079-abf9-30c9-2492-2a4b2efcc170" executionId="2b5cf430-0de1-402e-9c92-901549345197" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="dca00415-9a9b-5e9a-5d7c-36ae82b195dc" executionId="27bd1231-10cf-43f4-ab29-3a21b6bda3d0" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7c790003-2d63-4b93-840e-28312682c36d" executionId="4c98645f-8b70-473c-98f4-39dee8cd28ef" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b1ab4e5a-1abe-d4fc-3bce-7b8f40d0cefc" executionId="e4ac4c14-e5dd-4b7a-be43-46f0809256d3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="53690872-c918-73be-c68f-532030de60cd" executionId="49bcc3fb-cd36-4c15-8695-aa0dc6c5d995" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="de7a5bdb-03df-5bef-5375-0d9de7fb33cd" executionId="873656a9-bd98-4f4b-ab2c-5994ddf50e2d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2f29d2d9-c8a3-7a41-59a4-b408ec3f6dd9" executionId="e36db05d-10fa-4840-892e-e55f80910478" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3f1e1745-b426-f5fa-fc4d-7c36c8ed8987" executionId="aeb13019-0cdc-4ed2-9622-d586cb42060c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5bb814e9-4a25-51d9-60b7-6112270973bf" executionId="799da879-90ae-45bf-9711-17e323b7c423" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5168b9bd-ef9a-5e12-e4e4-606e2d42d0c0" executionId="d04f8c47-04c8-4aa9-a706-1d6644f6c4ef" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="eddea9f3-c4cb-08bd-7c5f-ec33540ba04f" executionId="ca5adf9b-35bc-4e26-8c73-ed85297b8686" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3d8895f0-2b0a-046b-ed9b-3a2696b478f8" executionId="9ef69fd3-9585-4110-adfb-f912cc5a5921" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="62c90d57-0073-79e7-453d-174a3cf7a9c9" executionId="b8b83d23-c75a-4709-967d-d19ccb5a8bad" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="74f91c1b-75f8-0352-e93b-da086f3f05ba" executionId="34f1ab25-38d9-41e7-9620-b85ab3a6d875" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0110b712-a0ab-bd1c-e53f-1f5be5b0e0fa" executionId="43344386-3121-441f-b384-a432f4251d52" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="03e7ae61-8ff6-c0f3-9702-96401e863ad1" executionId="ab8e16c8-9c1e-4697-a216-f82ca14d7102" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3da8ccb5-36b2-5b31-1537-436bec0c05cc" executionId="3d4166e6-e5a9-4a4d-b79c-fa665d4f868c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c4268a3e-dbd0-ce2a-3f14-43313d1251f3" executionId="9960f9c4-a31b-43fa-ab87-770539b32eb8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d864081d-48a6-ba7d-caf4-2925abe987a3" executionId="b136ea5e-505e-44c7-a589-cef28ad03033" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4f700146-33f1-ec69-f82a-0bacec577d1e" executionId="d37bbae8-6552-4777-901f-3f75541afd79" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="10ecc65a-1d98-44c2-5793-97f0d54a583d" executionId="030b426f-a420-4545-bead-7090d2015f56" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ad521c37-2834-6634-6bda-bfb366ff0094" executionId="57cd573f-663b-4b92-9d10-00fee997223c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8043c734-3566-ddb7-1d4a-07a44a746bbb" executionId="b3585c23-e8af-4d34-8d59-00b546c6f44e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3d3291e3-98dd-e9e2-de72-914857c1022d" executionId="b824207b-c8aa-44f5-8ce8-ee3c58951e84" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d915a464-2e21-829b-f426-6772ddf55055" executionId="0da91cc3-9f5e-4fbf-8806-dd1eb018cb30" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2e50dc24-0e76-792e-ccef-ba135fad6d20" executionId="f9c0ea0c-6bff-4513-b974-c9da4c6dc3e5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6ff352e2-9dcf-35a2-90fb-231d5f1f5bba" executionId="dcce725a-3baa-4638-bb8a-b3098ad570a1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b777246c-f407-ff52-0009-26e9cffd3bc2" executionId="716a1c32-8db4-4260-82ea-8ab38c12f8eb" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2b3d1d5d-a9e9-c463-03be-c93fc127b2b3" executionId="8cc8eedb-3a41-436d-ada0-7e5b99b1282d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3154c378-cb14-2e6f-72c2-17b34c4e8da5" executionId="28288568-0a3b-4662-ad9f-55ccc3f43bbf" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="abc2d729-2b39-bae1-e7d9-85d1bd974a73" executionId="191e4592-2408-44f4-9d69-01917ddfd779" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="acb3181c-ee8d-5c46-618c-09ac65f019a4" executionId="f0e46f22-a920-46ea-b5a6-5d677757007e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bed1de91-05bf-0085-34af-7bee2cd33ea7" executionId="77e56f5f-e5e4-43d5-9e24-9933631ba42c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="147438f0-3f17-1c20-cfc2-e5a56643d7cc" executionId="1238aa8c-1977-492b-ad07-cfc11de65f0a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cf6e81ed-2d36-4cb5-7d19-3207ec4a1e6b" executionId="2424791d-7fc2-40eb-8e3c-50e14d114627" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ee4c97f9-aba3-7fd3-4eff-c0ae32af980a" executionId="3b46205f-e603-4332-ad62-b4afe7d18f05" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6c69255c-6ff2-34a3-9fdf-65012918c8be" executionId="ffc722f7-02db-4d6f-a165-1592793e9b6e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5fcd1008-efe6-9593-2351-b4703f8be1f5" executionId="8db0f0d5-9287-4456-9d73-bae0ba0b58ec" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="456b7034-8cd0-175d-c38d-784450059613" executionId="e1b102d1-6696-4fe7-b104-bd7cc959aa03" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="df64e4db-ecba-2113-fb43-2182e90a55d5" executionId="0867df74-143c-4c90-a1c2-4c3324e0636f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c53401be-8da1-5d73-868a-6420974db0e4" executionId="e95bea0a-7761-4023-bcba-9f899e950772" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="05265970-e28e-f5df-e7d7-9126a86d207f" executionId="8d55e145-caff-46c8-8a42-e8ecb923763d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="700480ab-f9a3-c14c-3862-c09cc3b0f4a7" executionId="f4e2ec1d-146b-4313-a2ca-892abac4174f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3b43f857-4c69-124a-f1e5-4526c8f72ed1" executionId="78ae6ec0-9e35-4b27-a4db-82d5dfbcb57b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fc0f03d5-65c5-740b-47d9-a78e15ea1d9c" executionId="91e6ccf5-1140-4e7c-894b-b9c84cfd0891" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b8418e21-906c-bab7-f26a-6263d8be1dd9" executionId="a9d03deb-9d98-4156-a354-d875be8d54e3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0acc92da-f9c6-47d9-c5a2-760ab204265e" executionId="39a3a842-2ae5-452b-986f-a88765692495" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bfdab111-1736-9094-751d-b681895868a8" executionId="70dbb3de-0c7a-4e87-8fc4-0f755732891b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f7c3f6f4-f082-0f10-2964-f125578d9325" executionId="fafde010-82f0-4604-9b00-c71b6ed5fe56" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="edf58ff9-246f-e14c-f25f-39068185472a" executionId="844f9286-8d43-4a91-8827-d48cab7a8489" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ef67f86c-3dd1-1171-22b1-c007547c1494" executionId="38022a40-325b-459c-bb70-f9ccf9778e59" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e835e9bc-7afc-0744-d083-42c92aa262b2" executionId="8eb868c9-ba9d-4c5d-a26a-804050d3fa09" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d42f65dc-b483-693d-547e-03998b4ce142" executionId="19ccb6b8-2320-4fe3-b6fe-c928ec33e51b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a33546b2-5014-1109-76f2-708037dcb465" executionId="f0215e41-f689-4e46-a3ac-c22a9fa7348a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7255bb30-60f6-a3b4-e259-af25a7d0a5b7" executionId="eada3be6-70e1-49e6-8ffd-afa93fa3dabf" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c6821c0d-34ef-41c3-52bd-004793cc5855" executionId="a0a2a64f-7332-444b-b96f-10b13d570e6f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="03bac210-5121-4552-6b4a-8ad9c8a4430a" executionId="ce7da6a9-6149-49cd-9094-58f20440b3ae" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f3202fa7-6b71-5d20-4401-3d8bc9668755" executionId="146cfeca-66b8-4615-a07d-249d3f27b770" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cac85d36-7607-899b-f9db-0914e3049caf" executionId="d9cd0d0c-bc42-4436-b2bf-858398f1a93a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b5d0270e-cd4b-dce1-3f1c-951f7852f9a7" executionId="f31ff85c-6c26-446c-8491-2eaa92da6a9b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ad145a9e-531c-61f2-55a9-e48b910a979e" executionId="dcb1813b-2cbe-4689-a2fc-5ceb6b226c66" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bfffdd04-e7b6-b0a1-e537-a650f2f9df27" executionId="bdee86d2-36da-461b-b3a8-a792499b495c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ccb6e43b-ed87-41fb-aacf-8532bac21ec1" executionId="a8d9e12b-811a-4eb0-a54b-dbd75e6edc4b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="28df04bd-39fc-1cce-6c87-14cb33ebc5ed" executionId="869d4b0f-b542-4c5f-9045-cd39ab4c1a87" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9bd2b2f8-4184-1ac4-6817-df59f52f3eae" executionId="6997fa49-c83b-4f3c-badf-134b929fb38e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="81eb87c9-cb57-f039-ec3e-433c7ad89d3b" executionId="27ad2e10-18db-46a7-9284-47b5e2a549af" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e33e1140-ec5a-c360-ef7c-351a69dc550b" executionId="b71e87c9-3be0-48bf-884d-4ecc6e597201" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="15548509-32f5-1f17-61af-b5d469801c2e" executionId="3f2bdda8-daf3-4a67-8b84-c9e43c6b13c3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="969900f1-f9c4-1336-3ebc-d975003306b5" executionId="19fcb5ab-1040-4277-9a68-f2178d0c5adc" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="eec8e6e9-5b71-12c7-7998-04c7b1e88c20" executionId="50e91c56-4526-47d1-b04d-aeff40edbebf" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f26d999c-5247-a0ac-b5ab-848e393d4022" executionId="86c379aa-92c3-482c-ba9b-4113d13c30dc" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d8f02cf7-433b-61ec-c449-566982f978ff" executionId="66dc07dc-f62a-4d08-bbbd-d32329c29cc3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2759dc48-2d09-a5a8-b7c3-335908e8d07b" executionId="be204ca5-5cab-49fc-b5f3-1baee1cf962f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4fb7a94f-b5ad-a202-33ad-c14f2afe94a5" executionId="bb501047-38cc-4bd0-adc2-198494ac977f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="40dc612e-fcad-d21a-6d8f-c2c13cc86897" executionId="ffd1d515-da22-44d7-b116-ea5e145602e5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fc11fc4c-e912-c68a-17af-aad770dde13c" executionId="9c9235e5-eab6-4581-abc8-924cdfe89d79" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="48f760e6-1514-1162-1f47-005a4a4b8f9d" executionId="4fe6a727-0604-4700-8ae1-ee83ca7272e7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2b557e92-a648-4516-f450-d8ea7736c72f" executionId="a40b518c-756c-492e-8760-9d2e6b8f6a1c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8b38b793-1bda-2cf1-fc52-ca85a8e00e41" executionId="b46c3c57-b953-4b30-9e3b-5f136879c6cd" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2f010a59-e386-1c48-7a96-6eaf300b4a00" executionId="854495bb-1d00-426f-a63e-9566a23f564d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3fede858-ef69-906c-1781-83551e8174e8" executionId="20598506-185c-4dfb-8491-f22d64930d67" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a81de412-4838-6623-61fa-67ddc3188238" executionId="112e482d-9e22-4afd-aceb-21fdab287505" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fcb4374f-81fa-4153-7c7a-49168b68b53b" executionId="0ab58745-bff7-4175-a771-69249b3486f3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="02b715c1-0002-be47-2b1c-d64152d8f12d" executionId="beeb5424-4920-46e2-9298-c666abbcb191" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ea26ec81-d8c7-5fae-e953-a047af719c17" executionId="45a627ae-d748-483f-9744-f235b6058196" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8c638f55-8f9a-3b1d-dff8-8169a30d369f" executionId="3e4cf76d-13c8-464d-a507-02378947bb40" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b25ea050-dc5b-ed4b-30ec-a1476f14ecc6" executionId="6578f54f-10cc-41ce-8b31-b4967ad30071" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="09ce9de4-34a5-edec-6b47-7870a0ac8152" executionId="9df96433-583c-4b4b-a2ef-035c9562768a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8caa8b5b-b84c-d1d7-e2b2-05bff9553bb9" executionId="0cec6c94-d8ad-4ff3-838c-4d78b5320243" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d3314a3a-784e-50f7-5f7e-7145efc09018" executionId="34ffe3fd-7d92-4698-910f-ddfa65e65e88" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fa202986-794c-673e-db28-2db703cc0fc9" executionId="265895e2-2d09-432c-9174-6123040e99ab" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="53a79683-6e18-bdcc-2942-0ae631602802" executionId="1db23d47-09ac-4d1d-aefd-cf4656b9001b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4f9d435a-f0d2-5d93-e815-ec19ec4acd79" executionId="92c950d1-df38-406b-9bea-558f11eb118f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ca5223c1-4903-c112-ec50-72cd8a818e1b" executionId="8638ed3a-1212-4d72-a349-986ccab9f8b7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a70006d6-2422-b7b2-466f-65fda27fd805" executionId="ea664d28-6937-42f9-9d02-403bf4e51635" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="eb395603-826c-2aec-bdbc-d35ee9d4654e" executionId="4894f2b0-9694-49b6-8266-9b0f6bddef57" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7f56e2f6-e85d-2d67-fab2-f0a6cfe57e1f" executionId="a66f3d49-174b-4b2b-bce7-be130ec928ad" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b8844c7c-ca09-4104-11ef-326b5bcd433f" executionId="5d977581-ac72-48ab-91a7-ee38458d07cd" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e95c7136-7996-a786-e59e-522991193040" executionId="07f6ed4f-fe14-4ef2-8376-89e0d57c4b22" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="31b4aa6a-561a-ec27-c6d2-f1426e253b35" executionId="581699ab-34f2-4c77-a523-d0150c846c31" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="902ef6cf-b62e-58ea-9022-da49556e41c5" executionId="44efd3a7-438a-4c7c-9ce2-7f7de7206f10" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4d4c87b9-bd77-56b0-acd7-7c0c49cbc08f" executionId="f72ed453-828a-43b1-852f-b1bbc4c2815c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ac6b9c02-26c7-4e3d-e30f-fe45601d8bad" executionId="19e7e54f-5ed6-4d45-9133-1123b65625fb" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="db95ccd0-c126-7f48-a28b-a633c62602b2" executionId="7c4295e4-6c19-4e83-b65f-ba243850d603" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a0d6040b-1605-8c04-788d-3eef62fbd2d3" executionId="bd5d58f9-3c1f-4071-9a0b-23f4143abee6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e3460a9b-1799-f6b6-2f2c-a356970be8b9" executionId="61fd99a4-a2cc-43de-85f1-4ed762beac7c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2e5792b5-5830-a1a1-6907-f9e6eeb32c23" executionId="d7e25405-f868-4da0-a8e6-c678e0c3e92e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="33597ab1-9d34-65c5-b0a1-f1a694190e93" executionId="f650b668-8906-49a5-8828-fe880bf328cc" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="54caa2f7-9288-7523-076d-6fecd3569986" executionId="69933988-6dd8-45c6-b700-03788db86fb3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="14c24c6f-1bd1-8a0b-0fdb-8dccb26c718e" executionId="d2b68d6c-a74b-471b-ad55-73b4b9cdf457" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="92f29400-6caf-fff2-71d7-c6b032343516" executionId="878edbeb-000e-4a73-b4bb-50833218d20b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="43a8f9d1-3c2f-114d-d7c3-2fa82d3ccdfd" executionId="0345589f-e0a8-44f5-ae94-44d6ba955409" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="befe512a-b39c-f7de-f215-29ab09232596" executionId="85b3e4a5-f106-440d-bb75-e2b80e8f461b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="186e3f0a-894f-97e9-d204-455dc212b0c8" executionId="30ce4ab7-24f9-49cb-b5fd-f6705165ffea" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="54e12c3f-6b43-5687-a842-a3d35e6a7a19" executionId="781191c0-d9ff-4074-986a-c60a4d64abed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1ec54028-be89-592f-c08c-8fb797e62772" executionId="33cf1a5a-ffd3-4f22-8996-348021aec466" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="259912bc-7987-fbbd-0222-56d9037315ca" executionId="824bb285-e496-41a9-a92d-e3176d8738e6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cb5b6170-9791-0b05-807b-fb298b58c5d8" executionId="427c574c-68a5-4fa0-8ef9-a464b1e91c4c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="78f63ed6-a82b-1dc7-8333-4f5a85338c27" executionId="adc937b7-b14a-4f2b-bccc-34d004ef6655" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="14978f86-a584-09a6-4ff2-659b440bf384" executionId="cf8eb3f2-89e2-4de1-9155-a05ee18abb15" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1e4bdd15-076b-9af7-45c3-530c1fecf022" executionId="1ef20ea1-d8fd-4e2f-a91e-62157f39efb7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="06b280c7-f3e9-b8e7-377e-97f90aa891c1" executionId="360c5d24-39f1-4afe-8bba-f0b0dcf349c5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c92fb4d1-98fd-ec97-499d-551eebba0847" executionId="4cc16902-2296-41a3-9c83-9b2c5ebded0d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="be06675f-3c25-5bc8-de68-febd1ec325bb" executionId="b8fad011-f80c-49de-b352-ea6d38e5626c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e0e718ff-4c78-ae1f-949d-1594d0430302" executionId="115c6a11-a88f-43d7-a07c-968f091b4c4e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4e85762c-ae91-2b9e-76be-257c688fb9fa" executionId="0eab09e4-0364-4a33-93dd-ed348c68475d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f7c5e95f-be71-08d0-bb43-352b994d7d2c" executionId="047d9dca-fd24-4b85-b786-edbf3655ba5c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b1444560-e97c-3484-c5b5-7415dcdaf44b" executionId="f5da3f38-4d4b-4e29-91f2-b4995da3f097" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="35ae527b-65f4-e761-c5e9-42b376ef79c1" executionId="2139bd88-44e5-43bd-b55c-9f7a89cd520f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ed1c132d-d00f-bdaf-6b94-61ad7a3557e9" executionId="0f171567-98f2-4f22-a163-23feff577da3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5dbf9991-d128-405c-e119-2c5c8fb571d8" executionId="edc708a9-3094-4b78-9cea-bb6e75eba8be" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="55695b9c-8fa7-75b9-c4de-aa77143fb3c0" executionId="7feea879-aeea-4b5d-a8e7-56eb1a35f073" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="66354075-61e8-8db6-93a7-d9e160dce397" executionId="79607f67-ba92-4860-bb7c-6443dff7da36" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="89904fc7-f945-148c-427f-28dd272d907e" executionId="d8df03a1-86fc-4fbc-8e31-ccb120fd07e1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="35f2d5bf-cf12-732f-0809-1a84cafae57a" executionId="4353ba2a-d0ab-4929-b927-828a7dbbb3ad" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2a46be50-f303-bb70-1536-236543d44d59" executionId="76baeee0-569a-4ade-96c2-1e5a40f06f56" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ac78eccc-7e0e-a33e-3154-a1d2a9e455e3" executionId="d5005e0e-b80d-428a-8773-bc66b2fa41ed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8d9dd9a9-c289-1130-7c61-b67125a5dbe3" executionId="260757b9-59ce-494a-908d-c1bc96bd42c7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3a22456d-b134-6108-c30d-835c62639107" executionId="89d58db3-bf60-4099-8a8c-ada2225ad5f6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="db250e33-9c97-4756-436a-16d6e8cf414f" executionId="aa409082-b757-40d0-b6f7-e3588bc2670e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="65132280-9071-90ad-f1d3-b2bec385e31a" executionId="f5dc9014-6480-4954-a528-8a47ed09d889" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="57a78de4-cb96-8d86-813e-ab82acf74480" executionId="7f3d0102-fcd1-4cb0-81ba-4b680b9acd9c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5a625eaf-ce33-5cc0-168a-ac8f3f113fa3" executionId="ad6ef08a-4f7a-4a28-b8c8-30b65cc7bbdc" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="11d85114-8736-9f44-b948-699605b5a326" executionId="45e27199-b61c-487d-ba1f-0c3d0fcf178f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c46d3460-b4d6-d480-98b9-89cb5e5d84d1" executionId="c809a4fc-7fec-447c-8827-6505fb5a3d7a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0a76b3a1-8066-016c-7b64-ba09c55edde9" executionId="90d9d8a7-0a13-4ac0-86de-f1c8e640f994" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cffe2f0d-7498-e2dd-fb74-c52cfbb78304" executionId="53082dbc-74e1-4cc2-a677-f7950464b2bc" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1fc3a25a-3320-5e0f-9e3e-b9761c8f4549" executionId="41233f07-5353-4f56-978c-37305331dfad" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5cd769ba-e210-24e0-902c-6f839ac6a5e1" executionId="235382cc-a47b-445e-b6ba-c5b14ef0796c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fc3b21c3-0f8b-a760-763b-ba136c1f3355" executionId="fc9c58be-78a9-44e2-954f-f9b5108baaa1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1110dce6-cdc2-edbd-83e1-76acc885583d" executionId="87c1a5e9-9d0a-4475-a7bb-de94005a590d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c34da281-7d78-d48e-fd36-b17e7d8c79e7" executionId="65c3ad0d-2574-4d74-8901-8c6d080792c6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4a656b7f-67e3-8f8d-6a98-8264a3231c8f" executionId="6dfe7602-8ba3-409c-aef2-41f6f765fb07" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f9be5f74-0f42-c1ca-06ce-4ff54de6dea3" executionId="d4b4cd30-dd68-46d4-990c-51172ccb5955" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9c726bc3-a469-3d93-f1bf-fce1b2658615" executionId="af705834-4ea4-4cb3-a893-09ba5416b371" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ed73d661-31f4-564a-3bca-e7bd1da12bc7" executionId="bc4fe66c-e692-4924-b447-01bba7076734" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="94919b74-043c-ac5d-87f1-0587b8f46ef7" executionId="fee3bd86-4716-4cc1-a83d-00af277a730a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f0dd822e-110a-9423-89f0-2a6bc0510f84" executionId="97609364-6292-4b3b-a74e-f8a037cdb599" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f543d2e9-4ced-9d03-4307-68823ad67ec5" executionId="9e7e2dad-9c0d-4f34-a021-d8aa4a0b7d0e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="97ad4d87-64e8-3d1f-2000-33d34d4e461d" executionId="9c5b6496-637b-4108-9012-ce25baaa7be4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="65c4cae6-02b9-cdd9-6a8b-d3eb8e0242f1" executionId="00d1e193-5c71-472f-823b-b604e66296de" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="29cc443b-5843-75d0-329d-fdd89478ba0f" executionId="7f884700-60c8-4cf8-95bb-622a7a8c645d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d095da48-d2a1-b869-306b-68ec74fb6a86" executionId="f2ce9005-8196-4d7c-8ca3-f474a179af06" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a094d57c-350b-c7d9-7ef4-ab145f0776e7" executionId="de433dcd-76fa-456e-bd56-57452700faa5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8927fc38-5352-5a92-ac17-c0b53db402c5" executionId="d0aff8ce-c47d-4fff-8fe7-c8508af4f8e0" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="54219804-c561-b035-0838-e2a41e644606" executionId="9461cb61-7b4e-4252-8b9c-7f228984a49d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f4bd691f-6e1a-eb6a-8014-5eb6cc5c57a1" executionId="f3db0982-ac00-4527-997c-3218340b3a96" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c4b6fa98-2242-c1ba-efc6-ec174c1be4e2" executionId="8ad02b69-2acb-4bb9-9d4b-029400de496e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c8b6c16e-78a6-4e53-1410-df1171a01492" executionId="75c8fa63-4855-4020-9cb0-61e1208b5e71" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="26c01f72-2f55-6301-3660-bbcccfc220bf" executionId="69fd9dc6-fe48-41cc-bdf1-ba14ddbb3ab4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d205cea9-26a0-cb1a-deb8-bd301be91a7a" executionId="7d4c6c9f-117c-4303-890a-e2a50da69a57" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d554c12b-5ed2-23ff-fe49-9271ab3c4030" executionId="633947d2-46e3-4688-b6a7-73eddaae7a63" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d0e94aff-761a-b752-8ea1-b05e263e6baa" executionId="d969b723-3ac6-4818-84e4-82c7a2c98252" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d72a782f-3fee-0055-ced6-0e57470253f9" executionId="abf3202c-0ac5-4350-96ed-fb34e2a9d4fc" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="90a5f1aa-f90c-1c7c-8cfb-a59b5defb741" executionId="f7bb6d1f-b688-4638-a823-7a43f067dfef" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="374deec9-2627-2caa-434b-b1767297fddb" executionId="19cc99e3-1ae7-4bd9-b6e8-e4bfaef0151d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2660b25c-0ab3-7226-1818-3de3d93f7e8b" executionId="bdc4ed34-3a31-4608-9948-b56c9848e631" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e5f24540-0924-e65b-6f16-4ab793a71012" executionId="c8fe6d4e-00f1-4538-b4a9-9f88f4a05583" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cf59dfeb-fc64-c423-a638-29288238b6c4" executionId="ab609544-db7d-4ac8-8d0a-1bea29de6800" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bdd00514-c674-7fbe-6276-f883dc5e0af9" executionId="2e9652e0-70c1-4385-891b-ea03cf69e362" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4910faa5-5592-c319-7394-2de698b2c0c7" executionId="564651da-3d9f-44b9-8a42-61622853b193" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5bacd699-c1c3-a30f-c688-788c48f5fa12" executionId="cafda20a-444f-4357-8268-2dc3ada236ab" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4593fd1a-0307-349e-dd30-e0a7cd2c3a3b" executionId="07468682-eaaa-4e99-bb81-b57d43ab62b8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="37b241c9-a28c-3630-63d5-22833c81cd74" executionId="4745fcd7-09a8-41eb-93da-33fb819d36d7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="66753aa0-12f4-9a94-0af2-e1d64e8dae4a" executionId="04fc1006-3b5f-4835-9687-a8fe256b8f29" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5de9e3f2-f5fe-88a8-b3fb-d72657ad04d9" executionId="69ca79bf-cb37-41a1-a1fc-25cd47290b3e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4ba3dac7-6a62-cb49-022a-20a7d4d37087" executionId="c60870e9-6add-488a-aa11-e5cbd33e4614" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="586a2248-3398-cdf2-2aaf-e284666f9fc1" executionId="ad925acd-d884-45a7-b573-b51f3f925dd3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f758c623-141e-3413-11d2-f683cfe5a47c" executionId="c47bf3f4-5bc7-48b3-b091-5763827bd1a6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="387aec6f-b8f3-0719-a44f-14efd8e3f6b0" executionId="c8f1629c-da0d-402f-8559-6ac0661e286d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="80209ebf-9964-6643-698c-8cc7c39137a7" executionId="2b018ead-2d18-4cde-aee1-f2497f0113ed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1a863009-7253-b831-45ba-4599991a1e23" executionId="8e5b263e-1864-4058-996d-f986d4c253f9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d796aa36-66a9-9e1e-199e-e7f8579e7312" executionId="6dd4e648-2966-41af-a3a3-db37cde3f8f5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c18a85de-caa6-f92b-dd42-c491eddd8d5a" executionId="5ef0d7be-d0e5-44b0-8050-63b4542daed9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cd2d846a-4253-c669-9172-ae3f3e9c95ea" executionId="117930f2-8516-4ca5-8460-3c76c470a3a3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="44298843-f215-2386-e136-f2a25ffedf0e" executionId="f01c165e-a7d9-4565-9fc0-075062972101" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b531b8c7-1b6c-8c36-b996-64028f4f0324" executionId="ecd4c796-ce68-409f-b9f7-9ee437ba0e94" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="746694ab-c99f-c4df-9347-d40ac323bdff" executionId="db67cd9a-2c8f-4377-a242-0f82dc416dd7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d030f382-42f0-6302-7045-2cfd01c44112" executionId="91121044-df15-4780-b551-52488a255331" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a2862178-237f-01ca-e3a5-f47ceb7b9484" executionId="224ed723-bbab-4960-91f9-b496d43d0b11" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c828ecfa-33bf-6434-f923-b9cfd77032bc" executionId="beea40c7-8e6e-4901-bbc6-51908b27b734" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c4f03241-cdba-e70d-96b7-1a09d3cf8bfb" executionId="3708c74d-0813-4d22-ab37-7e7151a009ea" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="69868bb1-a9df-9b47-1d6a-43a9bb280b16" executionId="9060b876-36ec-4786-90f1-3e41f5e29b13" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9510bd6c-2b16-38c6-12a7-a2ae238d0e20" executionId="51b6e7cb-f0e4-44e3-a338-66017e7d0033" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="06f82529-1ce7-16b1-604c-9f0392f7d160" executionId="5a95d898-f888-487f-827f-0e0f78395c21" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="57f89734-de5a-0489-86e3-f364df300ca2" executionId="e11f5824-6b84-44d0-a5f0-4ab5316d014b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="99219d16-4665-2168-0835-d11459e2febf" executionId="5ad729f5-378c-4426-bee7-2df899f6b4ae" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
  </TestEntries>
  <TestLists>
    <TestList name="列表中未列出的结果" id="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestList name="所有已加载的结果" id="19431567-8539-422a-85d7-44ee4e166bda" />
  </TestLists>
  <ResultSummary outcome="Completed">
    <Counters total="260" executed="260" passed="260" failed="0" error="0" timeout="0" aborted="0" inconclusive="0" passedButRunAborted="0" notRunnable="0" notExecuted="0" disconnected="0" warning="0" completed="0" inProgress="0" pending="0" />
    <Output>
      <StdOut>[xUnit.net 00:00:00.00] xUnit.net VSTest Adapter v2.4.5+1caef2f33e (64-bit .NET 8.0.17)&#xD;
[xUnit.net 00:00:00.31]   Discovering: Alicres.SerialPort.Tests&#xD;
[xUnit.net 00:00:00.36]   Discovered:  Alicres.SerialPort.Tests&#xD;
[xUnit.net 00:00:00.36]   Starting:    Alicres.SerialPort.Tests&#xD;
�����ݴ���: 100,000 �ֽ�, ��ʱ: 0ms&#xD;
�����ݴ���: 10,000 �ֽ�, ��ʱ: 0ms&#xD;
�����ݴ���: 1,000 �ֽ�, ��ʱ: 0ms&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 ״̬�仯: Connecting -&gt; Connected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST �\udabe\udeb3ɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 ��������: 4096 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ��������: 13 �ֽ�&#xD;
fail: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST ��������: Frame&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 �رճɹ�&#xD;
CanSend���ܲ���: 10000 �ε���, ƽ�� 0.0001ms/��&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST ״̬�仯: Error -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 ��������: 100 �ֽ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST �رճɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ��������: 48 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ��������: 19 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ��������: 26 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �رճɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      ����Mock���ڷ���: COM_MGR_1&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      ����Mock���ڷ���: COM_MGR_2&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ��������: 131072 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW ��������: 2048 �ֽ�&#xD;
=== ���ܱ���: �����ݰ�������� (128KB) ===&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      �����\udabe\udeb4�����ɣ��ɹ�: 2/2&#xD;
���ݴ�С: 131,072 �ֽ�&#xD;
����ʱ��: 0.18 ����&#xD;
������: 710032502.71 �ֽ�/�� (693391.12 KB/s)&#xD;
========================&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 ��������: 22 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 ��������: 22 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA �\udabe\udeb3ɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      �㲥������ɣ��ɹ�: 2/2, ���ݳ���: 22&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ��������: 262144 �ֽ�&#xD;
=== ���ܱ���: �����ݰ�������� (256KB) ===&#xD;
���ݴ�С: 262,144 �ֽ�&#xD;
����ʱ��: 0.17 ����&#xD;
������: 1546572271.39 �ֽ�/�� (1510324.48 KB/s)&#xD;
========================&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ��������: 65536 �ֽ�&#xD;
=== ���ܱ���: �����ݰ�������� (64KB) ===&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 ״̬�仯: Connected -&gt; Disconnecting&#xD;
���ݴ�С: 65,536 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 �رճɹ�&#xD;
����ʱ��: 0.09 ����&#xD;
������: 729799554.57 �ֽ�/�� (712694.88 KB/s)&#xD;
========================&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      �Ƴ����ڷ���: COM_MGR_2&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 �رճɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      �����رմ�����ɣ��ɹ�: 1/1&#xD;
warn: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 �Ѿ��ر�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      �����رմ�����ɣ��ɹ�: 1/1&#xD;
warn: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 �Ѿ��ر�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      �����رմ�����ɣ��ɹ�: 1/1&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      Mock���ڹ��������ͷ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 1 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 1 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 2 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 2 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connecting -&gt; Connected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST �\udabe\udeb3ɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 12 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 12 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST ��������: 50 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 1 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 1 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ��������: 20 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ��������: 20 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 12 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ��������: 20 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ��������: 20 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ��������: 20 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 12 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 3 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 3 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED ��������: 2048 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST ��������: 36 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �رճɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���������Ѹ���: COM_CONFIG_UPDATE&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONFIG_UPDATE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONFIG_UPDATE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONFIG_UPDATE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONFIG_UPDATE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONFIG_UPDATE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONFIG_UPDATE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ��������: 2 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ��������: 1 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 4096 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 4097 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 512 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 513 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 65536 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 1 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 2 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NULL_EMPTY ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NULL_EMPTY ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NULL_EMPTY �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NULL_EMPTY ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NULL_EMPTY ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NULL_EMPTY �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM999 ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM999 ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM999 �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM999 ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM999 ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM999 �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� INVALID ״̬�仯: Disconnected -&gt; Connecting&#xD;
fail: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      ��Mock���� INVALID ʧ��: �޷����ʴ��� INVALID&#xD;
      System.UnauthorizedAccessException: �޷����ʴ��� INVALID&#xD;
         at Alicres.SerialPort.Tests.TestHelpers.MockSerialPort.Open() in G:\Alicres\tests\Alicres.SerialPort.Tests\TestHelpers\MockSerialPort.cs:line 115&#xD;
         at Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService.&lt;OpenAsync&gt;b__29_0() in G:\Alicres\tests\Alicres.SerialPort.Tests\TestHelpers\MockSerialPortService.cs:line 149&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� INVALID ״̬�仯: Error -&gt; Disconnected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE �رճɹ�&#xD;
�߸��ز��Խ��: 534817 ����/��, ���г���: 10&#xD;
���ܲ��Խ��: 267222 ����/��, �ɹ���: 100.00%&#xD;
ͳ����Ϣ����: 1000 �ε���, ƽ�� 0.003ms/��&#xD;
�ڴ�ʹ�����: ��ʼ 3,119,104 �ֽ�, ���� 34,376,176 �ֽ�, ���� 31,257,072 �ֽ�&#xD;
[xUnit.net 00:00:02.32]   Finished:    Alicres.SerialPort.Tests&#xD;
</StdOut>
    </Output>
    <CollectorDataEntries>
      <Collector agentName="JD-ITA028088-PC" uri="datacollector://microsoft/CoverletCodeCoverage/1.0" collectorDisplayName="XPlat code coverage">
        <UriAttachments>
          <UriAttachment>
            <A href="JD-ITA028088-PC\coverage.cobertura.xml"></A>
          </UriAttachment>
        </UriAttachments>
      </Collector>
    </CollectorDataEntries>
  </ResultSummary>
</TestRun>